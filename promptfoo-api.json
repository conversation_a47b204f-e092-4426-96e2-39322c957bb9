{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "Promptfoo API", "description": "Promptfoo API provides programmatic access to promptfoo's evaluation and testing platform for large language models. This documentation covers all available endpoints and authentication methods. This API is currently only available for Enterprise users. For support, contact <EMAIL>."}, "servers": [{"url": "https://api.promptfoo.app"}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Enter JWT token in the format \"Bearer {token}\""}}, "schemas": {"IssueDTO": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "pluginId": {}, "weakness": {"type": "string"}, "status": {"type": "string", "enum": ["open", "ignored", "false_positive", "fixed"]}, "organizationId": {"type": "string"}, "severity": {"type": "string", "enum": ["critical", "high", "medium", "low"]}, "targetId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}, "teamId": {"type": "string"}, "occurrences": {"type": "integer", "minimum": 0}, "history": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string"}, "name": {"type": "string"}, "text": {"type": "string", "minLength": 1, "maxLength": 5000}, "data": {"nullable": true}, "type": {"type": "string", "enum": ["comment_added", "created", "failed_tests_added", "failed_tests_removed", "severity_changed", "status_changed", "updated"]}}, "required": ["createdAt", "name", "text", "type"]}}}, "required": ["id", "createdAt", "updatedAt", "pluginId", "weakness", "status", "organizationId", "severity", "targetId", "providerId", "teamId", "occurrences", "history"]}, "IssueHistoryDTO": {"type": "object", "properties": {"createdAt": {"type": "string"}, "name": {"type": "string"}, "text": {"type": "string", "minLength": 1, "maxLength": 5000}, "data": {"nullable": true}, "type": {"type": "string", "enum": ["comment_added", "created", "failed_tests_added", "failed_tests_removed", "severity_changed", "status_changed", "updated"]}}, "required": ["createdAt", "name", "text", "type"]}, "PatchIssueDTO": {"type": "object", "properties": {"severity": {"type": "string", "enum": ["critical", "high", "medium", "low"]}, "status": {"type": "string", "enum": ["open", "ignored", "false_positive", "fixed"]}, "teamId": {"type": "string"}}}, "RemediationDTO": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "remediation": {"type": "string"}, "genericDescription": {"type": "string"}}, "required": ["id", "remediation", "genericDescription"]}, "IssueListResponse": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "pluginId": {}, "weakness": {"type": "string"}, "status": {"type": "string", "enum": ["open", "ignored", "false_positive", "fixed"]}, "organizationId": {"type": "string"}, "severity": {"type": "string", "enum": ["critical", "high", "medium", "low"]}, "targetId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}, "teamId": {"type": "string"}, "occurrences": {"type": "integer", "minimum": 0}, "history": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string"}, "name": {"type": "string"}, "text": {"type": "string", "minLength": 1, "maxLength": 5000}, "data": {"nullable": true}, "type": {"type": "string", "enum": ["comment_added", "created", "failed_tests_added", "failed_tests_removed", "severity_changed", "status_changed", "updated"]}}, "required": ["createdAt", "name", "text", "type"]}}}, "required": ["id", "createdAt", "updatedAt", "pluginId", "weakness", "status", "organizationId", "severity", "targetId", "providerId", "teamId", "occurrences", "history"]}}, "ProviderListResponse": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}}, "CommentRequest": {"type": "object", "properties": {"comment": {"type": "string", "minLength": 1, "maxLength": 5000}}, "required": ["comment"]}, "Job": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "JobListResponse": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}}}, "required": ["jobs"]}, "StartJob": {"allOf": [{"type": "object", "properties": {"config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "teamId": {"type": "string"}, "evalOnly": {"type": "boolean"}, "originalEvalId": {"type": "string"}, "subsetOptions": {"type": "object", "properties": {"subsetType": {"type": "string"}, "subsetName": {"type": "string"}}, "required": ["subsetType"]}}, "required": ["teamId"]}, {"nullable": true}]}, "SSEMessage": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "enum": ["log"]}, "message": {"type": "string"}}, "required": ["type", "message"]}, {"type": "object", "properties": {"type": {"type": "string", "enum": ["progress"]}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}}, "required": ["type", "progress"]}, {"type": "object", "properties": {"type": {"type": "string", "enum": ["completion"]}}, "required": ["type"]}]}, "JobsQueryParams": {"type": "object", "properties": {"limit": {"type": "string"}, "offset": {"type": "string"}}}, "Organization": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "canUseRedteam": {"type": "boolean"}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}, "idpEnabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string", "nullable": true}}, "required": ["id", "name", "canUseRedteam", "excludeTargetOutputFromAgenticAttackGeneration", "idpEnabled", "createdAt", "updatedAt"]}, "UpdateOrganization": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}}, "required": ["name"]}, "AddUserToOrganization": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "name": {"type": "string"}}, "required": ["email"]}, "SetUserAdminStatus": {"type": "object", "properties": {"isAdmin": {"type": "boolean"}}, "required": ["isAdmin"]}, "SetUserAdminStatusResponse": {"type": "object", "properties": {"isAdmin": {"type": "boolean"}}, "required": ["isAdmin"]}, "CreateServiceAccount": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}}, "required": ["name"]}, "ServiceAccount": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "token": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "name", "token", "createdAt"]}, "ServiceAccountListItem": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "createdAt": {"type": "string"}, "isAdmin": {"type": "boolean"}, "teams": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}}}, "required": ["id", "name", "createdAt", "isAdmin", "teams"]}, "PluginCollection": {"type": "object", "properties": {"id": {"type": "string"}, "plugins": {"type": "array", "items": {"nullable": true}}, "organizationId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "deletedAt": {"type": "string", "nullable": true}, "version": {"type": "number"}, "user": {"type": "string"}, "description": {"type": "string", "nullable": true}, "name": {"type": "string"}, "teamId": {"type": "string"}}, "required": ["id", "plugins", "organizationId", "userId", "createdAt", "updatedAt", "deletedAt", "version", "description", "name", "teamId"]}, "PluginCollectionList": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "plugins": {"type": "array", "items": {"nullable": true}}, "organizationId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "deletedAt": {"type": "string", "nullable": true}, "version": {"type": "number"}, "user": {"type": "string"}, "description": {"type": "string", "nullable": true}, "name": {"type": "string"}, "teamId": {"type": "string"}}, "required": ["id", "plugins", "organizationId", "userId", "createdAt", "updatedAt", "deletedAt", "version", "description", "name", "teamId"]}}, "CreatePluginCollection": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string", "nullable": true}, "plugins": {"type": "array", "items": {"nullable": true}, "minItems": 1}, "teamId": {"type": "string"}}, "required": ["name", "plugins", "teamId"]}, "UpdatePluginCollection": {"type": "object", "properties": {"plugins": {"type": "array", "items": {"nullable": true}}, "name": {"type": "string"}, "description": {"type": "string"}, "teamId": {"type": "string"}}}, "PluginCollectionErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "configNames": {"type": "array", "items": {"type": "string"}}}, "required": ["error"]}, "Provider": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "config": {"nullable": true}, "applicationDescription": {"nullable": true}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "deletedAt": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}, "required": ["id", "name", "organizationId", "teamId", "created<PERSON>y", "updatedBy", "createdAt", "updatedAt", "deletedAt"]}, "CreateProvider": {"type": "object", "properties": {"name": {"type": "string"}, "config": {"type": "object", "additionalProperties": {"nullable": true}}, "applicationDescription": {"type": "object", "properties": {"purpose": {"type": "string"}, "systemPrompt": {"type": "string"}, "redteamUser": {"type": "string"}, "accessToData": {"type": "string"}, "forbiddenData": {"type": "string"}, "accessToActions": {"type": "string"}, "forbiddenActions": {"type": "string"}, "connectedSystems": {"type": "string"}}}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "teamId": {"type": "string"}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}, "required": ["name", "config", "teamId"]}, "UpdateProvider": {"type": "object", "properties": {"name": {"type": "string"}, "config": {"type": "object", "additionalProperties": {"nullable": true}}, "applicationDescription": {"type": "object", "properties": {"purpose": {"type": "string"}, "systemPrompt": {"type": "string"}, "redteamUser": {"type": "string"}, "accessToData": {"type": "string"}, "forbiddenData": {"type": "string"}, "accessToActions": {"type": "string"}, "forbiddenActions": {"type": "string"}, "connectedSystems": {"type": "string"}}}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "teamId": {"type": "string"}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}}, "HttpProviderSetupRequest": {"type": "object", "properties": {"config": {"type": "object", "additionalProperties": {"nullable": true}}, "providerResponse": {"nullable": true}, "parsedResponse": {"nullable": true}, "error": {"nullable": true}}, "required": ["config"]}, "ProviderTestResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}, "ProviderDeleteError": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "array", "items": {"type": "string"}}}, "required": ["error"]}, "SessionSource": {"type": "string", "enum": ["server", "client"]}, "TestProvider": {"type": "object", "properties": {"provider": {"type": "object", "properties": {"config": {"type": "object", "additionalProperties": {"nullable": true}}}, "required": ["config"]}, "type": {"type": "string"}}, "required": ["provider", "type"]}, "GetByIdProviderOptions": {"type": "object", "properties": {"includeDeleted": {"type": "boolean"}}}, "ConsolidatedRemediation": {"type": "object", "properties": {"targetId": {"type": "string"}, "targetName": {"type": "string"}, "remediation": {"type": "string"}}, "required": ["targetId", "targetName", "remediation"]}, "GenerateConsolidatedRemediation": {"type": "object", "properties": {"forceRefresh": {"type": "boolean"}}}, "Config": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "config": {"nullable": true}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "pluginCollectionId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "user": {"type": "string"}}, "required": ["id", "name", "type", "organizationId", "teamId", "userId", "createdAt"]}, "ConfigListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "config": {"nullable": true}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "pluginCollectionId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "user": {"type": "string"}}, "required": ["id", "name", "type", "organizationId", "teamId", "userId", "createdAt"]}}}, "required": ["data"]}, "CreateConfig": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string", "nullable": true}, "config": {"nullable": true}, "teamId": {"type": "string"}, "pluginCollectionId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}}, "required": ["name", "teamId"]}, "UpdateConfig": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string", "nullable": true}, "config": {"nullable": true}, "teamId": {"type": "string"}, "pluginCollectionId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}}}, "RedteamConfig": {"type": "object", "properties": {"id": {"type": "string"}, "sharing": {"type": "boolean"}, "description": {"type": "string"}, "prompts": {"type": "array", "items": {"type": "string"}}, "target": {"nullable": true}, "plugins": {"type": "array", "items": {"anyOf": [{"nullable": true}, {"type": "object", "properties": {"id": {"type": "string"}, "config": {"nullable": true}}, "required": ["id"]}, {"nullable": true}]}}, "pluginCollectionId": {"type": "string"}, "strategies": {"type": "array", "items": {"nullable": true}}, "purpose": {"type": "string"}, "numTests": {"type": "number"}, "extensions": {"type": "array", "items": {"type": "string"}}, "applicationDefinition": {"nullable": true}, "entities": {"type": "array", "items": {"type": "string"}}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "userId": {"type": "string"}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "runOptions": {"nullable": true}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}}, "required": ["description", "prompts", "plugins", "strategies", "entities"]}, "Eval": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "EvalSummary": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "provider": {"type": "string"}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "resultsCount": {"type": "number"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["id", "createdAt", "updatedAt", "name", "organizationId", "status"]}, "EvalListResponse": {"type": "object", "properties": {"evals": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "provider": {"type": "string"}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "resultsCount": {"type": "number"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["id", "createdAt", "updatedAt", "name", "organizationId", "status"]}}, "total": {"type": "number"}}, "required": ["evals", "total"]}, "EvalResult": {"type": "object", "properties": {"id": {"type": "string"}, "evalId": {"type": "string"}, "prompt": {"type": "string"}, "response": {"type": "string"}, "metrics": {"type": "object", "additionalProperties": {"nullable": true}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "evalId", "prompt", "response", "createdAt", "updatedAt"]}, "Issue": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "pluginId": {}, "weakness": {"type": "string"}, "status": {"type": "string", "enum": ["open", "ignored", "false_positive", "fixed"]}, "organizationId": {"type": "string"}, "severity": {"type": "string", "enum": ["critical", "high", "medium", "low"]}, "targetId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}, "teamId": {"type": "string"}, "occurrences": {"type": "integer", "minimum": 0}, "history": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string"}, "name": {"type": "string"}, "text": {"type": "string", "minLength": 1, "maxLength": 5000}, "data": {"nullable": true}, "type": {"type": "string", "enum": ["comment_added", "created", "failed_tests_added", "failed_tests_removed", "severity_changed", "status_changed", "updated"]}}, "required": ["createdAt", "name", "text", "type"]}}}, "required": ["id", "createdAt", "updatedAt", "pluginId", "weakness", "status", "organizationId", "severity", "targetId", "providerId", "teamId", "occurrences", "history"]}, "EvalIdParam": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"]}, "ResultIdParam": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "resultsId": {"type": "string", "format": "uuid"}}, "required": ["id", "resultsId"]}, "EvalResultIdParam": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"]}, "UpdateEval": {"type": "object", "properties": {"config": {"nullable": true}, "isPublic": {"type": "boolean"}}}, "Results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "prompt": {"nullable": true}, "provider": {"nullable": true}, "vars": {"type": "object", "additionalProperties": {"nullable": true}}, "output": {"nullable": true}, "latencyMs": {"type": "number"}, "success": {"type": "boolean"}, "error": {"type": "string"}, "gradingResult": {"nullable": true}}}}, "GradeResult": {"type": "object", "properties": {"result": {"type": "string"}, "score": {"type": "number", "minimum": 0, "maximum": 1}, "reasonsHtml": {"type": "string"}, "reasons": {"type": "array", "items": {"type": "string"}}}, "required": ["result"]}, "CreateRole": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}}, "required": ["name", "permissionSets"]}, "UpdateRole": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}}, "required": ["name", "permissionSets"]}, "Role": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "permissionSets", "createdAt", "updatedAt"]}, "OrganizationUsage": {"type": "object", "properties": {"id": {"type": "string"}, "targets": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["CLOUD", "CLI"]}, "status": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "createdAt": {"type": "string"}, "usage": {"type": "number"}, "team": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}}, "required": ["id", "name", "type", "status", "createdAt", "usage", "team"]}}}, "required": ["id", "targets"]}, "CreateTeam": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}}, "required": ["name"]}, "UpdateTeam": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}}}, "AddTeamMember": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}}, "required": ["userId", "roleId"]}, "UpdateTeamMemberRole": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid"}}, "required": ["roleId"]}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "accountType": {"type": "string", "enum": ["user", "service"]}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "isAdmin": {"type": "boolean"}}, "required": ["id", "name", "email", "accountType", "createdAt", "updatedAt"]}, "Me": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "accountType": {"type": "string", "enum": ["user", "service"]}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "isAdmin": {"type": "boolean"}}, "required": ["id", "name", "email", "accountType", "createdAt", "updatedAt"]}, "organization": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "canUseRedteam": {"type": "boolean"}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}, "idpEnabled": {"type": "boolean"}}, "required": ["id", "name", "canUseRedteam", "excludeTargetOutputFromAgenticAttackGeneration", "idpEnabled"]}, "app": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"]}, "isAdmin": {"type": "boolean"}}, "required": ["user", "app", "isAdmin"]}, "RequestPasswordReset": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}}, "required": ["email"]}, "Team": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "members": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "accountType": {"type": "string", "enum": ["user", "service"]}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "isAdmin": {"type": "boolean"}, "role": {"type": "object", "nullable": true, "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "permissionSets", "createdAt", "updatedAt"]}}, "required": ["id", "name", "email", "accountType", "createdAt", "updatedAt", "role"]}}}, "required": ["id", "name", "organizationId", "createdAt", "updatedAt", "members"]}, "UserAbility": {"type": "object", "properties": {"action": {"type": "string"}, "subject": {"type": "string"}, "conditions": {"type": "object", "additionalProperties": {"nullable": true}}, "inverted": {"type": "boolean"}, "reason": {"type": "string"}}, "required": ["action", "subject"]}, "UserStatus": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok", "exceeded_limit", "show_usage_warning"]}, "error": {"type": "string"}, "message": {"type": "string"}}, "required": ["status"]}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string"}, "organizationId": {"type": "string"}}, "required": ["email"]}, "Webhook": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}, "CreateWebhook": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "enabled": {"type": "boolean"}}, "required": ["name", "events", "url"]}, "UpdateWebhook": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "enabled": {"type": "boolean"}}}, "WebhookListResponse": {"type": "object", "properties": {"webhooks": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}}}, "required": ["webhooks"]}, "WebhookResponse": {"type": "object", "properties": {"webhook": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}}, "required": ["webhook"]}, "WebhookEventTypesResponse": {"type": "object", "properties": {"eventTypes": {"type": "array", "items": {"type": "string"}}}, "required": ["eventTypes"]}, "GradingExample": {"type": "object", "properties": {"id": {"type": "string"}, "pluginId": {"type": "string"}, "pass": {"type": "boolean"}, "score": {"type": "integer"}, "reason": {"type": "string"}, "output": {"type": "string"}, "teamId": {"type": "string"}, "providerId": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["pass", "score", "reason", "output", "teamId", "providerId"]}, "CreateGradingExample": {"type": "object", "properties": {"pluginId": {"type": "string"}, "pass": {"type": "boolean"}, "score": {"type": "integer"}, "reason": {"type": "string"}, "output": {"type": "string"}, "teamId": {"type": "string"}, "providerId": {"type": "string"}}, "required": ["pass", "score", "reason", "output", "teamId", "providerId"]}, "UpdateGradingExample": {"type": "object", "properties": {"id": {"type": "string"}, "pluginId": {"type": "string"}, "pass": {"type": "boolean"}, "score": {"type": "integer"}, "reason": {"type": "string"}, "output": {"type": "string"}, "teamId": {"type": "string"}, "providerId": {"type": "string"}}}, "GradingExampleListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "pluginId": {"type": "string"}, "pass": {"type": "boolean"}, "score": {"type": "integer"}, "reason": {"type": "string"}, "output": {"type": "string"}, "teamId": {"type": "string"}, "providerId": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["pass", "score", "reason", "output", "teamId", "providerId"]}}}, "required": ["data"]}, "AuditLog": {"type": "object", "properties": {"id": {"type": "string"}, "description": {"type": "string"}, "actorId": {"type": "string", "nullable": true}, "actorName": {"type": "string", "nullable": true}, "actorEmail": {"type": "string", "nullable": true}, "action": {"type": "string"}, "actionDisplayName": {"type": "string"}, "target": {"type": "string"}, "targetId": {"type": "string", "nullable": true}, "metadata": {"type": "object", "nullable": true, "additionalProperties": {"nullable": true}}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "createdAt": {"type": "string"}}, "required": ["id", "description", "actorId", "<PERSON><PERSON><PERSON>", "actor<PERSON><PERSON>", "action", "actionDisplayName", "target", "targetId", "metadata", "organizationId", "teamId", "createdAt"]}, "AuditLogResponse": {"type": "object", "properties": {"total": {"type": "number"}, "limit": {"type": "number"}, "offset": {"type": "number"}, "logs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "description": {"type": "string"}, "actorId": {"type": "string", "nullable": true}, "actorName": {"type": "string", "nullable": true}, "actorEmail": {"type": "string", "nullable": true}, "action": {"type": "string"}, "actionDisplayName": {"type": "string"}, "target": {"type": "string"}, "targetId": {"type": "string", "nullable": true}, "metadata": {"type": "object", "nullable": true, "additionalProperties": {"nullable": true}}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "createdAt": {"type": "string"}}, "required": ["id", "description", "actorId", "<PERSON><PERSON><PERSON>", "actor<PERSON><PERSON>", "action", "actionDisplayName", "target", "targetId", "metadata", "organizationId", "teamId", "createdAt"]}}}, "required": ["total", "limit", "offset", "logs"]}, "AuditLogQueryParams": {"type": "object", "properties": {"limit": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}, "offset": {"type": "integer", "nullable": true, "minimum": 0, "default": 0}, "createdAtGte": {"type": "string"}, "createdAtLte": {"type": "string"}, "action": {"type": "string", "enum": ["login", "user_added", "user_removed", "role_created", "role_deleted", "role_updated", "team_created", "team_deleted", "user_added_to_team", "user_removed_from_team", "user_role_changed_in_team", "org_admin_added", "org_admin_removed", "service_account_created", "service_account_deleted"]}, "target": {"type": "string", "enum": ["user", "role", "team", "organization", "service_account"]}, "actorId": {"type": "string"}}}, "AuditAction": {"type": "string", "enum": ["login", "user_added", "user_removed", "role_created", "role_deleted", "role_updated", "team_created", "team_deleted", "user_added_to_team", "user_removed_from_team", "user_role_changed_in_team", "org_admin_added", "org_admin_removed", "service_account_created", "service_account_deleted"]}, "AuditTarget": {"type": "string", "enum": ["user", "role", "team", "organization", "service_account"]}}, "parameters": {}}, "paths": {"/health": {"get": {"description": "System health check endpoint", "responses": {"200": {"description": "Server is healthy and running", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}, "required": ["status"]}, "example": {"status": "OK"}}}}}}}, "/version": {"get": {"description": "Get server version information", "responses": {"200": {"description": "Version information retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"version": {"type": "string", "description": "Package version from package.json"}, "buildDate": {"type": "string", "description": "Date when the server was built"}, "gitSha": {"type": "string", "description": "Git commit SHA of the current build"}}, "required": ["version", "buildDate", "git<PERSON><PERSON>"]}, "example": {"version": "1.2.3", "buildDate": "2024-03-27T14:30:00Z", "gitSha": "abc123def456"}}}}}}}, "/api/v1/issues/{id}/remediate": {"post": {"description": "Generate a remediation for an issue", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the issue to remediate"}], "security": [{"BearerAuth": []}], "responses": {"204": {"description": "Request accepted, remediation will be generated asynchronously"}}}}, "/api/v1/issues": {"get": {"description": "Get all issues for the organization", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "List of issues retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueListResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/v1/issues/providers": {"get": {"description": "Get all providers for the organization", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "List of providers retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProviderListResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/v1/issues/{id}": {"get": {"description": "Get an issue by ID", "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the issue to retrieve"}], "responses": {"200": {"description": "Issue retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueDTO"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "404": {"description": "Issue not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}, "patch": {"description": "Update an issue", "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the issue to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchIssueDTO"}}}}, "responses": {"200": {"description": "Issue updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueDTO"}}}}, "400": {"description": "Invalid request body", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "404": {"description": "Issue or team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/v1/issues/{id}/evalResults": {"get": {"description": "Get evaluation results for an issue", "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the issue"}], "responses": {"200": {"description": "Evaluation results retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": true}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "404": {"description": "Issue not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/v1/issues/{id}/comment": {"post": {"description": "Add a comment to an issue", "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the issue"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentRequest"}}}}, "responses": {"200": {"description": "Comment added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueHistoryDTO"}}}}, "400": {"description": "Invalid comment", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "additionalProperties": true}}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "404": {"description": "Issue not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/v1/issues/{id}/remediation": {"get": {"description": "Get remediation for an issue", "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the issue"}], "responses": {"200": {"description": "Remediation retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemediationDTO"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "404": {"description": "Issue not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/v1/jobs": {"get": {"description": "Get all jobs for the authenticated user. Returns a paginated list of evaluation jobs.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": false, "name": "limit", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "offset", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved jobs", "content": {"application/json": {"schema": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}}}, "required": ["jobs"]}, "example": {"jobs": [{"id": "job_123", "status": "running", "config": {"name": "Customer Service Evaluation", "description": "Evaluate customer service responses", "prompts": ["How can I help you today?"], "assertions": ["response is helpful", "response is polite"]}, "progress": {"total": 100, "completed": 45, "current": "Evaluating response 45/100"}, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z"}]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching jobs"}}}}}}, "post": {"description": "Start a new evaluation job. Creates and starts a new job with the specified configuration.", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "teamId": {"type": "string"}, "evalOnly": {"type": "boolean"}, "originalEvalId": {"type": "string"}, "subsetOptions": {"type": "object", "properties": {"subsetType": {"type": "string"}, "subsetName": {"type": "string"}}, "required": ["subsetType"]}}, "required": ["teamId"]}, {"nullable": true}]}, "example": {"config": {"name": "Customer Service Evaluation", "description": "Evaluate customer service responses", "prompts": ["How can I help you today?"], "assertions": ["response is helpful", "response is polite"]}, "teamId": "team_123", "force": false, "verbose": true, "delay": 0, "configId": "config_456"}}}}, "responses": {"200": {"description": "Job started successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "example": {"id": "job_123", "status": "running", "config": {"name": "Customer Service Evaluation", "description": "Evaluate customer service responses", "prompts": ["How can I help you today?"], "assertions": ["response is helpful", "response is polite"]}, "progress": {"total": 100, "completed": 0, "current": "Initializing job"}, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while starting job"}}}}}}}, "/api/v1/jobs/{jobId}": {"get": {"description": "Get a specific job by ID. Returns detailed information about a single evaluation job.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the job to retrieve"}, "required": true, "description": "The ID of the job to retrieve", "name": "jobId", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved job", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "example": {"id": "job_123", "status": "running", "config": {"name": "Customer Service Evaluation", "description": "Evaluate customer service responses", "prompts": ["How can I help you today?"], "assertions": ["response is helpful", "response is polite"]}, "progress": {"total": 100, "completed": 45, "current": "Evaluating response 45/100"}, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Job not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Job not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching job"}}}}}}}, "/api/v1/jobs/{jobId}/logs": {"get": {"description": "Get real-time logs for a job. Returns a server-sent events stream of job logs, progress updates, and completion status.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the job to get logs for"}, "required": true, "description": "The ID of the job to get logs for", "name": "jobId", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved job logs", "content": {"text/event-stream": {"schema": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "enum": ["log"]}, "message": {"type": "string"}}, "required": ["type", "message"]}, {"type": "object", "properties": {"type": {"type": "string", "enum": ["progress"]}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}}, "required": ["type", "progress"]}, {"type": "object", "properties": {"type": {"type": "string", "enum": ["completion"]}}, "required": ["type"]}]}, "example": {"type": "log", "message": "Starting evaluation of prompt 1/100"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Job not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Job not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching job logs"}}}}}}}, "/api/v1/jobs/{jobId}/stop": {"post": {"description": "Stop a running job. Attempts to gracefully stop a job that is currently running.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the job to stop"}, "required": true, "description": "The ID of the job to stop", "name": "jobId", "in": "path"}], "responses": {"200": {"description": "Job stopped successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "example": {"id": "job_123", "status": "canceled", "config": {"name": "Customer Service Evaluation", "description": "Evaluate customer service responses", "prompts": ["How can I help you today?"], "assertions": ["response is helpful", "response is polite"]}, "progress": {"total": 100, "completed": 45, "current": "Job stopped by user"}, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Job not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Job not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while stopping job"}}}}}}}, "/api/v1/organizations": {"get": {"description": "Get all organizations for the authenticated user", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved organizations", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "canUseRedteam": {"type": "boolean"}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}, "idpEnabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string", "nullable": true}}, "required": ["id", "name", "canUseRedteam", "excludeTargetOutputFromAgenticAttackGeneration", "idpEnabled", "createdAt", "updatedAt"]}}, "example": [{"id": "org_123", "name": "My Organization", "canUseRedteam": false, "idpEnabled": false, "excludeTargetOutputFromAgenticAttackGeneration": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": null}]}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}}, "/api/v1/organizations/{id}": {"get": {"description": "Get a specific organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization to retrieve"}, "required": true, "description": "The ID of the organization to retrieve", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved organization", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "canUseRedteam": {"type": "boolean"}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}, "idpEnabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string", "nullable": true}}, "required": ["id", "name", "canUseRedteam", "excludeTargetOutputFromAgenticAttackGeneration", "idpEnabled", "createdAt", "updatedAt"]}, "example": {"id": "org_123", "name": "My Organization", "canUseRedteam": false, "idpEnabled": false, "excludeTargetOutputFromAgenticAttackGeneration": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": null}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization not found"}}}}}}, "put": {"description": "Update an organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization to update"}, "required": true, "description": "The ID of the organization to update", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}}, "required": ["name"]}, "example": {"name": "Updated Organization Name", "redteamPrivacyEnabled": true}}}}, "responses": {"200": {"description": "Organization updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "canUseRedteam": {"type": "boolean"}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}, "idpEnabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string", "nullable": true}}, "required": ["id", "name", "canUseRedteam", "excludeTargetOutputFromAgenticAttackGeneration", "idpEnabled", "createdAt", "updatedAt"]}, "example": {"id": "org_123", "name": "Updated Organization Name", "canUseRedteam": false, "idpEnabled": false, "excludeTargetOutputFromAgenticAttackGeneration": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z"}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Unauthorized"}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization not found"}}}}}}}, "/api/v1/organizations/{id}/users": {"get": {"description": "Get all users in an organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved organization users", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "accountType": {"type": "string", "enum": ["user", "service"]}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "isAdmin": {"type": "boolean"}}, "required": ["id", "name", "email", "accountType", "createdAt", "updatedAt"]}}, "example": [{"id": "user_123", "name": "<PERSON>", "email": "<EMAIL>", "teams": [{"id": "team_123", "name": "Engineering"}], "isAdmin": false, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}]}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization not found"}}}}}}, "post": {"description": "Add a user to an organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "name": {"type": "string"}}, "required": ["email"]}, "example": {"email": "<EMAIL>", "name": "New User"}}}}, "responses": {"201": {"description": "User added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Unauthorized"}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization not found"}}}}}}}, "/api/v1/organizations/{id}/users/{userId}": {"delete": {"description": "Remove a user from an organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "id", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the user to remove"}, "required": true, "description": "The ID of the user to remove", "name": "userId", "in": "path"}], "responses": {"204": {"description": "User removed successfully"}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Unauthorized"}}}}, "404": {"description": "Organization or user not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization or user not found"}}}}}}}, "/api/v1/organizations/{id}/users/{userId}/admin": {"put": {"description": "Set user admin status in an organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "id", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the user"}, "required": true, "description": "The ID of the user", "name": "userId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"isAdmin": {"type": "boolean"}}, "required": ["isAdmin"]}, "example": {"isAdmin": true}}}}, "responses": {"200": {"description": "Admin status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"isAdmin": {"type": "boolean"}}, "required": ["isAdmin"]}, "example": {"isAdmin": true}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Unauthorized"}}}}, "404": {"description": "Organization or user not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization or user not found"}}}}}}}, "/api/v1/organizations/{id}/service-accounts": {"post": {"description": "Create a new service account", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}}, "required": ["name"]}}}}, "responses": {"200": {"description": "Service account created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "token": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "name", "token", "createdAt"]}, "example": {"id": "sa_123", "name": "CI/CD Service Account", "token": "sk-service-xxx...", "createdAt": "2024-03-20T10:00:00Z"}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization not found"}}}}}}, "get": {"description": "Get all service accounts for an organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved service accounts", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "createdAt": {"type": "string"}, "isAdmin": {"type": "boolean"}, "teams": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}}}, "required": ["id", "name", "createdAt", "isAdmin", "teams"]}}, "example": [{"id": "sa_123", "name": "CI/CD Service Account", "createdAt": "2024-03-20T10:00:00Z", "isAdmin": false, "teams": [{"id": "team_123", "name": "Engineering"}]}]}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization not found"}}}}}}}, "/api/v1/organizations/{id}/service-accounts/{accountId}": {"delete": {"description": "Delete a service account", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "id", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the service account to delete"}, "required": true, "description": "The ID of the service account to delete", "name": "accountId", "in": "path"}], "responses": {"204": {"description": "Service account deleted successfully"}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Organization or service account not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Service account not found"}}}}}}}, "/api/v1/redteam/plugins": {"get": {"description": "Get all plugin collections for the organization", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved plugin collections", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginCollectionList"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}, "post": {"description": "Create a new plugin collection", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePluginCollection"}}}}, "responses": {"201": {"description": "Plugin collection created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginCollection"}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}}, "/api/v1/redteam/plugins/{id}": {"get": {"description": "Get a specific plugin collection", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the plugin collection to retrieve"}, "required": true, "description": "The ID of the plugin collection to retrieve", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved plugin collection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginCollection"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "404": {"description": "Plugin collection not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}, "patch": {"description": "Update a plugin collection", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the plugin collection to update"}, "required": true, "description": "The ID of the plugin collection to update", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCollection"}}}}, "responses": {"200": {"description": "Plugin collection updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginCollection"}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "404": {"description": "Plugin collection not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}, "delete": {"description": "Delete a plugin collection", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the plugin collection to delete"}, "required": true, "description": "The ID of the plugin collection to delete", "name": "id", "in": "path"}], "responses": {"204": {"description": "Plugin collection deleted successfully"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "404": {"description": "Plugin collection not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "409": {"description": "Plugin collection is in use and cannot be deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PluginCollectionErrorResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}}, "/api/v1/providers/test": {"post": {"description": "Test HTTP provider setup. Validates and tests the configuration for an HTTP provider.", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"config": {"type": "object", "additionalProperties": {"nullable": true}}, "providerResponse": {"nullable": true}, "parsedResponse": {"nullable": true}, "error": {"nullable": true}}, "required": ["config"]}, "example": {"config": {"url": "https://api.example.com/v1/chat", "headers": {"Authorization": "Bearer your-api-key", "Content-Type": "application/json"}, "body": {"model": "gpt-4", "messages": [{"role": "user", "content": "Hello"}]}}, "providerResponse": null, "parsedResponse": null, "error": null}}}}, "responses": {"200": {"description": "Provider test completed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}, "example": {"success": true, "message": "Provider configuration is valid and connection successful"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Failed to test provider configuration"}}}}}}}, "/api/v1/providers/test-provider": {"post": {"description": "Test a provider configuration. Validates and tests a provider configuration with sample requests.", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"provider": {"type": "object", "properties": {"config": {"type": "object", "additionalProperties": {"nullable": true}}}, "required": ["config"]}, "type": {"type": "string"}}, "required": ["provider", "type"]}, "example": {"provider": {"config": {"apiKey": "your-api-key", "model": "gpt-4"}}, "type": "chat"}}}}, "responses": {"200": {"description": "Provider test completed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}, "example": {"success": true, "message": "Provider configuration is valid and test successful"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Failed to test provider configuration"}}}}}}}, "/api/v1/providers": {"get": {"description": "Get all providers for an organization. Returns a list of all providers associated with the organization.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "Include deleted providers in the response (default: false)"}, "required": false, "description": "Include deleted providers in the response (default: false)", "name": "includeDeleted", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved providers", "content": {"application/json": {"schema": {"type": "object", "properties": {"providers": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "config": {"nullable": true}, "applicationDescription": {"nullable": true}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "deletedAt": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}, "required": ["id", "name", "organizationId", "teamId", "created<PERSON>y", "updatedBy", "createdAt", "updatedAt", "deletedAt"]}}}, "required": ["providers"]}, "example": {"providers": [{"id": "provider_123", "name": "OpenAI Provider", "type": "openai", "config": {"apiKey": "your-api-key", "model": "gpt-4"}, "teamId": "team_123", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching providers"}}}}}}, "post": {"description": "Create a new provider. Creates a new provider with the specified configuration.", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "config": {"type": "object", "additionalProperties": {"nullable": true}}, "applicationDescription": {"type": "object", "properties": {"purpose": {"type": "string"}, "systemPrompt": {"type": "string"}, "redteamUser": {"type": "string"}, "accessToData": {"type": "string"}, "forbiddenData": {"type": "string"}, "accessToActions": {"type": "string"}, "forbiddenActions": {"type": "string"}, "connectedSystems": {"type": "string"}}}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "teamId": {"type": "string"}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}, "required": ["name", "config", "teamId"]}, "example": {"name": "OpenAI Provider", "config": {"apiKey": "your-api-key", "model": "gpt-4"}, "teamId": "team_123"}}}}, "responses": {"201": {"description": "Provider created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "config": {"nullable": true}, "applicationDescription": {"nullable": true}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "deletedAt": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}, "required": ["id", "name", "organizationId", "teamId", "created<PERSON>y", "updatedBy", "createdAt", "updatedAt", "deletedAt"]}, "example": {"id": "provider_123", "name": "OpenAI Provider", "config": {"apiKey": "your-api-key", "model": "gpt-4"}, "teamId": "team_123", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while creating provider"}}}}}}}, "/api/v1/providers/{id}": {"get": {"description": "Get a specific provider. Returns detailed information about a single provider.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the provider to retrieve"}, "required": true, "description": "The ID of the provider to retrieve", "name": "id", "in": "path"}, {"schema": {"type": "boolean"}, "required": false, "name": "includeDeleted", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved provider", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "config": {"nullable": true}, "applicationDescription": {"nullable": true}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "deletedAt": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}, "required": ["id", "name", "organizationId", "teamId", "created<PERSON>y", "updatedBy", "createdAt", "updatedAt", "deletedAt"]}, "example": {"id": "provider_123", "name": "OpenAI Provider", "config": {"apiKey": "your-api-key", "model": "gpt-4"}, "teamId": "team_123", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Provider not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Provider not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching provider"}}}}}}, "patch": {"description": "Update a provider. Updates the configuration and details of an existing provider.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the provider to update"}, "required": true, "description": "The ID of the provider to update", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "config": {"type": "object", "additionalProperties": {"nullable": true}}, "applicationDescription": {"type": "object", "properties": {"purpose": {"type": "string"}, "systemPrompt": {"type": "string"}, "redteamUser": {"type": "string"}, "accessToData": {"type": "string"}, "forbiddenData": {"type": "string"}, "accessToActions": {"type": "string"}, "forbiddenActions": {"type": "string"}, "connectedSystems": {"type": "string"}}}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "teamId": {"type": "string"}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}}, "example": {"name": "Updated OpenAI Provider", "config": {"apiKey": "new-api-key", "model": "gpt-4-turbo"}}}}}, "responses": {"200": {"description": "Provider updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "config": {"nullable": true}, "applicationDescription": {"nullable": true}, "sessionSource": {"type": "string", "nullable": true, "enum": ["server", "client"]}, "stateful": {"type": "boolean"}, "extensions": {"type": "array", "items": {"type": "string"}}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "deletedAt": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["http", "websocket", "browser", "azure", "openrouter", "python", "javascript", "custom"]}}, "required": ["id", "name", "organizationId", "teamId", "created<PERSON>y", "updatedBy", "createdAt", "updatedAt", "deletedAt"]}, "example": {"id": "provider_123", "name": "Updated OpenAI Provider", "config": {"apiKey": "new-api-key", "model": "gpt-4-turbo"}, "teamId": "team_123", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Provider not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Provider not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while updating provider"}}}}}}, "delete": {"description": "Delete a provider. Removes a provider from the system. If the provider is in use, the deletion will fail.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the provider to delete"}, "required": true, "description": "The ID of the provider to delete", "name": "id", "in": "path"}], "responses": {"204": {"description": "Provider deleted successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Provider not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Provider not found"}}}}, "409": {"description": "Provider is in use", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "array", "items": {"type": "string"}}}, "required": ["error"]}, "example": {"error": "Provider is in use", "details": ["Provider is referenced by 3 active evaluations"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while deleting provider"}}}}}}}, "/api/v1/providers/{id}/consolidated-remediation": {"get": {"description": "Retrieves a consolidated remediation for a specific provider or target.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the provider or target to retrieve consolidated remediation for"}, "required": true, "description": "The ID of the provider or target to retrieve consolidated remediation for", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved consolidated remediation", "content": {"application/json": {"schema": {"type": "object", "properties": {"targetId": {"type": "string"}, "targetName": {"type": "string"}, "remediation": {"type": "string"}}, "required": ["targetId", "targetName", "remediation"]}, "example": {"targetId": "provider_123", "targetName": "OpenAI Provider", "remediation": "{\"prioritized_suggestions\":[{\"title\":\"Implement input validation\",\"remediation\":\"Add input validation to prevent prompt injection attacks.\",\"remediation_path\":\"api_guardrails\",\"impact_level\":\"high\",\"implementation_complexity\":\"moderate\",\"timeframe\":\"immediate\",\"related_issues\":[\"issue_123\",\"issue_456\"]}],\"issue_references\":[{\"issue_id\":\"issue_123\",\"weakness\":\"prompt_injection\",\"severity\":\"high\"},{\"issue_id\":\"issue_456\",\"weakness\":\"jailbreak\",\"severity\":\"high\"}]}"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "No consolidated remediation found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "example": {"message": "No consolidated remediation found. Please generate one first."}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while retrieving consolidated remediation"}}}}}}, "post": {"description": "Initiates generation of a consolidated remediation for a specific provider or target.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the provider or target to generate consolidated remediation for"}, "required": true, "description": "The ID of the provider or target to generate consolidated remediation for", "name": "id", "in": "path"}, {"schema": {"type": "string", "description": "Force regeneration of the remediation (default: false)"}, "required": false, "description": "Force regeneration of the remediation (default: false)", "name": "forceRefresh", "in": "query"}], "responses": {"202": {"description": "Consolidated remediation generation started", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "example": {"message": "Consolidated remediation generation started"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while starting remediation generation"}}}}}}}, "/api/v1/redteam/configs": {"get": {"description": "Get all redteam configs for an organization", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved configs", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "config": {"nullable": true}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "pluginCollectionId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "user": {"type": "string"}}, "required": ["id", "name", "type", "organizationId", "teamId", "userId", "createdAt"]}}}, "required": ["data"]}}}}, "400": {"description": "User must be a member of an organization", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}, "post": {"description": "Create a new redteam config", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string", "nullable": true}, "config": {"nullable": true}, "teamId": {"type": "string"}, "pluginCollectionId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}}, "required": ["name", "teamId"]}}}}, "responses": {"201": {"description": "Config created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "config": {"nullable": true}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "pluginCollectionId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "user": {"type": "string"}}, "required": ["id", "name", "type", "organizationId", "teamId", "userId", "createdAt"]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}}, "/api/v1/redteam/configs/{configId}": {"get": {"description": "Get a specific redteam config", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "configId", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved config", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "config": {"nullable": true}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "pluginCollectionId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "user": {"type": "string"}}, "required": ["id", "name", "type", "organizationId", "teamId", "userId", "createdAt"]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "404": {"description": "Config not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}, "put": {"description": "Update a redteam config", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "configId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string", "nullable": true}, "config": {"nullable": true}, "teamId": {"type": "string"}, "pluginCollectionId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "Config updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "config": {"nullable": true}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "pluginCollectionId": {"type": "string"}, "userId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "user": {"type": "string"}}, "required": ["id", "name", "type", "organizationId", "teamId", "userId", "createdAt"]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "404": {"description": "Config not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}, "delete": {"description": "Delete a redteam config", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "configId", "in": "path"}], "responses": {"204": {"description": "Config deleted successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "404": {"description": "Config not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}}, "/api/v1/redteam/configs/{configId}/unified": {"get": {"description": "Get unified config format", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "configId", "in": "path"}, {"schema": {"type": "string"}, "required": false, "name": "providerId", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved unified config", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "sharing": {"type": "boolean"}, "description": {"type": "string"}, "prompts": {"type": "array", "items": {"type": "string"}}, "target": {"nullable": true}, "plugins": {"type": "array", "items": {"anyOf": [{"nullable": true}, {"type": "object", "properties": {"id": {"type": "string"}, "config": {"nullable": true}}, "required": ["id"]}, {"nullable": true}]}}, "pluginCollectionId": {"type": "string"}, "strategies": {"type": "array", "items": {"nullable": true}}, "purpose": {"type": "string"}, "numTests": {"type": "number"}, "extensions": {"type": "array", "items": {"type": "string"}}, "applicationDefinition": {"nullable": true}, "entities": {"type": "array", "items": {"type": "string"}}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "userId": {"type": "string"}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "runOptions": {"nullable": true}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}}, "required": ["description", "prompts", "plugins", "strategies", "entities"]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "404": {"description": "Config not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}}}}}}}, "/api/v1/results": {"get": {"description": "Get all evaluation summaries. Returns a paginated list of evaluations with their summary information.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "Maximum number of evaluations to return (default: 20)"}, "required": false, "description": "Maximum number of evaluations to return (default: 20)", "name": "limit", "in": "query"}, {"schema": {"type": "string", "description": "Number of evaluations to skip (default: 0)"}, "required": false, "description": "Number of evaluations to skip (default: 0)", "name": "offset", "in": "query"}, {"schema": {"type": "string", "description": "Filter by team ID"}, "required": false, "description": "Filter by team ID", "name": "teamId", "in": "query"}, {"schema": {"type": "string", "description": "Filter by evaluation status (running, completed, failed, canceled)"}, "required": false, "description": "Filter by evaluation status (running, completed, failed, canceled)", "name": "status", "in": "query"}, {"schema": {"type": "string", "description": "Filter by evaluation name (partial match)"}, "required": false, "description": "Filter by evaluation name (partial match)", "name": "name", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved evaluations", "content": {"application/json": {"schema": {"type": "object", "properties": {"evals": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "provider": {"type": "string"}, "organizationId": {"type": "string"}, "teamId": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "resultsCount": {"type": "number"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["id", "createdAt", "updatedAt", "name", "organizationId", "status"]}}, "total": {"type": "number"}}, "required": ["evals", "total"]}, "example": {"evals": [{"id": "eval_123", "name": "Customer Service Evaluation", "description": "Testing response quality for customer queries", "provider": "openai", "organizationId": "org_123", "teamId": "team_123", "status": "completed", "resultsCount": 50, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:15:00Z", "metadata": {"tags": "customer-service,quality"}}], "total": 1}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching evaluations"}}}}}}, "post": {"description": "Create a new evaluation. Starts a new evaluation process with the specified configuration.", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "teamId": {"type": "string"}, "evalOnly": {"type": "boolean"}, "originalEvalId": {"type": "string"}, "subsetOptions": {"type": "object", "properties": {"subsetType": {"type": "string"}, "subsetName": {"type": "string"}}, "required": ["subsetType"]}}, "required": ["teamId"]}, {"nullable": true}]}, "example": {"config": {"name": "Customer Service Evaluation", "description": "Testing response quality for customer queries", "prompts": ["How can I reset my password?", "Where can I update my profile?"], "assertions": ["response is helpful", "response is accurate"]}, "teamId": "team_123", "force": false, "verbose": true}}}}, "responses": {"200": {"description": "Evaluation created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "example": {"id": "eval_123", "name": "Customer Service Evaluation", "description": "Testing response quality for customer queries", "provider": "openai", "organizationId": "org_123", "teamId": "team_123", "status": "running", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while creating evaluation"}}}}}}}, "/api/v1/results/{id}": {"get": {"description": "Get a specific evaluation by ID. Returns detailed information about a single evaluation including results.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved evaluation", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "example": {"id": "eval_123", "name": "Customer Service Evaluation", "description": "Testing response quality for customer queries", "provider": "openai", "organizationId": "org_123", "teamId": "team_123", "status": "completed", "results": [{"id": "result_123", "evalId": "eval_123", "prompt": "How can I reset my password?", "response": "You can reset your password by clicking on the 'Forgot Password' link on the login page.", "metrics": {"helpfulness": 0.9, "accuracy": 0.95}, "createdAt": "2024-03-20T10:02:00Z", "updatedAt": "2024-03-20T10:02:00Z"}], "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:15:00Z", "metadata": {"tags": "customer-service,quality"}}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Evaluation not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Evaluation not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching evaluation"}}}}}}, "delete": {"description": "Delete an evaluation. Permanently removes an evaluation and its results.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"204": {"description": "Evaluation deleted successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Evaluation not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Evaluation not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while deleting evaluation"}}}}}}, "patch": {"description": "Update an evaluation. Modifies metadata or other properties of an existing evaluation.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"config": {"nullable": true}, "isPublic": {"type": "boolean"}}}, "example": {"config": {"name": "Updated Evaluation Name", "description": "Updated description for the evaluation"}, "isPublic": true}}}}, "responses": {"200": {"description": "Evaluation updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "canceled"]}, "config": {"nullable": true}, "configId": {"type": "string", "nullable": true}, "redteam": {"type": "boolean"}, "evalId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "progress": {"type": "object", "properties": {"completed": {"type": "number"}, "total": {"type": "number"}, "passes": {"type": "number"}, "failures": {"type": "number"}, "errors": {"type": "number"}}, "required": ["completed", "total", "passes", "failures", "errors"]}, "teamId": {"type": "string"}}, "required": ["id", "createdAt", "updatedAt", "status", "configId", "redteam", "evalId", "teamId"]}, "example": {"id": "eval_123", "name": "Updated Evaluation Name", "description": "Updated description for the evaluation", "provider": "openai", "organizationId": "org_123", "teamId": "team_123", "status": "completed", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:30:00Z", "metadata": {"tags": "updated-tags,quality-check"}}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Evaluation not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Evaluation not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while updating evaluation"}}}}}}}, "/api/v1/results/{id}/results": {"post": {"description": "Add results to an evaluation. Appends new result data to an existing evaluation.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "prompt": {"nullable": true}, "provider": {"nullable": true}, "vars": {"type": "object", "additionalProperties": {"nullable": true}}, "output": {"nullable": true}, "latencyMs": {"type": "number"}, "success": {"type": "boolean"}, "error": {"type": "string"}, "gradingResult": {"nullable": true}}}}, "example": [{"prompt": "How can I track my order?", "output": "You can track your order by logging into your account and viewing the order history section.", "provider": {"id": "provider_123", "name": "OpenAI Provider"}, "gradingResult": {"score": 0.85, "pass": true}}]}}}, "responses": {"200": {"description": "Results added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "count": {"type": "number"}}, "required": ["success", "count"]}, "example": {"success": true, "count": 1}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Evaluation not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Evaluation not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while adding results"}}}}}}}, "/api/v1/results/{id}/generateIssues": {"post": {"description": "Generate issues from an evaluation. Analyzes evaluation results to create issue records.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "required": true, "name": "id", "in": "path"}], "responses": {"200": {"description": "Issues generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"issues": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "pluginId": {}, "weakness": {"type": "string"}, "status": {"type": "string", "enum": ["open", "ignored", "false_positive", "fixed"]}, "organizationId": {"type": "string"}, "severity": {"type": "string", "enum": ["critical", "high", "medium", "low"]}, "targetId": {"type": "string", "nullable": true}, "providerId": {"type": "string", "nullable": true}, "teamId": {"type": "string"}, "occurrences": {"type": "integer", "minimum": 0}, "history": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string"}, "name": {"type": "string"}, "text": {"type": "string", "minLength": 1, "maxLength": 5000}, "data": {"nullable": true}, "type": {"type": "string", "enum": ["comment_added", "created", "failed_tests_added", "failed_tests_removed", "severity_changed", "status_changed", "updated"]}}, "required": ["createdAt", "name", "text", "type"]}}}, "required": ["id", "createdAt", "updatedAt", "pluginId", "weakness", "status", "organizationId", "severity", "targetId", "providerId", "teamId", "occurrences", "history"]}}}, "required": ["issues"]}, "example": {"issues": [{"id": "issue_123", "status": "open", "severity": "medium", "weakness": "Response does not address the user's question", "evalId": "eval_123", "organizationId": "org_123", "createdAt": "2024-03-20T10:30:00Z", "updatedAt": "2024-03-20T10:30:00Z"}]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Evaluation not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Evaluation not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while generating issues"}}}}}}}, "/api/v1/results/{id}/results/{resultsId}/rating": {"post": {"description": "Rate a specific result in an evaluation. Adds user-provided rating and feedback to a result.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "format": "uuid"}, "required": true, "name": "id", "in": "path"}, {"schema": {"type": "string", "format": "uuid"}, "required": true, "name": "resultsId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"result": {"type": "string"}, "score": {"type": "number", "minimum": 0, "maximum": 1}, "reasonsHtml": {"type": "string"}, "reasons": {"type": "array", "items": {"type": "string"}}}, "required": ["result"]}, "example": {"result": "pass", "score": 0.8, "reasonsHtml": "<p>Good response but could be more concise</p>", "reasons": ["Good response but could be more concise"]}}}}, "responses": {"200": {"description": "Rating added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}, "required": ["success"]}, "example": {"success": true}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Evaluation or result not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Result not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while adding rating"}}}}}}}, "/api/v1/organizations/{organizationId}/roles": {"get": {"description": "Get all roles in the organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "organizationId", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved roles", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "permissionSets", "createdAt", "updatedAt"]}}, "example": [{"id": "role_123", "name": "Admin", "organizationId": "org_123", "permissionSets": ["ADMINISTRATOR"], "createdAt": "2024-03-20T10:00:00.000Z", "updatedAt": "2024-03-20T10:00:00.000Z"}]}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}, "post": {"description": "Create a new role", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "organizationId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}}, "required": ["name", "permissionSets"]}, "example": {"name": "Admin", "permissionSets": ["ADMINISTRATOR"]}}}}, "responses": {"201": {"description": "Role created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "permissionSets", "createdAt", "updatedAt"]}, "example": {"id": "role_123", "name": "Admin", "organizationId": "org_123", "permissionSets": ["ADMINISTRATOR"], "createdAt": "2024-03-20T10:00:00.000Z", "updatedAt": "2024-03-20T10:00:00.000Z"}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}}, "/api/v1/organizations/{organizationId}/roles/{id}": {"get": {"description": "Get a specific role", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "organizationId", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the role to retrieve"}, "required": true, "description": "The ID of the role to retrieve", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved role", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "permissionSets", "createdAt", "updatedAt"]}, "example": {"id": "role_123", "name": "Admin", "organizationId": "org_123", "permissionSets": ["ADMINISTRATOR"], "createdAt": "2024-03-20T10:00:00.000Z", "updatedAt": "2024-03-20T10:00:00.000Z"}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Role not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Role not found"}}}}}}, "put": {"description": "Update a role", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "organizationId", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the role to update"}, "required": true, "description": "The ID of the role to update", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}}, "required": ["name", "permissionSets"]}, "example": {"name": "Updated Admin Role", "permissionSets": ["ADMINISTRATOR", "MANAGE_EVALS"]}}}}, "responses": {"200": {"description": "Role updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "permissionSets": {"type": "array", "items": {"type": "string", "enum": ["ADMINISTRATOR", "VIEW_CONFIGURATIONS", "MANAGE_CONFIGURATIONS", "MANAGE_PROVIDERS", "RUN_SCANS", "VIEW_EVALS", "MANAGE_EVALS", "VIEW_ISSUES", "MANAGE_ISSUES"]}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "permissionSets", "createdAt", "updatedAt"]}, "example": {"id": "role_123", "name": "Updated Admin Role", "organizationId": "org_123", "permissionSets": ["ADMINISTRATOR", "MANAGE_EVALS"], "createdAt": "2024-03-20T10:00:00.000Z", "updatedAt": "2024-03-20T10:01:00.000Z"}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Role not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Role not found"}}}}}}, "delete": {"description": "Delete a role", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "organizationId", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the role to delete"}, "required": true, "description": "The ID of the role to delete", "name": "id", "in": "path"}], "responses": {"204": {"description": "Role deleted successfully"}, "400": {"description": "Role is in use and cannot be deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Role is in use and cannot be deleted"}}}}, "401": {"description": "User not authenticated or not an admin", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Role not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Role not found"}}}}}}}, "/api/v1/organizations/{organizationId}/usage": {"get": {"description": "Get usage statistics for an organization", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "organizationId", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved usage statistics", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "targets": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["CLOUD", "CLI"]}, "status": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "createdAt": {"type": "string"}, "usage": {"type": "number"}, "team": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}}, "required": ["id", "name", "type", "status", "createdAt", "usage", "team"]}}}, "required": ["id", "targets"]}}}}}}}, "/api/v1/teams": {"post": {"description": "Create a new team", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}}, "required": ["name"]}, "example": {"name": "Engineering Team", "description": "Team responsible for engineering tasks"}}}}, "responses": {"201": {"description": "Team created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string", "nullable": true}, "members": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "roleId": {"type": "string"}}, "required": ["id", "name", "email", "roleId"]}}}, "required": ["id", "name", "organizationId", "createdAt", "updatedAt"]}, "example": {"id": "team_123", "name": "Engineering Team", "description": "Team responsible for engineering tasks", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": null, "members": []}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}, "get": {"description": "Get all teams in the organization", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved teams", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string", "nullable": true}, "members": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "roleId": {"type": "string"}}, "required": ["id", "name", "email", "roleId"]}}}, "required": ["id", "name", "organizationId", "createdAt", "updatedAt"]}}, "example": [{"id": "team_123", "name": "Engineering Team", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": null, "members": [{"id": "user_123", "name": "<PERSON>", "email": "<EMAIL>", "roleId": "role_123"}]}]}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}}, "/api/v1/teams/{id}": {"get": {"description": "Get a specific team", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team to retrieve"}, "required": true, "description": "The ID of the team to retrieve", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved team", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string", "nullable": true}, "members": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "roleId": {"type": "string"}}, "required": ["id", "name", "email", "roleId"]}}}, "required": ["id", "name", "organizationId", "createdAt", "updatedAt", "members"]}, "example": {"id": "team_123", "name": "Engineering Team", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": null, "members": [{"id": "user_123", "name": "<PERSON>", "email": "<EMAIL>", "roleId": "role_123"}]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "403": {"description": "User does not have access to this team", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Forbidden"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}}}, "put": {"description": "Update a team", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team to update"}, "required": true, "description": "The ID of the team to update", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}}}, "example": {"name": "Updated Team Name"}}}}, "responses": {"200": {"description": "Team updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "members": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "roleId": {"type": "string"}}, "required": ["id", "name", "email", "roleId"]}}}, "required": ["id", "name", "organizationId", "createdAt", "updatedAt", "members"]}, "example": {"id": "team_123", "name": "Updated Team Name", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z", "members": []}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "403": {"description": "User does not have access to this team", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Forbidden"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}}}, "delete": {"description": "Delete a team", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team to delete"}, "required": true, "description": "The ID of the team to delete", "name": "id", "in": "path"}], "responses": {"204": {"description": "Team deleted successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "403": {"description": "User does not have access to this team", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Forbidden"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}}}}, "/api/v1/teams/{id}/members": {"get": {"description": "Get team members", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team"}, "required": true, "description": "The ID of the team", "name": "id", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved team members", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "roleId": {"type": "string"}}, "required": ["id", "name", "email", "roleId"]}}, "example": [{"id": "user_123", "name": "<PERSON>", "email": "<EMAIL>", "roleId": "role_123"}]}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "403": {"description": "User does not have access to this team", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Forbidden"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}}}, "post": {"description": "Add a member to the team", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team"}, "required": true, "description": "The ID of the team", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}}, "required": ["userId", "roleId"]}, "example": {"userId": "user_123", "roleId": "role_123"}}}}, "responses": {"201": {"description": "Member added successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "403": {"description": "User does not have access to this team", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Forbidden"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}}}}, "/api/v1/teams/{id}/members/{userId}": {"put": {"description": "Update a member's role in the team", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team"}, "required": true, "description": "The ID of the team", "name": "id", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the user"}, "required": true, "description": "The ID of the user", "name": "userId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid"}}, "required": ["roleId"]}, "example": {"roleId": "role_123"}}}}, "responses": {"204": {"description": "Member role updated successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}}}, "delete": {"description": "Remove a member from the team", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team"}, "required": true, "description": "The ID of the team", "name": "id", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the user to remove"}, "required": true, "description": "The ID of the user to remove", "name": "userId", "in": "path"}], "responses": {"204": {"description": "Member removed successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "403": {"description": "User does not have access to this team", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Forbidden"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team not found"}}}}}}}, "/api/v1/me": {"get": {"description": "Get current user information", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved user information", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "accountType": {"type": "string", "enum": ["user", "service"]}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "isAdmin": {"type": "boolean"}}, "required": ["id", "name", "email", "accountType", "createdAt", "updatedAt"]}, "organization": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "canUseRedteam": {"type": "boolean"}, "excludeTargetOutputFromAgenticAttackGeneration": {"type": "boolean"}, "idpEnabled": {"type": "boolean"}}, "required": ["id", "name", "canUseRedteam", "excludeTargetOutputFromAgenticAttackGeneration", "idpEnabled"]}, "app": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"]}, "isAdmin": {"type": "boolean"}}, "required": ["user", "app", "isAdmin"]}, "example": {"user": {"id": "user_123", "name": "<PERSON>", "email": "<EMAIL>", "accountType": "user", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z", "isAdmin": false}, "organization": {"id": "org_123", "name": "Example Org", "canUseRedteam": true, "idpEnabled": false}, "app": {"url": "https://app.example.com"}, "isAdmin": false}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}}, "/api/v1/users/me/teams": {"get": {"description": "Get teams for the current authenticated user", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved user teams", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "createdAt", "updatedAt"]}}, "example": [{"id": "team_123", "name": "Engineering", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z"}]}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}}, "/api/v1/users/me/abilities": {"get": {"description": "Get user abilities for a specific team", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the team to get abilities for"}, "required": true, "description": "The ID of the team to get abilities for", "name": "teamId", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved user abilities", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"action": {"type": "string"}, "subject": {"type": "string"}, "conditions": {"type": "object", "additionalProperties": {"nullable": true}}, "inverted": {"type": "boolean"}, "reason": {"type": "string"}}, "required": ["action", "subject"]}}, "example": [{"action": "read", "subject": "Project", "conditions": {"teamId": "team_123"}, "inverted": false}]}}}, "400": {"description": "Team ID is required", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Team ID query parameter is required"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}}, "/api/v1/users/me/reset-password": {"post": {"description": "Request a password reset for the current user", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Password reset request sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "example": {"message": "Password reset request sent"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}}}}, "/api/v1/users/status": {"get": {"description": "Get user status and usage information", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "Email address to check status for"}, "required": true, "description": "Email address to check status for", "name": "email", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved user status", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok", "exceeded_limit", "show_usage_warning"]}, "error": {"type": "string"}, "message": {"type": "string"}}, "required": ["status"]}, "example": {"status": "ok"}}}}, "400": {"description": "Email is required", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Email is required"}}}}}}}, "/api/v1/users/login": {"post": {"description": "Login a user (only available in public mode)", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "organizationId": {"type": "string"}}, "required": ["email"]}, "example": {"email": "<EMAIL>"}}}}, "responses": {"200": {"description": "Successfully logged in", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}}, "required": ["token"]}, "example": {"token": "jwt-token-xxx..."}}}}, "400": {"description": "Email is required", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Email is required"}}}}, "401": {"description": "Authentication failed", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Authentication failed"}}}}}}}, "/api/v1/users/{id}/teams": {"get": {"description": "Get teams for a specific user", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the user"}, "required": true, "description": "The ID of the user", "name": "id", "in": "path"}, {"schema": {"type": "string", "description": "The ID of the organization"}, "required": true, "description": "The ID of the organization", "name": "organizationId", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved user teams", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "createdAt", "updatedAt"]}}, "example": [{"id": "team_123", "name": "Engineering", "organizationId": "org_123", "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:01:00Z"}]}}}, "400": {"description": "Organization ID is required", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Organization ID is required"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not found"}}}}}}}, "/api/v1/webhooks": {"get": {"description": "Get all webhooks for an organization. Returns a list of webhooks that can be used to receive event notifications.", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved webhooks", "content": {"application/json": {"schema": {"type": "object", "properties": {"webhooks": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}}}, "required": ["webhooks"]}, "example": {"webhooks": [{"id": "webhook_123", "name": "New Evaluation Webhook", "organizationId": "org_123", "events": ["evaluation.created", "evaluation.completed"], "url": "https://example.com/webhook", "secret": "your-webhook-secret", "enabled": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching webhooks"}}}}}}, "post": {"description": "Create a new webhook. Creates a webhook that will receive notifications for the specified events.", "security": [{"BearerAuth": []}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "enabled": {"type": "boolean"}}, "required": ["name", "events", "url"]}, "example": {"name": "New Evaluation Webhook", "events": ["evaluation.created", "evaluation.completed"], "url": "https://example.com/webhook", "enabled": true}}}}, "responses": {"201": {"description": "Webhook created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"webhook": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}}, "required": ["webhook"]}, "example": {"webhook": {"id": "webhook_123", "name": "New Evaluation Webhook", "organizationId": "org_123", "events": ["evaluation.created", "evaluation.completed"], "url": "https://example.com/webhook", "secret": "generated-webhook-secret", "enabled": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}}}}}, "400": {"description": "Invalid request body", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "URL must be a valid URL"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while creating webhook"}}}}}}}, "/api/v1/webhooks/{webhookId}": {"get": {"description": "Get a specific webhook by ID. Returns detailed information about a single webhook.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the webhook to retrieve"}, "required": true, "description": "The ID of the webhook to retrieve", "name": "webhookId", "in": "path"}], "responses": {"200": {"description": "Successfully retrieved webhook", "content": {"application/json": {"schema": {"type": "object", "properties": {"webhook": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}}, "required": ["webhook"]}, "example": {"webhook": {"id": "webhook_123", "name": "New Evaluation Webhook", "organizationId": "org_123", "events": ["evaluation.created", "evaluation.completed"], "url": "https://example.com/webhook", "secret": "your-webhook-secret", "enabled": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:00:00Z"}}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Webhook not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Webhook not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching webhook"}}}}}}, "put": {"description": "Update a webhook. Modifies the configuration of an existing webhook.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the webhook to update"}, "required": true, "description": "The ID of the webhook to update", "name": "webhookId", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "enabled": {"type": "boolean"}}}, "example": {"name": "Updated Webhook Name", "events": ["evaluation.created", "evaluation.completed", "evaluation.failed"], "url": "https://updated-example.com/webhook", "enabled": true}}}}, "responses": {"200": {"description": "Webhook updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"webhook": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}}, "required": ["webhook"]}, "example": {"webhook": {"id": "webhook_123", "name": "Updated Webhook Name", "organizationId": "org_123", "events": ["evaluation.created", "evaluation.completed", "evaluation.failed"], "url": "https://updated-example.com/webhook", "secret": "your-webhook-secret", "enabled": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T10:30:00Z"}}}}}, "400": {"description": "Invalid request body", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "URL must be a valid URL"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Webhook not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Webhook not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while updating webhook"}}}}}}, "delete": {"description": "Delete a webhook. Permanently removes a webhook from the system.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the webhook to delete"}, "required": true, "description": "The ID of the webhook to delete", "name": "webhookId", "in": "path"}], "responses": {"204": {"description": "Webhook deleted successfully"}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Webhook not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Webhook not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while deleting webhook"}}}}}}}, "/api/v1/webhooks/{webhookId}/regenerate-secret": {"post": {"description": "Regenerate webhook secret. Creates a new secret for an existing webhook.", "security": [{"BearerAuth": []}], "parameters": [{"schema": {"type": "string", "description": "The ID of the webhook to regenerate the secret for"}, "required": true, "description": "The ID of the webhook to regenerate the secret for", "name": "webhookId", "in": "path"}], "responses": {"200": {"description": "Secret regenerated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"webhook": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1}, "organizationId": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "url": {"type": "string", "format": "uri"}, "secret": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["id", "name", "organizationId", "events", "url", "secret", "createdAt", "updatedAt"]}}, "required": ["webhook"]}, "example": {"webhook": {"id": "webhook_123", "name": "New Evaluation Webhook", "organizationId": "org_123", "events": ["evaluation.created", "evaluation.completed"], "url": "https://example.com/webhook", "secret": "new-generated-webhook-secret", "enabled": true, "createdAt": "2024-03-20T10:00:00Z", "updatedAt": "2024-03-20T11:00:00Z"}}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "404": {"description": "Webhook not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Webhook not found"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while regenerating webhook secret"}}}}}}}, "/api/v1/webhooks/event-types": {"get": {"description": "Get available webhook event types. Returns all event types that can be used to configure webhooks.", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved webhook event types", "content": {"application/json": {"schema": {"type": "object", "properties": {"eventTypes": {"type": "array", "items": {"type": "string"}}}, "required": ["eventTypes"]}, "example": {"eventTypes": ["evaluation.created", "evaluation.completed", "evaluation.failed", "issue.created", "issue.updated"]}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Internal server error occurred while fetching event types"}}}}}}}, "/api/v1/grading-examples": {"get": {"description": "Get all grading examples for the organization", "tags": ["Grading Examples"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "List of grading examples retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GradingExampleListResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server error"}}}, "post": {"description": "Create a new grading example", "tags": ["Grading Examples"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGradingExample"}}}}, "responses": {"201": {"description": "Grading example created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GradingExample"}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}}}}, "/api/v1/grading-examples/plugin/{pluginId}": {"get": {"description": "Get grading examples by plugin ID", "tags": ["Grading Examples"], "security": [{"BearerAuth": []}], "parameters": [{"name": "pluginId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The plugin ID to filter examples by"}], "responses": {"200": {"description": "List of grading examples retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GradingExampleListResponse"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server error"}}}}, "/api/v1/grading-examples/{id}": {"get": {"description": "Get a grading example by ID", "tags": ["Grading Examples"], "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the grading example to retrieve"}], "responses": {"200": {"description": "Grading example retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GradingExample"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Grading example not found"}}}, "patch": {"description": "Update a grading example", "tags": ["Grading Examples"], "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the grading example to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGradingExample"}}}}, "responses": {"200": {"description": "Grading example updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GradingExample"}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "404": {"description": "Grading example not found"}}}, "delete": {"description": "Delete a grading example", "tags": ["Grading Examples"], "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The ID of the grading example to delete"}], "responses": {"204": {"description": "Grading example deleted successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Grading example not found"}}}}, "/api/v1/audit-logs": {"get": {"description": "Get organization audit logs", "summary": "Retrieve audit logs for an organization", "tags": ["<PERSON><PERSON>"], "security": [{"bearerAuth": []}], "parameters": [{"schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}, "required": false, "name": "limit", "in": "query"}, {"schema": {"type": "integer", "nullable": true, "minimum": 0, "default": 0}, "required": false, "name": "offset", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "createdAtGte", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "createdAtLte", "in": "query"}, {"schema": {"type": "string", "enum": ["login", "user_added", "user_removed", "role_created", "role_deleted", "role_updated", "team_created", "team_deleted", "user_added_to_team", "user_removed_from_team", "user_role_changed_in_team", "org_admin_added", "org_admin_removed", "service_account_created", "service_account_deleted"]}, "required": false, "name": "action", "in": "query"}, {"schema": {"type": "string", "enum": ["user", "role", "team", "organization", "service_account"]}, "required": false, "name": "target", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "actorId", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved audit logs", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "number"}, "limit": {"type": "number"}, "offset": {"type": "number"}, "logs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "description": {"type": "string"}, "actorId": {"type": "string", "nullable": true}, "actorName": {"type": "string", "nullable": true}, "actorEmail": {"type": "string", "nullable": true}, "action": {"type": "string"}, "actionDisplayName": {"type": "string"}, "target": {"type": "string"}, "targetId": {"type": "string", "nullable": true}, "metadata": {"type": "object", "nullable": true, "additionalProperties": {"nullable": true}}, "organizationId": {"type": "string"}, "teamId": {"type": "string", "nullable": true}, "createdAt": {"type": "string"}}, "required": ["id", "description", "actorId", "<PERSON><PERSON><PERSON>", "actor<PERSON><PERSON>", "action", "actionDisplayName", "target", "targetId", "metadata", "organizationId", "teamId", "createdAt"]}}}, "required": ["total", "limit", "offset", "logs"]}, "example": {"total": 50, "limit": 20, "offset": 0, "logs": [{"id": "audit_123", "description": "<PERSON> logged in", "actorId": "user_123", "actorName": "<PERSON>", "actorEmail": "<EMAIL>", "action": "login", "actionDisplayName": "User Login", "target": "user", "targetId": "user_123", "metadata": null, "organizationId": "org_123", "teamId": null, "createdAt": "2024-05-20T10:00:00Z"}, {"id": "audit_124", "description": "<PERSON> created team Engineering", "actorId": "user_123", "actorName": "<PERSON>", "actorEmail": "<EMAIL>", "action": "team_created", "actionDisplayName": "Team Created", "target": "team", "targetId": "team_123", "metadata": null, "organizationId": "org_123", "teamId": "team_123", "createdAt": "2024-05-21T11:30:00Z"}]}}}}, "400": {"description": "Bad request - invalid parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "object", "additionalProperties": {"nullable": true}}}, "required": ["error"]}, "example": {"error": "Invalid query parameters", "details": {"limit": {"_errors": ["Number must be less than or equal to 100"]}}}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "User not authenticated"}}}}, "403": {"description": "User does not have organization admin privileges", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}, "required": ["error"]}, "example": {"error": "Access denied"}}}}}}}}}