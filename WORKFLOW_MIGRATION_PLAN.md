# GitHub Actions Workflow Migration to Infisical

## 🎯 Objective
Replace private-keys folder and combine-secret-keys.sh usage in GitHub Actions workflows with Infisical secret loading.

## 📋 Current State Analysis

### Current Secret Loading Methods
1. **GitHub Repository Secrets**: Direct access via `${{ secrets.SECRET_NAME }}`
2. **Private-Keys Folder**: Volume-mounted via git submodule
3. **combine-secret-keys.sh**: Script that combines .env files
4. **load-env.sh**: GitHub Actions script that loads .env files to GITHUB_ENV

### Key Files to Update
- `.github/workflows/build-deploy-changed-services.yml` (main deployment)
- `.github/workflows/comment-actions.yml` (PR comment deployments)
- `.github/scripts/load-env.sh` (environment loader)
- `deploy/util/combine-secret-keys.sh` (secret combiner)

## 🔄 Migration Strategy

### Phase 1: Add Infisical Integration ✅
- [x] Create reusable action: `.github/actions/load-infisical-secrets/action.yml`
- [x] Create test workflow: `.github/workflows/test-infisical-integration.yml`

### Phase 2: Update Main Workflow (Current)
Replace secret loading in `build-deploy-changed-services.yml`:

#### Current Pattern (Line 1685):
```yaml
- name: Load environment variables
  run: |
    chmod +x .github/scripts/load-env.sh
    .github/scripts/load-env.sh private-keys/api-test
```

#### New Pattern:
```yaml
- name: Load secrets from Infisical
  uses: ./.github/actions/load-infisical-secrets
  with:
    environment: ${{ env.INFISICAL_ENVIRONMENT }}
    client-id: ${{ secrets.INFISICAL_CLIENT_ID }}
    client-secret: ${{ secrets.INFISICAL_CLIENT_SECRET }}
    project-id: ${{ secrets.INFISICAL_PROJECT_ID }}
```

### Phase 3: Environment Mapping
Map GitHub workflow contexts to Infisical environments:

```yaml
env:
  INFISICAL_ENVIRONMENT: ${{
    github.base_ref == 'main' && 'production' ||
    github.base_ref == 'stage' && 'staging' ||
    github.base_ref == 'develop' && 'dev' ||
    'dev'
  }}
```

### Phase 4: Remove Dependencies
- Remove `PRIVATE_KEYS_FOLDER: "api-test"` environment variable
- Remove private-keys checkout steps
- Remove load-env.sh calls
- Update deployment scripts to expect environment variables instead of files

## 🔧 Implementation Steps

### Step 1: Add Infisical Environment Variables
Add to workflow env section:
```yaml
env:
  INFISICAL_ENVIRONMENT: ${{
    github.base_ref == 'main' && 'production' ||
    github.base_ref == 'stage' && 'staging' ||
    github.base_ref == 'develop' && 'dev' ||
    'dev'
  }}
```

### Step 2: Replace Secret Loading Steps
Find and replace these patterns:

**Pattern 1 - load-env.sh usage:**
```yaml
# BEFORE
- name: Load environment variables
  run: |
    chmod +x .github/scripts/load-env.sh
    .github/scripts/load-env.sh private-keys/api-test

# AFTER  
- name: Load secrets from Infisical
  uses: ./.github/actions/load-infisical-secrets
  with:
    environment: ${{ env.INFISICAL_ENVIRONMENT }}
    client-id: ${{ secrets.INFISICAL_CLIENT_ID }}
    client-secret: ${{ secrets.INFISICAL_CLIENT_SECRET }}
    project-id: ${{ secrets.INFISICAL_PROJECT_ID }}
```

**Pattern 2 - Private-keys references:**
```yaml
# BEFORE
env:
  PRIVATE_KEYS_FOLDER: "api-test"

# AFTER
# Remove this environment variable entirely
```

### Step 3: Update Deployment Scripts
Modify deployment scripts to expect environment variables instead of files:

**deploy/steps/3.deploy-new.sh** - Update to use env vars instead of file loading
**deploy/util/combine-secret-keys.sh** - Mark as deprecated, eventually remove

### Step 4: Test Integration
1. Add required GitHub repository secrets
2. Test with staging deployment first
3. Validate all services start correctly
4. Monitor for missing environment variables

## 🧪 Testing Strategy

### 1. Staging Test
- Deploy to staging environment using Infisical
- Verify all services start correctly
- Check logs for missing environment variables
- Validate API functionality

### 2. Production Validation
- Test production deployment in safe environment
- Ensure no service disruption
- Validate all critical secrets are available

## 📊 Benefits After Migration

### Security Improvements
- ✅ Centralized secret management
- ✅ Audit trail for secret access
- ✅ No more secrets in git submodules
- ✅ Automatic secret rotation support

### Operational Benefits
- ✅ Simplified workflow maintenance
- ✅ Consistent secret loading across environments
- ✅ Better secret organization and discovery
- ✅ Reduced complexity in deployment scripts

## 🚨 Rollback Plan

If issues occur during migration:
1. Revert workflow changes
2. Re-enable private-keys folder usage
3. Restore load-env.sh calls
4. Debug Infisical integration separately

## 📝 Next Actions

1. **Add GitHub repository secrets** (manual step)
2. **Update main workflow** with Infisical integration
3. **Test staging deployment** with new secret loading
4. **Update comment-actions workflow** 
5. **Remove private-keys dependencies**
6. **Update documentation**
