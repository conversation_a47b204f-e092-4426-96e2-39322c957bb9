#!/bin/bash

# Fast startup script for Divinci development environment
set -e

echo "🚀 Starting Divinci development environment (fast mode)..."

# Check if optimization has been run
if [ ! -d "hidden/mongodb" ]; then
    echo "📦 Running first-time optimization..."
    ./scripts/optimize-startup.sh
fi

# Start the fast docker compose
echo "🐳 Starting Docker services..."
cd docker && docker compose -f fast-local.yml up -d

echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service status
echo "📊 Service Status:"
docker compose -f fast-local.yml ps

echo ""
echo "✅ Development environment is ready!"
echo ""
echo "🌐 Web Client: http://localhost:8080"
echo "🗄️  MinIO Console: http://localhost:9001 (admin/minioadmin)"
echo "📊 MongoDB: localhost:27017"
echo ""
echo "💡 To stop: cd docker && docker compose -f fast-local.yml down"
echo "🔄 To restart: ./scripts/start-fast.sh"
