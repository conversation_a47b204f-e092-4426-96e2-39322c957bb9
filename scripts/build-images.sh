#!/bin/bash

# Build pre-optimized Docker images for faster startup
set -e

echo "🏗️  Building optimized Docker images..."

# Build API images
echo "📦 Building main API image..."
docker build -t divinci/api:latest \
  -f deploy/docker/local/api.Dockerfile \
  workspace/servers/public-api

echo "📦 Building API Live image..."
docker build -t divinci/api-live:latest \
  -f deploy/docker/local/api-live.Dockerfile \
  workspace/servers/public-api-live

echo "📦 Building API Webhook image..."
docker build -t divinci/api-webhook:latest \
  -f deploy/docker/local/api-webhook.Dockerfile \
  workspace/servers/public-api-webhook

# Build Worker Services
echo "📦 Building Open Parse image..."
docker build -t divinci/open-parse:latest \
  -f workspace/workers/open-parse/open-parse.Dockerfile \
  workspace/workers/open-parse

echo "📦 Building Audio Speaker Diarization image..."
docker build -t divinci/audio-pyannote:latest \
  -f workspace/workers/audio-speaker-diarization@pyannote/python.Dockerfile \
  workspace/workers/audio-speaker-diarization@pyannote

echo "📦 Building Audio Splitter image..."
docker build -t divinci/audio-splitter:latest \
  -f workspace/workers/audio-splitter@ffmpeg/typescript.Dockerfile \
  workspace/workers/audio-splitter@ffmpeg

echo "✅ All images built successfully!"
echo "🚀 You can now use 'docker compose -f docker/fast-local.yml up' for faster startup"
echo "📊 Image sizes:"
docker images | grep divinci/
