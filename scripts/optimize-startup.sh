#!/bin/bash

# Optimize Docker startup by pre-building dependencies and caching layers
set -e

echo "🚀 Optimizing Docker startup..."

# Step 1: Pre-build all workspace dependencies
echo "📦 Pre-building workspace dependencies..."
pnpm install
pnpm run prepare:all 2>/dev/null || echo "⚠️  Some packages failed to build, continuing..."

# Step 2: Create optimized docker-compose with better caching
echo "🐳 Creating optimized Docker configuration..."

# Step 3: Pre-pull base images
echo "📥 Pre-pulling Docker base images..."
docker pull node:22-bookworm-slim
docker pull node:22-bookworm
docker pull python:3.11.11-bookworm
docker pull python:3.13.1
docker pull mongo
docker pull redis
docker pull minio/minio
docker pull qdrant/qdrant

# Step 4: Create hidden directories for volume mounts
echo "📁 Creating volume directories..."
mkdir -p hidden/mongodb
mkdir -p hidden/redis
mkdir -p hidden/qdrant
mkdir -p hidden/servers/open-parse
mkdir -p hidden/workers/audio-speaker-diarization@pyannote
mkdir -p hidden/workers/audio-splitter@ffmpeg

echo "✅ Optimization complete!"
echo "🚀 You can now use 'docker compose -f docker/local.yml up' for faster startup"
echo "💡 First startup will still take time, but subsequent startups will be much faster"
