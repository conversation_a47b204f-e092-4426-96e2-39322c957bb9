import axios from "axios";

const PROMPTFOO_API_URL = process.env.PROMPTFOO_API_URL || "https://api.promptfoo.app";
const PROMPTFOO_API_TOKEN = process.env.PROMPTFOO_API_TOKEN;

function getAuthHeaders() {
  if (!PROMPTFOO_API_TOKEN) throw new Error("PROMPTFOO_API_TOKEN not set");
  return {
    Authorization: `Bearer ${PROMPTFOO_API_TOKEN}`,
  };
}

// Create a new redteam config
export async function createRedteamConfig(config) {
  const res = await axios.post(
    `${PROMPTFOO_API_URL}/api/v1/redteam/configs`,
    config,
    { headers: getAuthHeaders() }
  );
  return res.data;
}

// Start a new redteam evaluation job
export async function startRedteamJob(jobConfig) {
  const res = await axios.post(
    `${PROMPTFOO_API_URL}/api/v1/jobs`,
    jobConfig,
    { headers: getAuthHeaders() }
  );
  return res.data;
}

// Fetch redteam evaluation results
export async function getRedteamResults() {
  const res = await axios.get(
    `${PROMPTFOO_API_URL}/api/v1/results`,
    { headers: getAuthHeaders() }
  );
  return res.data;
}

// Fetch available plugins
export async function getRedteamPlugins() {
  const res = await axios.get(
    `${PROMPTFOO_API_URL}/api/v1/redteam/plugins`,
    { headers: getAuthHeaders() }
  );
  return res.data;
}
