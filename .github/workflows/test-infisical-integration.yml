name: Test Infisical Integration

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod

jobs:
  test-infisical:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Debug secrets directly
        run: |
          echo "🔍 Testing secrets directly..."
          echo "Client ID length: ${#CLIENT_ID}"
          echo "Client Secret length: ${#CLIENT_SECRET}"
          echo "Project ID length: ${#PROJECT_ID}"
          echo "Client ID starts with: ${CLIENT_ID:0:8}..."
          echo "Project ID starts with: ${PROJECT_ID:0:8}..."

          # Test direct API call
          echo "🔐 Testing direct API authentication..."
          AUTH_RESPONSE=$(curl -s -X POST "https://app.infisical.com/api/v1/auth/universal-auth/login" \
            -H "Content-Type: application/json" \
            -d "{\"clientId\": \"$CLIENT_ID\", \"clientSecret\": \"$CLIENT_SECRET\"}")

          echo "Auth response: $AUTH_RESPONSE"

          if echo "$AUTH_RESPONSE" | grep -q "accessToken"; then
            echo "✅ Direct API authentication successful!"
          else
            echo "❌ Direct API authentication failed"
            exit 1
          fi
        env:
          CLIENT_ID: ${{ secrets.INFISICAL_CLIENT_ID }}
          CLIENT_SECRET: ${{ secrets.INFISICAL_CLIENT_SECRET }}
          PROJECT_ID: ${{ secrets.INFISICAL_PROJECT_ID }}

      - name: Load secrets using custom action
        uses: ./.github/actions/load-infisical-secrets
        with:
          environment: ${{ inputs.environment }}
          client-id: ${{ secrets.INFISICAL_CLIENT_ID }}
          client-secret: ${{ secrets.INFISICAL_CLIENT_SECRET }}
          project-id: ${{ secrets.INFISICAL_PROJECT_ID }}

      - name: Test secret access
        run: |
          echo "🔍 Testing if secrets were properly exported by custom action..."

          # Test for common secrets that should be present
          secrets_found=0

          if [ -n "$MONGODB_URI" ] || [ -n "$MONGO_DOMAIN_HOSTNAME" ]; then
            echo "✅ MongoDB secrets loaded"
            ((secrets_found++))
          fi

          if [ -n "$REDIS_REDIS_PASSWORD" ] || [ -n "$REDIS_REDIS_USERNAME" ] || [ -n "$REDIS_REDIS_PORT" ]; then
            echo "✅ Redis secrets loaded"
            ((secrets_found++))
          fi

          if [ -n "$OPENAI_API_KEY" ]; then
            echo "✅ OpenAI secrets loaded"
            ((secrets_found++))
          fi

          if [ -n "$CLOUDFLARE_API_TOKEN" ] || [ -n "$CLOUDFLARE_ACCOUNT_ID" ]; then
            echo "✅ Cloudflare secrets loaded"
            ((secrets_found++))
          fi

          if [ -n "$AUTH0_CLIENT_ID" ] || [ -n "$AUTH0_BASE_URL" ]; then
            echo "✅ Auth0 secrets loaded"
            ((secrets_found++))
          fi

          echo "📊 Found $secrets_found critical secret groups"

          if [ $secrets_found -lt 3 ]; then
            echo "❌ Expected more critical secrets. Custom action may have failed."
            exit 1
          else
            echo "✅ Custom action successfully loaded secrets!"
          fi

          # Test environment-specific secrets
          case "${{ inputs.environment }}" in
            "dev")
              echo "🧪 Testing development environment secrets"
              ;;
            "staging")
              echo "🎭 Testing staging environment secrets"
              ;;
            "prod")
              echo "🚀 Testing production environment secrets"
              ;;
          esac

          echo "🎉 Infisical integration test completed for ${{ inputs.environment }}!"

      - name: Compare with current secret loading
        run: |
          echo "📋 Current workflow uses combine-secret-keys.sh"
          echo "🔄 This test demonstrates Infisical replacement"
          echo "✨ Next step: Update main workflows to use Infisical"
