#!/bin/bash
# Script to run act tests for GitHub Actions workflows with Ubuntu runner

# Change to repository root directory
cd ../../..

# Set up the command
CMD="ACT=true act issue_comment -e .github/act/events/deploy-comment-event.json --secret-file .github/act/secrets.env --container-architecture linux/amd64 -P ubuntu-latest=catthehacker/ubuntu:act-latest -j handle-deploy-command --workflows .github/workflows/comment-actions.yml"

# Run the command
echo "🚀 Running act with the following command:"
echo "$CMD"
echo ""

# Run the command
eval "$CMD"
