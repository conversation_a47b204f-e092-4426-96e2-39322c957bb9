#!/bin/bash
# Script to run act tests for GitHub Actions workflows

# Default values
EVENT_TYPE="issue_comment"
EVENT_FILE="../events/deploy-comment-event.json"
SECRETS_FILE="../secrets.env"
JOB=""
PLATFORM="ubuntu-latest=catthehacker/ubuntu:act-latest"

# Display help message
show_help() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  -e, --event TYPE     Event type (default: issue_comment)"
  echo "  -f, --file FILE      Event payload file (default: ../events/deploy-comment-event.json)"
  echo "  -s, --secrets FILE   Secrets file (default: ../secrets.env)"
  echo "  -j, --job JOB        Specific job to run (default: all jobs)"
  echo "  -p, --platform PLAT  Platform to use (default: ubuntu-latest=catthehacker/ubuntu:act-latest)"
  echo "  -h, --help           Show this help message"
  echo ""
  echo "Example:"
  echo "  $0 --job handle-deploy-command"
  echo "  $0 --event workflow_dispatch --file events/workflow-dispatch-event.json"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -e|--event)
      EVENT_TYPE="$2"
      shift 2
      ;;
    -f|--file)
      EVENT_FILE="$2"
      shift 2
      ;;
    -s|--secrets)
      SECRETS_FILE="$2"
      shift 2
      ;;
    -j|--job)
      JOB="$2"
      shift 2
      ;;
    -p|--platform)
      PLATFORM="$2"
      shift 2
      ;;
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      show_help
      exit 1
      ;;
  esac
done

# Check if act is installed
if ! command -v act &> /dev/null; then
  echo "❌ Error: act is not installed"
  echo "Please install act: https://github.com/nektos/act#installation"
  exit 1
fi

# Check if files exist
if [ ! -f "$EVENT_FILE" ]; then
  echo "❌ Error: Event file not found: $EVENT_FILE"
  exit 1
fi

if [ ! -f "$SECRETS_FILE" ]; then
  echo "❌ Error: Secrets file not found: $SECRETS_FILE"
  exit 1
fi

# Build the command with ACT=true environment variable
# Convert relative paths to absolute paths from repo root
if [[ "$EVENT_FILE" == ../* ]]; then
  EVENT_PATH=".github/act/${EVENT_FILE#../}"
else
  EVENT_PATH="$EVENT_FILE"
fi

if [[ "$SECRETS_FILE" == ../* ]]; then
  SECRETS_PATH=".github/act/${SECRETS_FILE#../}"
else
  SECRETS_PATH="$SECRETS_FILE"
fi

CMD="ACT=true act $EVENT_TYPE -e $EVENT_PATH --secret-file $SECRETS_PATH --container-architecture linux/amd64 -P $PLATFORM"

# Add job if specified
if [ -n "$JOB" ]; then
  CMD="$CMD -j $JOB"
fi

# Run the command
echo "🚀 Running act with the following command:"
echo "$CMD"
echo ""

# Change to repository root directory
cd ../../..

# Run the command
eval "$CMD"
