#!/bin/bash
# Script to test the new workflows with act

# Change to repository root directory
cd ../../..

echo "🚀 Testing comment-actions.yml workflow..."
act issue_comment \
  -W .github/workflows/comment-actions.yml \
  -e .github/act/tests/test-event.json \
  --container-architecture linux/amd64 \
  -P ubuntu-latest=nektos/act-environments-ubuntu:18.04 \
  -s GITHUB_TOKEN=dummy-token \
  -s ELEVATE_PR_PAT=dummy-token \
  -s DIVINCI_GH_PAT_3=dummy-token \
  -s GCP_SA_KEY=dummy-key

echo "✅ Test completed!"
