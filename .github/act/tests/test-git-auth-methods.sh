#!/bin/bash
# Script to test different Git authentication methods

# Load GitHub token from secrets file
source secrets.env

echo "🔍 Testing different Git authentication methods..."

# Create a temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"
echo "📁 Working in temporary directory: $TEMP_DIR"

# Method 1: Using GitHub CLI directly
echo "🧪 Method 1: Using GitHub CLI directly"
export GH_TOKEN="${GITHUB_TOKEN}"
if gh repo clone Divinci-AI/server method1 --depth 1; then
  echo "✅ Method 1 successful!"
else
  echo "❌ Method 1 failed."
fi
rm -rf method1

# Method 2: Using Git with credential.helper=store
echo "🧪 Method 2: Using Git with credential.helper=store"
git config --global credential.helper store
echo "https://x-access-token:${GITHUB_TOKEN}@github.com" > ~/.git-credentials
mkdir method2
cd method2
git init
git remote add origin "https://github.com/Divinci-AI/server"
if git fetch --depth 1 origin; then
  echo "✅ Method 2 successful!"
else
  echo "❌ Method 2 failed."
fi
cd ..
rm -rf method2

# Method 3: Using Git with insteadOf configuration
echo "🧪 Method 3: Using Git with insteadOf configuration"
git config --global --unset-all credential.helper
git config --global url."https://x-access-token:${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"
mkdir method3
cd method3
git init
git remote add origin "https://github.com/Divinci-AI/server"
if git fetch --depth 1 origin; then
  echo "✅ Method 3 successful!"
else
  echo "❌ Method 3 failed."
fi
cd ..
rm -rf method3

# Method 4: Using Git with OAuth2 format
echo "🧪 Method 4: Using Git with OAuth2 format"
git config --global --unset-all url."https://x-access-token:${GITHUB_TOKEN}@github.com/".insteadOf
git config --global url."https://oauth2:${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"
mkdir method4
cd method4
git init
git remote add origin "https://github.com/Divinci-AI/server"
if git fetch --depth 1 origin; then
  echo "✅ Method 4 successful!"
else
  echo "❌ Method 4 failed."
fi
cd ..
rm -rf method4

# Method 5: Using Git with direct URL embedding
echo "🧪 Method 5: Using Git with direct URL embedding"
git config --global --unset-all url."https://oauth2:${GITHUB_TOKEN}@github.com/".insteadOf
mkdir method5
cd method5
git init
if git remote add origin "https://x-access-token:${GITHUB_TOKEN}@github.com/Divinci-AI/server" && git fetch --depth 1 origin; then
  echo "✅ Method 5 successful!"
else
  echo "❌ Method 5 failed."
fi
cd ..
rm -rf method5

# Method 6: Using Git with direct URL embedding (OAuth2 format)
echo "🧪 Method 6: Using Git with direct URL embedding (OAuth2 format)"
mkdir method6
cd method6
git init
if git remote add origin "https://oauth2:${GITHUB_TOKEN}@github.com/Divinci-AI/server" && git fetch --depth 1 origin; then
  echo "✅ Method 6 successful!"
else
  echo "❌ Method 6 failed."
fi
cd ..
rm -rf method6

# Method 7: Using Git with username:token format
echo "🧪 Method 7: Using Git with username:token format"
mkdir method7
cd method7
git init
if git remote add origin "https://${GITHUB_TOKEN}:<EMAIL>/Divinci-AI/server" && git fetch --depth 1 origin; then
  echo "✅ Method 7 successful!"
else
  echo "❌ Method 7 failed."
fi
cd ..
rm -rf method7

# Clean up
cd -
rm -rf "$TEMP_DIR"
echo "🧹 Cleaned up temporary directory"

echo "✅ All tests completed!"
