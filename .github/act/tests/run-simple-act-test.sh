#!/bin/bash
# Simple script to test the refactored workflow with act on ubuntu runner

# Create a temporary event file
EVENT_FILE=$(mktemp)
cat > "$EVENT_FILE" << EOF
{
  "inputs": {
    "pr_number": "123",
    "base_ref": "develop",
    "triggered_by_comment": true,
    "changed_folders": "workspace/servers/public-api",
    "is_fast_deploy": true,
    "comment": "@github-actions [deploy:develop:fail-tests]"
  }
}
EOF

# Change to repository root directory
cd ../../..

echo "🚀 Running act test with ubuntu runner..."
act workflow_dispatch \
  -W .github/act/tests/deploy-services-act-test.yml \
  -e "$EVENT_FILE" \
  --container-architecture linux/amd64 \
  -P ubuntu-latest=nektos/act-environments-ubuntu:18.04 \
  -s GITHUB_TOKEN=dummy-token \
  -s ELEVATE_PR_PAT=dummy-token \
  -s GCP_SA_KEY=dummy-key

# Clean up
rm "$EVENT_FILE"
echo "✅ Test completed!"
