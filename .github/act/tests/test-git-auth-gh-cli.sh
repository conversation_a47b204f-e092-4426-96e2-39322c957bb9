#!/bin/bash
# Script to test the new Git authentication method using GitHub CLI

# Load GitHub token from secrets file
source secrets.env

echo "🔍 Testing new Git authentication method with GitHub CLI..."

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) is not installed. Please install it first."
    exit 1
fi

# Create a temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"
echo "📁 Working in temporary directory: $TEMP_DIR"

# Authenticate with GitHub CLI
echo "🔑 Authenticating with GitHub CLI..."
echo "$GITHUB_TOKEN" | gh auth login --with-token

# Configure git to use GitHub CLI as credential helper
echo "⚙️ Configuring git to use GitHub CLI..."
git config --global credential.helper "gh auth git-credential"

# Initialize a new git repository
echo "🔄 Initializing git repository..."
git init

# Set up the remote without embedding token in URL
echo "🔗 Setting up remote..."
git remote add origin "https://github.com/Divinci-AI/server"

# Try to fetch from the repository
echo "📥 Fetching from repository..."
if git fetch origin; then
  echo "✅ Git fetch successful! The new authentication method works."
else
  echo "❌ Git fetch failed. The new authentication method is not working."
  exit 1
fi

# Try to clone the private-keys repository as a submodule
echo "🔑 Testing submodule authentication..."
if git submodule add https://github.com/Divinci-AI/private-keys.git private-keys; then
  echo "✅ Submodule add successful! The new authentication method works for submodules."
else
  echo "❌ Submodule add failed. The new authentication method is not working for submodules."
  exit 1
fi

# Clean up
cd -
rm -rf "$TEMP_DIR"
echo "🧹 Cleaned up temporary directory"

echo "✅ All tests passed! The new Git authentication method works correctly."
