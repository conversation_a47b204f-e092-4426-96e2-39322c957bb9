name: Deploy to Google Cloud Run (Act Test)

on:
  workflow_dispatch:
    inputs:
      pr_number:
        description: "PR number"
        required: true
      base_ref:
        description: "Base branch reference"
        required: true
      triggered_by_comment:
        description: "Whether triggered by comment"
        required: true
        type: boolean
      changed_folders:
        description: "Comma-separated list of folders to deploy"
        required: false
      is_fast_deploy:
        description: "Whether to run fast deployment"
        required: false
        type: boolean
        default: false
      comment:
        description: "Original deployment command."
        required: false

env:
  RUNNER_LABELS: "self-hosted,linux,docker,X64"
  LABELS: "self-hosted,linux,docker,X64"
  GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
  CI: true
  BASE_REF: ${{ inputs.base_ref || 'develop' }}
  WEB_CLIENT_HOST: "chat.dev.divinci.app"
  ENVIRONMENT: "develop"
  NODE_ENV: "develop"
  ACT: "true"

jobs:
  # For act testing, we'll use a simplified workflow
  act-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Debug Inputs
        run: |
          echo "🔍 Workflow Inputs:"
          echo "PR Number: ${{ inputs.pr_number }}"
          echo "Base Ref: ${{ inputs.base_ref }}"
          echo "Triggered by Comment: ${{ inputs.triggered_by_comment }}"
          echo "Changed Folders: ${{ inputs.changed_folders }}"
          echo "Is Fast Deploy: ${{ inputs.is_fast_deploy }}"
          echo "Comment: ${{ inputs.comment }}"

      - name: Simulate Check Deploy Flag
        id: check-deploy-flag
        run: |
          echo "should_deploy=true" >> $GITHUB_OUTPUT
          echo "is_fast_deploy=${{ inputs.is_fast_deploy }}" >> $GITHUB_OUTPUT
          echo "changed_folders=${{ inputs.changed_folders }}" >> $GITHUB_OUTPUT
          echo "target_branch=${{ inputs.base_ref }}" >> $GITHUB_OUTPUT
          
          echo "✅ Deploy flag check completed"
          echo "Should Deploy: true"
          echo "Is Fast Deploy: ${{ inputs.is_fast_deploy }}"
          echo "Changed Folders: ${{ inputs.changed_folders }}"
          echo "Target Branch: ${{ inputs.base_ref }}"

      - name: Simulate Service Deployment
        run: |
          CHANGED_FOLDERS="${{ inputs.changed_folders }}"
          
          if [ -z "$CHANGED_FOLDERS" ]; then
            echo "⚠️ No folders specified for deployment"
          else
            echo "🚀 Simulating deployment of services:"
            
            # Split the comma-separated list
            IFS=',' read -ra FOLDERS <<< "$CHANGED_FOLDERS"
            
            for FOLDER in "${FOLDERS[@]}"; do
              echo "  - Deploying $FOLDER to ${{ env.ENVIRONMENT }} environment"
              echo "    ✅ Deployment successful!"
            done
          fi

      - name: Simulate API Tests
        if: ${{ inputs.is_fast_deploy != true }}
        run: |
          echo "🧪 Simulating API tests for deployed services"
          echo "✅ All tests passed!"

      - name: Simulate Cleanup
        run: |
          echo "🧹 Simulating cleanup of resources"
          echo "✅ Cleanup successful!"
