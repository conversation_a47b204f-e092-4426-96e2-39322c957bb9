name: Minimal Test Workflow

on:
  workflow_dispatch:
    inputs:
      pr_number:
        description: "PR number"
        required: true
        default: "123"

jobs:
  test-job:
    runs-on: ubuntu-latest
    steps:
      - name: Echo Inputs
        run: |
          echo "PR Number: ${{ inputs.pr_number }}"
          echo "This is a minimal test of the refactored workflow system"
          echo "✅ Test successful!"
