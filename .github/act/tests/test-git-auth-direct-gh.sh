#!/bin/bash
# <PERSON>ript to test Git operations using GitHub CLI directly

# Load GitHub token from secrets file
source secrets.env

echo "🔍 Testing Git operations using GitHub CLI directly..."

# Create a temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"
echo "📁 Working in temporary directory: $TEMP_DIR"

# Set the GitHub token as an environment variable
export GH_TOKEN="${GITHUB_TOKEN}"
echo "🔑 Set GH_TOKEN environment variable"

# Use GitHub CLI to clone the repository
echo "📥 Cloning repository using GitHub CLI..."
if gh repo clone Divinci-AI/server .; then
  echo "✅ Repository clone successful!"
else
  echo "❌ Repository clone failed."
  exit 1
fi

# Try to clone the private-keys repository as a submodule
echo "🔑 Testing submodule clone using GitHub CLI..."
if gh repo clone Divinci-AI/private-keys private-keys; then
  echo "✅ Private-keys repository clone successful!"
else
  echo "❌ Private-keys repository clone failed."
  exit 1
fi

# Clean up
cd -
rm -rf "$TEMP_DIR"
echo "🧹 Cleaned up temporary directory"

echo "✅ All tests passed! Using GitHub CLI directly for Git operations works correctly."
