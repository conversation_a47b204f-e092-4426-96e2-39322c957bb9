#!/bin/bash
# Script to test the refactored workflows with act

# Set up environment
echo "🔧 Setting up environment for act test..."
export ACT=true
export GITHUB_TOKEN="${GITHUB_TOKEN:-dummy-token-for-testing}"
export ELEVATE_PR_PAT="${ELEVATE_PR_PAT:-dummy-token-for-testing}"
export DIVINCI_GH_PAT_3="${DIVINCI_GH_PAT_3:-dummy-token-for-testing}"
export GCP_SA_KEY="${GCP_SA_KEY:-dummy-key-for-testing}"

# Create a temporary directory for act artifacts
TEMP_DIR=$(mktemp -d)
echo "📁 Created temporary directory: $TEMP_DIR"

# Function to clean up on exit
cleanup() {
  echo "🧹 Cleaning up..."
  rm -rf "$TEMP_DIR"
  echo "✅ Cleanup complete"
}
trap cleanup EXIT

# Change to repository root directory
cd ../../..

# Determine which workflow to test
if [ "$1" == "comment" ]; then
  WORKFLOW=".github/workflows/comment-actions-new.yml"
  EVENT="issue_comment"
  echo "🚀 Testing comment-actions-new.yml workflow..."
else
  WORKFLOW=".github/workflows/deploy-services.yml"
  EVENT="workflow_dispatch"
  echo "🚀 Testing deploy-services.yml workflow..."
fi

# Create event payload for act
cat > "$TEMP_DIR/event.json" << EOF
{
  "inputs": {
    "pr_number": "123",
    "base_ref": "develop",
    "triggered_by_comment": true,
    "changed_folders": "workspace/servers/public-api",
    "is_fast_deploy": true,
    "comment": "@github-actions [deploy:develop:fail-tests]"
  },
  "issue": {
    "number": 123,
    "pull_request": {}
  },
  "comment": {
    "body": "@github-actions [deploy:develop:fail-tests]"
  },
  "repository": {
    "full_name": "Divinci-AI/server"
  }
}
EOF

# Run act with the specified workflow
echo "🏃 Running act with $WORKFLOW..."
act \
  --container-architecture linux/amd64 \
  -W "$WORKFLOW" \
  -e "$TEMP_DIR/event.json" \
  -s GITHUB_TOKEN="$GITHUB_TOKEN" \
  -s ELEVATE_PR_PAT="$ELEVATE_PR_PAT" \
  -s DIVINCI_GH_PAT_3="$DIVINCI_GH_PAT_3" \
  -s GCP_SA_KEY="$GCP_SA_KEY" \
  --bind \
  -P ubuntu-latest=nektos/act-environments-ubuntu:18.04 \
  -P self-hosted=nektos/act-environments-ubuntu:18.04 \
  --job simulate-self-hosted \
  "$EVENT"

echo "✅ Act test completed!"
