name: 'Load Infisical Secrets'
description: 'Load secrets from Infisical for specified environment'
author: 'Divinci AI'

inputs:
  environment:
    description: 'Environment to load secrets from (dev, staging, prod)'
    required: true
  path:
    description: 'Secret path in Infisical (default: /)'
    required: false
    default: '/'
  client-id:
    description: 'Infisical client ID'
    required: true
  client-secret:
    description: 'Infisical client secret'
    required: true
  project-id:
    description: 'Infisical project ID'
    required: true

outputs:
  secrets-loaded:
    description: 'Number of secrets loaded from Infisical'
    value: ${{ steps.load-secrets.outputs.secrets-loaded }}

runs:
  using: 'composite'
  steps:
    - name: Debug Infisical inputs
      shell: bash
      run: |
        echo "🔍 Debug Infisical configuration:"
        echo "Environment: ${{ inputs.environment }}"
        echo "Path: ${{ inputs.path }}"
        echo "Project ID: ${{ inputs.project-id }}"
        echo "Client ID: ${{ inputs.client-id }}"
        echo "Client Secret: [REDACTED]"

    - name: Install jq
      shell: bash
      run: |
        sudo apt-get update && sudo apt-get install -y jq

    - name: Debug GitHub secrets
      shell: bash
      run: |
        echo "🔍 Debugging GitHub secrets..."
        echo "Client ID length: ${#INFISICAL_CLIENT_ID}"
        echo "Client Secret length: ${#INFISICAL_CLIENT_SECRET}"
        echo "Project ID length: ${#INFISICAL_PROJECT_ID}"
        echo "Client ID starts with: ${INFISICAL_CLIENT_ID:0:8}..."
        echo "Project ID starts with: ${INFISICAL_PROJECT_ID:0:8}..."
      env:
        INFISICAL_CLIENT_ID: ${{ inputs.client-id }}
        INFISICAL_CLIENT_SECRET: ${{ inputs.client-secret }}
        INFISICAL_PROJECT_ID: ${{ inputs.project-id }}

    - name: Load secrets using Infisical API
      id: load-secrets
      shell: bash
      run: |
        echo "🔐 Loading secrets from Infisical API..."

        # Step 1: Validate environment variables
        echo "Step 1: Validate environment variables"
        echo "CLIENT_ID length: ${#CLIENT_ID}"
        echo "CLIENT_SECRET length: ${#CLIENT_SECRET}"
        echo "PROJECT_ID length: ${#PROJECT_ID}"
        echo "Environment: ${{ inputs.environment }}"

        # Step 2: Test authentication
        echo "Step 2: Test authentication"
        AUTH_RESPONSE=$(curl -s -X POST "https://app.infisical.com/api/v1/auth/universal-auth/login" \
          -H "Content-Type: application/json" \
          -d "{\"clientId\":\"$CLIENT_ID\",\"clientSecret\":\"$CLIENT_SECRET\"}")

        ACCESS_TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.accessToken')

        if [ "$ACCESS_TOKEN" = "null" ] || [ -z "$ACCESS_TOKEN" ]; then
          echo "❌ Failed to get access token"
          echo "Auth response: $AUTH_RESPONSE"
          exit 1
        fi

        echo "✅ Successfully authenticated with Infisical"
        echo "Access token length: ${#ACCESS_TOKEN}"

        # Step 3: Test secrets API endpoint
        echo "Step 3: Test secrets API endpoint"
        SECRETS_URL="https://app.infisical.com/api/v3/secrets/raw?workspaceId=$PROJECT_ID&environment=${{ inputs.environment }}&secretPath=${{ inputs.path }}"
        echo "Secrets URL: $SECRETS_URL"

        SECRETS_RESPONSE=$(curl -s -X GET "$SECRETS_URL" \
          -H "Authorization: Bearer $ACCESS_TOKEN")

        if echo "$SECRETS_RESPONSE" | jq -e '.secrets' > /dev/null 2>&1; then
          SECRET_COUNT=$(echo "$SECRETS_RESPONSE" | jq '.secrets | length')
          echo "✅ Successfully fetched $SECRET_COUNT secrets"

          # Step 4: Export secrets to environment variables
          echo "Step 4: Export secrets to environment variables"
          if [ "$SECRET_COUNT" -gt 0 ]; then
            # Use a more robust approach for multi-line values
            echo "$SECRETS_RESPONSE" | jq -r '.secrets[] | @base64' | while read -r secret; do
              decoded=$(echo "$secret" | base64 -d)
              key=$(echo "$decoded" | jq -r '.secretKey')
              value=$(echo "$decoded" | jq -r '.secretValue')

              # Handle multi-line values properly using GitHub's delimiter syntax
              if [[ "$value" == *$'\n'* ]]; then
                delimiter="EOF_$(openssl rand -hex 8)"
                echo "${key}<<${delimiter}" >> $GITHUB_ENV
                echo "$value" >> $GITHUB_ENV
                echo "$delimiter" >> $GITHUB_ENV
              else
                echo "${key}=${value}" >> $GITHUB_ENV
              fi
            done
            echo "✅ Successfully exported $SECRET_COUNT secrets to environment"
            echo "secrets-loaded=$SECRET_COUNT" >> $GITHUB_OUTPUT
          else
            echo "⚠️ No secrets found in environment ${{ inputs.environment }}"
            echo "secrets-loaded=0" >> $GITHUB_OUTPUT
          fi
        else
          echo "❌ Failed to fetch secrets or invalid response format"
          echo "Response: $SECRETS_RESPONSE"
          exit 1
        fi
      env:
        CLIENT_ID: ${{ inputs.client-id }}
        CLIENT_SECRET: ${{ inputs.client-secret }}
        PROJECT_ID: ${{ inputs.project-id }}

    - name: Validate secret loading
      shell: bash
      run: |
        echo "🔍 Validating Infisical secret loading for environment: ${{ inputs.environment }}"

        # Test for common secrets that should be present
        secrets_found=0

        if [ -n "$MONGODB_URI" ] || [ -n "$MONGO_DOMAIN_HOSTNAME" ]; then
          echo "✅ MongoDB secrets loaded"
          secrets_found=$((secrets_found + 1))
        fi

        echo "🔍 Debug Redis variables:"
        echo "REDIS_REDIS_PASSWORD: ${REDIS_REDIS_PASSWORD:-'NOT_SET'}"
        echo "REDIS_REDIS_USERNAME: ${REDIS_REDIS_USERNAME:-'NOT_SET'}"
        echo "REDIS_REDIS_PORT: ${REDIS_REDIS_PORT:-'NOT_SET'}"

        echo "🔍 Testing Redis conditions individually:"
        if [ -n "$REDIS_REDIS_PASSWORD" ]; then
          echo "✅ REDIS_REDIS_PASSWORD is set"
        else
          echo "❌ REDIS_REDIS_PASSWORD is not set"
        fi

        if [ -n "$REDIS_REDIS_USERNAME" ]; then
          echo "✅ REDIS_REDIS_USERNAME is set"
        else
          echo "❌ REDIS_REDIS_USERNAME is not set"
        fi

        if [ -n "$REDIS_REDIS_PORT" ]; then
          echo "✅ REDIS_REDIS_PORT is set"
        else
          echo "❌ REDIS_REDIS_PORT is not set"
        fi

        echo "🔍 Testing combined Redis condition:"
        if [ -n "$REDIS_REDIS_PASSWORD" ] || [ -n "$REDIS_REDIS_USERNAME" ] || [ -n "$REDIS_REDIS_PORT" ]; then
          echo "✅ Redis secrets loaded"
          secrets_found=$((secrets_found + 1))
        else
          echo "❌ Redis secrets not found"
        fi

        echo "🔍 Current secrets_found count: $secrets_found"

        if [ -n "$OPENAI_API_KEY" ]; then
          echo "✅ OpenAI secrets loaded"
          secrets_found=$((secrets_found + 1))
        fi

        if [ -n "$CLOUDFLARE_API_TOKEN" ] || [ -n "$CLOUDFLARE_ACCOUNT_ID" ]; then
          echo "✅ Cloudflare secrets loaded"
          secrets_found=$((secrets_found + 1))
        fi

        if [ -n "$AUTH0_CLIENT_ID" ] || [ -n "$AUTH0_BASE_URL" ]; then
          echo "✅ Auth0 secrets loaded"
          secrets_found=$((secrets_found + 1))
        fi

        echo "📊 Found $secrets_found critical secret groups"

        if [ $secrets_found -lt 3 ]; then
          echo "⚠️ Warning: Expected more critical secrets. Please verify Infisical configuration."
        else
          echo "🎉 Secret loading validation successful!"
        fi

branding:
  icon: 'lock'
  color: 'blue'
