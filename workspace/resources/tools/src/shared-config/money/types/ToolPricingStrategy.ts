// We'll use BigInt for prices, but you could use Decimal or another precise type.

import { ShallowObject } from "@divinci-ai/utils";

export type CostEstimationStatus = "exact" | "unknown" | "maximum" | "minimum";

export type ToolInitialCosts = {
  inputCost: bigint,
  outputEscrow: bigint,
  costStatus: CostEstimationStatus,
};

export type ToolFinalCosts = {
  outputCost: bigint,
};

export interface ToolPricingStrategy<
  Config extends ShallowObject, InputArgs, OutputResult
> {
  /**
   * Calculate the necessary cost for input usage,
   * plus how much to escrow for the output usage if needed.
   *
   * @param input - Data required to estimate the cost, such as file size, number of pages,
   *   or worst-case token usage.
   * @returns An object containing:
   *   - inputCost: The fixed cost charged immediately for the input usage.
   *   - outputEscrow: The maximum or estimated cost to escrow for variable output usage.
   */
  getEstimatedCost(config: Config, input: InputArgs): Promise<ToolInitialCosts>,

  /**
   * Calculates the final cost after the task is complete,
   * typically based on actual resource usage.
   *
   * If the final cost differs from escrowOutputCost,
   * you'll either refund or charge the difference in your transaction manager.
   *
   * @param input - The original input used to generate the result.
   * @param output - The actual output or result used to compute final usage.
   * @return The final total cost for the usage actual output
   */
  getFinalCost(config: Config, input: InputArgs, output: OutputResult): Promise<ToolFinalCosts>,

}

export function mixCostStatus(costs: Array<ToolInitialCosts>): CostEstimationStatus{
  const statuses = new Set(costs.map((item)=>item.costStatus));
  if(statuses.has("unknown")) return "unknown";
  if(statuses.has("minimum")) return "minimum";
  if(statuses.has("maximum")) return "maximum";
  return "exact";
}

