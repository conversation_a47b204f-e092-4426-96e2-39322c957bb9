
export * from "./types";
import { OpenAIGPT4oMiniFineTune } from "./openai/gpt-4o-mini";
import { OpenAIGPT4oFineTune } from "./openai/gpt-4o";
export { OpenAIGPT4oMiniFineTune, OpenAIGPT4oFineTune };

import { AssistantFineTuneBase } from "./types";

export const FINETUNE_TOOLS: Record<string, AssistantFineTuneBase<any>> = {
  [OpenAIGPT4oMiniFineTune.id]: OpenAIGPT4oMiniFineTune,
  [OpenAIGPT4oFineTune.id]: OpenAIGPT4oFineTune,
};
