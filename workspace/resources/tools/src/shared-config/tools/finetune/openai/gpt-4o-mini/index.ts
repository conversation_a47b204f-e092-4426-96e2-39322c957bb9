
import { ToolType } from "./type";
import { ToolAvailableStatus } from "../../../../types";
import { PRICING } from "./pricing";
import { INFO } from "./info";
import { FinetuneToolTypeName } from "../../types";
import { AI_CATEGORY_ENUM } from "@divinci-ai/models";

export const OpenAIGPT4oMiniFineTune: ToolType = {
  toolType: FinetuneToolTypeName,
  id: INFO.id,
  status: ToolAvailableStatus.AVAILABLE,

  info: INFO,
  pricing: PRICING,
  meta: {
    assistantCategory: AI_CATEGORY_ENUM.TEXT,
    baseAssistantName: "gpt-4o-mini-2024-07-18",
  },
};
