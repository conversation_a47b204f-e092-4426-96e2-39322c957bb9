
import { ToolType } from "./type";

import { NANO_MULTIPLIER } from "../../../../money";
import { countTokens } from "gpt-tokenizer/model/gpt-4o";
import { calculateTokens } from "../common/pricing";

// https://platform.openai.com/docs/pricing#fine-tuning
// $25.00 / 1,000,000
// When we have a million tokens, we get charged $25
const COST_PER_TOKEN = 25n * NANO_MULTIPLIER / 1_000_000n;

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, { threads }){
    return {
      inputCost: COST_PER_TOKEN * calculateTokens(countTokens, { threads }),
      outputEscrow: 0n,
      costStatus: "exact",
    };
  },

  async getFinalCost(){
    return {
      outputCost: 0n
    };
  },

};

