import { ShallowObject } from "@divinci-ai/utils";
import { ToolConfig } from "../../types";
import { AI_CATEGORY_ENUM } from "@divinci-ai/models";


type Thread = Array<{ prompt: string, response: string }>;

export type FinetuneInput = {
  threads: Array<Thread>,
};

export type FinetuneOutput = {
  newModelName: string,
};

export const FinetuneToolTypeName = "AssistantFineTune";
export type FinetuneToolTypeName = "AssistantFineTune";

export type FineTuneMetaData = {
  assistantCategory: AI_CATEGORY_ENUM,
  baseAssistantName: string,
};


export type AssistantFineTuneBase<Config extends ShallowObject> = (
  ToolConfig<
    FinetuneToolTypeName, Config,
    FinetuneInput, FinetuneOutput,
    FineTuneMetaData
  >
);
