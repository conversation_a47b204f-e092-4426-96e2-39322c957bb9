
export * from "./types";
import { URLScraperBase } from "./types";

import { DivinciFetchScrape } from "./divinci-fetch";
import { MendableFirecrawl } from "./mendable-firecrawl";

export {
  DivinciFetchScrape as URLScraperDivinciFetchScrape,
  MendableFirecrawl as URLScraperMendableFirecrawl
};

export const RAG_URL_SCRAPE: Record<string, URLScraperBase<any>> = {
  [DivinciFetchScrape.id]: DivinciFetchScrape,
  [MendableFirecrawl.id]: MendableFirecrawl,
};
