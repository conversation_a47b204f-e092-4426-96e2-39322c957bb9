export * from "./types";

import { RawToChunksBase } from "./types";
export { RawToChunksInput, RawToChunksOutput } from "./types";

import { RawToChunksDivinciOpenparse } from "./divinci-openparse";
export * from "./divinci-openparse";
import { RawToChunksUnstructured } from "./unstructured";
export * from "./unstructured";


export const RAG_RAW_TO_CHUNKS: Record<string, RawToChunksBase<any>> = {
  [RawToChunksDivinciOpenparse.id]: RawToChunksDivinciOpenparse,
  [RawToChunksUnstructured.id]: RawToChunksUnstructured,
};
