
import { ToolType } from "./type";

import { NANO_MULTIPLIER } from "../../../../../money";

// https://platform.openai.com/docs/pricing#image-generation
// $0.02 per Image
const COST_PER_IMAGE = NANO_MULTIPLIER * 2n / 100n;


export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(){
    return {
      inputCost: COST_PER_IMAGE,
      outputEscrow: 0n,
      costStatus: "exact",
    };
  },

  async getFinalCost(){
    return {
      outputCost: 0n
    };
  },
};

