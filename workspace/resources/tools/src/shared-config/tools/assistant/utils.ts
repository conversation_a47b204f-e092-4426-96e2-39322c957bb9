
import { AIAssistantInfoItem, AIAssistantInfoMap } from "@divinci-ai/models";
import { GeneratorAssistantBase } from "./types";
export function viewableAssistants(
  assistants: Record<string, GeneratorAssistantBase<any>>
): AIAssistantInfoMap{
  const entries = Object.values(assistants);
  if(entries.length === 0){
    throw new Error("No Entries Available");
  }
  const infoMap: AIAssistantInfoMap = {};
  for(const entry of entries){
    if(!(entry.meta.assistantCategory in infoMap)){
      infoMap[entry.meta.assistantCategory] = {
        available: [],
        default: entry.id
      };
    }
    infoMap[entry.meta.assistantCategory].available.push({
      id: entry.id,
      deprecated: entry.info.deprecated,
      info: entry.info,
      meta: entry.meta,
    });
  }

  return infoMap;
}

export function categorizeAssistants(
  assistants: Record<string, AIAssistantInfoItem>
){
  const infoMap: AIAssistantInfoMap = {};
  for(const entry of Object.values(assistants)){
    if(!(entry.meta.assistantCategory in infoMap)){
      infoMap[entry.meta.assistantCategory] = {
        available: [],
        default: entry.id
      };
    }
    infoMap[entry.meta.assistantCategory].available.push(entry);
  }

  return infoMap;
}

export function mergeViewableAssistantInfos(
  ...infoMaps: Array<AIAssistantInfoMap>
): AIAssistantInfoMap{
  const messengerInfo: AIAssistantInfoMap = {};
  for(const infoMap of infoMaps){
    for(const [category, itemMap] of Object.entries(infoMap)){
      if(!(category in messengerInfo)){
        messengerInfo[category] = {
          available: [],
          default: itemMap.default,
        };
      }
      for(const entry of itemMap.available){
        messengerInfo[category].available.push({
          id: entry.id,
          deprecated: entry.info.deprecated,
          info: entry.info,
          meta: entry.meta,
        });
      }
      if(itemMap.default){
        messengerInfo[category].default = itemMap.default;
      }
    }
  }
  return messengerInfo;
}

