
export * from "./types";

import { GeneratorAssistantBase } from "./types";

import { OpenAIGPT4oAssistant } from "./text/openai/gpt-4o";
import { OpenAIGPT4oMiniAssistant } from "./text/openai/gpt-4o-mini";

import { CFDeepSeekR1Assistant } from "./text/cloudflare/deepseek-r1";
import { CFMetaLlama3_1Assistant } from "./text/cloudflare/llama-3_1";
import { CFMetaLlama3_3Assistant } from "./text/cloudflare/llama-3_3";

import { OpenAIDalle2Assistant } from "./image/openai/dalle-2";
import { OpenAIDalle3Assistant } from "./image/openai/dalle-3";

import { OpenAIGPT4oMiniMermaid } from "./diagram/openai/mermaid";
export const CHAT_AI_ASSISTANTS: Record<string, GeneratorAssistantBase<any>> = {
  // Text
  [OpenAIGPT4oAssistant.id]: OpenAIGPT4oAssistant,
  [OpenAIGPT4oMiniAssistant.id]: OpenAIGPT4oMiniAssistant,
  [CFDeepSeekR1Assistant.id]: CFDeepSeekR1Assistant,
  [CFMetaLlama3_1Assistant.id]: CFMetaLlama3_1Assistant,
  [CFMetaLlama3_3Assistant.id]: CFMetaLlama3_3Assistant,

  // Image
  [OpenAIDalle2Assistant.id]: OpenAIDalle2Assistant,
  [OpenAIDalle3Assistant.id]: OpenAIDalle3Assistant,

  // Diagram
  [OpenAIGPT4oMiniMermaid.id]: OpenAIGPT4oMiniMermaid,
};

export {
  OpenAIGPT4oAssistant,
  OpenAIGPT4oMiniAssistant,
  CFDeepSeekR1Assistant,
  CFMetaLlama3_1Assistant,
  CFMetaLlama3_3Assistant,

  OpenAIDalle2Assistant,
  OpenAIDalle3Assistant,

  OpenAIGPT4oMiniMermaid,
};

export const WHITELABEL_AI_ASSISTANTS: Record<string, GeneratorAssistantBase<any>> = {
  [OpenAIGPT4oAssistant.id]: OpenAIGPT4oAssistant,
  [OpenAIGPT4oMiniAssistant.id]: OpenAIGPT4oMiniAssistant,
  [CFDeepSeekR1Assistant.id]: CFDeepSeekR1Assistant,
  [CFMetaLlama3_1Assistant.id]: CFMetaLlama3_1Assistant,
  [CFMetaLlama3_3Assistant.id]: CFMetaLlama3_3Assistant,
};


import { OpenAIGPT4oMiniFineTuneAssistant } from "./finetuned/openai/gpt-4o-mini-2024-07-18";
import { OpenAIGPT4oFineTuneAssistant } from "./finetuned/openai/gpt-4o-2024-08-06";

export {
  OpenAIGPT4oMiniFineTuneAssistant,
  OpenAIGPT4oFineTuneAssistant,
};

export const FINE_TUNE_ASSISTANTS: Record<string, GeneratorAssistantBase<any>> = {
  [OpenAIGPT4oMiniFineTuneAssistant.id]: OpenAIGPT4oMiniFineTuneAssistant,
  [OpenAIGPT4oFineTuneAssistant.id]: OpenAIGPT4oFineTuneAssistant,
};


export * from "./utils";
