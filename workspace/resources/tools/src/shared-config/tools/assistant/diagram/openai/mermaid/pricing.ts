
import { ToolType } from "./type";
import { Prefix } from "./meta";

import { calculateTokens } from "../../../text/common/pricing";

// USe gpt-4o-mini to calculate
import {
  INPUT_COST_PER_TOKEN, OUTPUT_COST_PER_TOKEN, countTokens
} from "../../../text/openai/gpt-4o-mini/pricing";

// https://platform.openai.com/docs/models/gpt-4o-mini
const MAX_OUTPUT_TOKENS = 16_384n;

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, input){
    return {
      inputCost: INPUT_COST_PER_TOKEN * calculateTokens(
        countTokens, [
          ...input.thread,
          { content: Prefix }
        ]
      ),
      outputEscrow: MAX_OUTPUT_TOKENS * OUTPUT_COST_PER_TOKEN,
      costStatus: "maximum",
    };
  },

  async getFinalCost(config, input, output){
    return {
      outputCost: BigInt(countTokens(output.text)) * OUTPUT_COST_PER_TOKEN
    };
  },
};

