
import { ToolType } from "../type";

import { NANO_MULTIPLIER } from "../../../../../../money";
import { calculateTokens } from "../../../common/pricing";

// https://developers.cloudflare.com/workers-ai/models/deepseek-r1-distill-qwen-32b/
// $0.50 / 1,000,000
const INPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 50n / 100n / 1_000_000n;
// $4.88 / 1,000,000
const OUTPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 4_88n / 100n / 1_000_000n;

// https://platform.openai.com/docs/models/gpt-4o-mini
const MAX_OUTPUT_TOKENS = BigInt(Math.pow(2, 14));

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, input){
    const countTokens = await COUNT_TOKENS;
    return {
      inputCost: INPUT_COST_PER_TOKEN * calculateTokens(
        countTokens, input.thread
      ),
      outputEscrow: MAX_OUTPUT_TOKENS * OUTPUT_COST_PER_TOKEN,
      costStatus: "maximum",
    };
  },

  async getFinalCost(config, input, output){
    const countTokens = await COUNT_TOKENS;
    return {
      outputCost: BigInt(await countTokens(output.text)) * OUTPUT_COST_PER_TOKEN
    };
  },
};

import { PreTrainedTokenizer } from "@huggingface/transformers";
import tokenizer from "./tokenizer.json";
import tokenizer_config from "./tokenizer_config.json";
const COUNT_TOKENS = Promise.resolve().then(async ()=>{
  const tok = new PreTrainedTokenizer(tokenizer, tokenizer_config);
  return function(text: string){
    return tok.encode(text).length;
  };
});

