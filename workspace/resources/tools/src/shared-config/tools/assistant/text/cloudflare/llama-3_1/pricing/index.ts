
import { ToolType } from "../type";

import { NANO_MULTIPLIER } from "../../../../../../money";
import { calculateTokens } from "../../../common/pricing";

// https://developers.cloudflare.com/workers-ai/models/llama-3.1-8b-instruct/
// $0.28 / 1,000,000
// When we have a million tokens, we get charged $0.28 for input
const INPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 28n / 100n / 1_000_000n;
// $0.83 / 1,000,000
const OUTPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 83n / 100n / 1_000_000n;

// https://platform.openai.com/docs/models/gpt-4o-mini
const MAX_OUTPUT_TOKENS = BigInt(Math.pow(2, 14));

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, input){
    const countTokens = await COUNT_LLAMA3_TOKENS;
    return {
      inputCost: INPUT_COST_PER_TOKEN * calculateTokens(
        countTokens, input.thread
      ),
      outputEscrow: MAX_OUTPUT_TOKENS * OUTPUT_COST_PER_TOKEN,
      costStatus: "maximum",
    };
  },

  async getFinalCost(config, input, output){
    const countTokens = await COUNT_LLAMA3_TOKENS;
    return {
      outputCost: BigInt(countTokens(output.text)) * OUTPUT_COST_PER_TOKEN
    };
  },
};

import { PreTrainedTokenizer } from "@huggingface/transformers";
import tokenizer from "./tokenizer.json";
import tokenizer_config from "./tokenizer_config.json";
export const COUNT_LLAMA3_TOKENS = Promise.resolve().then(async ()=>{
  const tok = new PreTrainedTokenizer(tokenizer, tokenizer_config);
  return function(text: string){
    return tok.encode(text).length;
  };
});

