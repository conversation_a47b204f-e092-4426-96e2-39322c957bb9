
import { ToolType } from "./type";

import { NANO_MULTIPLIER } from "../../../../../money";
import { countTokens } from "gpt-tokenizer/model/gpt-4o-mini-2024-07-18-finetune";
import { calculateTokens } from "../../../text/common/pricing";

// https://platform.openai.com/docs/pricing#fine-tuning
// $0.30 / 1,000,000
// When we have a million tokens, we get charged $0.30 for input
const INPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 30n / 100n / 1_000_000n;
// $1.20 / 1,000,000
const OUTPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 1_20n / 100n / 1_000_000n;

// https://platform.openai.com/docs/models/gpt-4o-mini
const MAX_OUTPUT_TOKENS = 16_384n;

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, input){
    return {
      inputCost: INPUT_COST_PER_TOKEN * calculateTokens(
        countTokens, input.thread
      ),
      outputEscrow: MAX_OUTPUT_TOKENS * OUTPUT_COST_PER_TOKEN,
      costStatus: "maximum",
    };
  },

  async getFinalCost(config, input, output){
    return {
      outputCost: BigInt(countTokens(output.text)) * OUTPUT_COST_PER_TOKEN
    };
  },
};

