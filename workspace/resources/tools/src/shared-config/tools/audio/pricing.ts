
import { ProcessPricingStrategy } from "../../money/types";
import { AUDIO_SPEAKER_DIARIZATION } from "./speaker-diarization";
import { AUDIO_TRANSCRIPTION } from "./transcription";

export const AUDIO_SPEAKER_TRANSCRIPT_PROCESS_PRICING = new ProcessPricingStrategy(
  "Audio Speaker Sensitive Transcription",
  {}, {},
  {
    diarizer: AUDIO_SPEAKER_DIARIZATION,
    transcriber: AUDIO_TRANSCRIPTION
  },
  {
    diarizer: { title: "Speaker Diarization", description: "Identify speakers in the audio" },
    transcriber: { title: "Transcription", description: "Transcribe the audio" },
  }
);
