{
  "compilerOptions": {
    "outDir": "./dist",
    "target": "es2022",
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "types": ["node", "vitest/globals"],
    "lib": ["es2022", "dom"],
    "sourceMap": true,
    "esModuleInterop": true,
    "strict": true,
    "skipLibCheck": true,
    "removeComments": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": true,
    "declaration": true,
  },
  "files": ["src/index.ts"],
  "include": ["./src/**/*", "./typings"],
  "exclude": ["node_modules", "**/*.spec.ts"]
}
