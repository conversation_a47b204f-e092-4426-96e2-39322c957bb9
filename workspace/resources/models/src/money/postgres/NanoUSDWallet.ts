
export type NanoUSDWallet = {
  userId: string,
  usd_balance: bigint,
  nano_balance: bigint,
};

import { JSON_Object } from "@divinci-ai/utils";

export type NanoUSDWalletTransactionDoc = {
  parentId?: string,
  _id: string,
  createTimestamp: number,

  transactionType: string,
  transactionStep: string,

  runnerUser: string,
  allowDebt: boolean,

  relUsers: Array<string>,
  parts: Array<NanoUSDWalletTransactionPartDoc>,
};

export type NanoUSDWalletTransactionPartDoc = {
  _id: string,
  transactionId: string,
  walletUser: string,

  label: string,
  partRole: string,
  appliesToPartId?: string,

  balanceBefore: bigint,
  amount: bigint,

  metadata?: JSON_Object,
};
