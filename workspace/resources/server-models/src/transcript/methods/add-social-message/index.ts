import { getChatRedisClient } from "@divinci-ai/server-globals";
import { uniqueId, logDebug } from "@divinci-ai/utils";

import {
  condenseTarget,
  TranscriptMessage,
  TRANSCRIPT_LOCATION,
} from "@divinci-ai/models";
import { broadcastTextMessage } from "../add-chat-message/postgres-payment/broadcast";
const { chatRedisClient } = getChatRedisClient();

import { getUserById } from "../../statics/getUserById";
import { ITranscriptMethods, ITranscriptModelType } from "../../types";

export const addSocialMessage: ITranscriptMethods["addSocialMessage"] = async function(
  this: InstanceType<ITranscriptModelType>,
  message: { content: string, release?: string },
  request: { userId: string, phoneNumber?: string },
  buildUrl: (message: TranscriptMessage) => string,
){
  const TranscriptModel = this.constructor as ITranscriptModelType;
  const { userId, phoneNumber } = request;
  const user = await getUserById(userId);
  const id = uniqueId();


  logDebug("👤👤 transcript addUserToUserMessage user: ", JSON.stringify(user));
  logDebug("👤👤 transcript addUserToUserMessage content: ", JSON.stringify(message.content));

  const userToUserMessage: TranscriptMessage = {
    release: message.release,

    _id: id,
    role: "social",
    category: "text", // User-to-user messages are always text
    content: message.content,
    name: userId,
    timestamp: Date.now(),
    metadata: [],
    emojis: new Map(),
    context: [],
  };

  // Directly add to transcript without AI processing
  const updated = await TranscriptModel.findOneAndUpdate(
    { _id: this._id },
    {
      $push: { messages: { $each: [userToUserMessage] } },
    },
    {
      returnDocument: "after",
    },
  );


  if(updated === null) {
    throw new Error("🤷🏻‍♂️ Unable to update chat, possibly deleted.");
  }

  logDebug("👤👤 transcript addUserToUserMessage: pushed");

  // Broadcast the update via Redis
  chatRedisClient
    .publish(
      "/transcript/" + this._id.toString(),
      JSON.stringify(updated),
    )
    .catch((e: any)=>{
      console.error(
        "Redis Publish Error",
        "user-to-user",
        "/transcript/" + this._id.toString(),
        e,
      );
    });

  const target = condenseTarget({
    ...TRANSCRIPT_LOCATION,
    id: this._id.toString(),
  });
  broadcastTextMessage(
    phoneNumber ? [phoneNumber] : [],
    target,
    `User message from ${user.nickname}\n\n` + message.content,
    buildUrl(userToUserMessage),
  );

  return { messageId: id };
};
