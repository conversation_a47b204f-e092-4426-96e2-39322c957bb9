import { getUserById as getAuth0UserById } from "@divinci-ai/server-globals";

import { TRANSCRIPT_USER_COMPANY } from "@divinci-ai/models";

export type UserInfo = { user_id: string, nickname: string };

export async function getUserById(userId: string): Promise<UserInfo>{
  if(userId === TRANSCRIPT_USER_COMPANY){
    return { user_id: TRANSCRIPT_USER_COMPANY, nickname: "Company" };
  }
  return await getAuth0UserById(userId);
}
