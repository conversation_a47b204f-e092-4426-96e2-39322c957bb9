import { getPostgres } from "@divinci-ai/server-globals";

import { NANO_USD_WALLET_TABLE } from "./NanoUSDWallet/nano-usd-wallet.table";
import { PROCESS_TRANSACTION_TABLE } from "./ProcessPendingTransaction/process-transaction.table";
import { TOOL_TRANSACTION_TABLE } from "./ToolPendingTransaction/tool-transaction.table";

const TABLES = [
  { tableName: "nano_wallet", sqlQuery: NANO_USD_WALLET_TABLE },
  { tableName: "process transaction", sqlQuery: PROCESS_TRANSACTION_TABLE },
  { tableName: "tool transaction", sqlQuery: TOOL_TRANSACTION_TABLE },
];

export async function buildPostgresMoney(){
  const postgres = getPostgres();
  for(const { tableName, sqlQuery } of TABLES){
    try {
      await postgres.query(sqlQuery);
    }catch(e){
      console.error("Failed Creating Table:", tableName, e);
      throw e;
    }
  }
}

