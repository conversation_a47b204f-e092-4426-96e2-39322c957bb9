import { sql } from "@divinci-ai/server-globals";

export const PROCESS_TRANSACTION_TABLE = sql`
-- 1. The “header” row for each process
CREATE TABLE IF NOT EXISTS process_pending_transaction (
  _id            TEXT       PRIMARY KEY,
  doc_id         TEXT       NOT NULL,
  process_name   TEXT       NOT NULL,
  request_status TEXT       NOT NULL CHECK (request_status IN ('pending', 'failed', 'success')),

  payer_id       TEXT NOT NULL,
  runner_id      TEXT NOT NULL,
  created_at     TIMESTAMPTZ DEFAULT now(),
  finished_at    TIMESTAMPTZ
);

CREATE INDEX IF NOT EXISTS idx_process_pending_transaction_doc_id
  ON process_pending_transaction (doc_id);
CREATE INDEX IF NOT EXISTS idx_process_pending_transaction_payer_id
  ON process_pending_transaction (payer_id);
CREATE INDEX IF NOT EXISTS idx_process_pending_transaction_runner_id
  ON process_pending_transaction (runner_id);
CREATE INDEX IF NOT EXISTS idx_process_pending_transaction_created_at
  ON process_pending_transaction (created_at);
CREATE INDEX IF NOT EXISTS idx_process_pending_transaction_finished_at
  ON process_pending_transaction (finished_at);


CREATE TABLE IF NOT EXISTS process_io_details (
  process_id          TEXT  PRIMARY KEY REFERENCES process_pending_transaction(_id) ON DELETE CASCADE,
  process_config      JSONB NOT NULL,
  tools               JSONB NOT NULL,
  process_input       JSONB NOT NULL,
  process_output      JSONB,
  process_failure     JSONB
);

-- 2. One row per tool’s initial‐cost breakup
CREATE TABLE IF NOT EXISTS process_tool_costs (
  process_id            TEXT       NOT NULL  REFERENCES process_pending_transaction(_id) ON DELETE CASCADE,
  tool_key              TEXT       NOT NULL,               -- the same string key you use in your Record<…>

  input_cost            BIGINT     NOT NULL,
  output_escrow         BIGINT     NOT NULL,
  output_escrow_status  TEXT NOT NULL,
  output_cost           BIGINT,
  PRIMARY KEY (process_id, tool_key)
);
`;
