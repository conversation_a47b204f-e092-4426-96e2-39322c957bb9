import { sql } from "@divinci-ai/server-globals";
import { PoolClient } from "pg";

import { balanceUSDToNanoAmount } from "@divinci-ai/tools";

import { createDepositRefundTransaction } from "./createTransaction";

import { runNanoUSDTransaction } from "../../NanoUSDWallet";
import { DEPOSIT_REFUND_STEP, stripeParentId, stripeRefundId } from "../constants";

export async function runTransaction(
  client: PoolClient,
  paymentIntentId: string,
  refundId: string,
  amount: number
){
  const parentId = stripeParentId(paymentIntentId);

  // 1. Get original deposit transaction
  const { rows: [original] } = await client.query<{
    _id: string,
    transaction_step: string,
    runner_user: string,
  }>(sql`
    SELECT _id, transaction_step, runner_user
    FROM nano_wallet_transaction
    WHERE parent_id = $1 AND transaction_step = 'Deposit'
    LIMIT 1
    FOR UPDATE;
  `, [parentId]);

  if(!original) {
    throw new Error(`Original deposit transaction not found for ${parentId}`);
  }

  // 2. Check for existing refund transaction
  const { rowCount: refundExists } = await client.query(sql`
    SELECT 1
    FROM nano_wallet_transaction
    WHERE
      parent_id = $1
      AND transaction_step = $2
      AND _id = $3
    LIMIT 1;
  `, [parentId, DEPOSIT_REFUND_STEP, stripeRefundId(refundId)]);

  if(refundExists) {
    throw new Error(`Duplicate refund for ${parentId}`);
  }

  const refundAmount = -1n * balanceUSDToNanoAmount(amount/100);

  await runNanoUSDTransaction(
    client, createDepositRefundTransaction(
      paymentIntentId, refundId, original.runner_user, refundAmount
    ), true
  );
}
