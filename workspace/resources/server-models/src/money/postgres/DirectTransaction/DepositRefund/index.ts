import { getPostgres } from "@divinci-ai/server-globals";

import { runTransaction } from "./runTransaction";

export const runNanoUSDStripeDepositRefund = async function(
  paymentIntentId: string, refundId: string, amount: number
){
  // This should never happen as this is meant to be run from Stripe Webhook
  // But just in case
  if(amount <= 0) {
    throw new Error("Amount must be greater than 0");
  }
  const pool = getPostgres();
  const client = await pool.connect();
  try {
    await client.query("BEGIN");

    await runTransaction(client, paymentIntentId, refundId, amount);

    await client.query("COMMIT");
  }catch(err){
    await client.query("ROLLBACK");
    throw err; // re-throw or handle the error
  } finally {
    client.release();
  }
};

