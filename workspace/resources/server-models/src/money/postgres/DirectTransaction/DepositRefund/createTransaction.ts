import { NanoUSDWalletTransactionDocInsertable } from "../../NanoUSDWallet";
import { uniqueId } from "@divinci-ai/utils";

import {
  STRIPE_TRANSACTION_TYPE, DEPOSIT_REFUND_STEP,
  STRIPE_WEBHOOK_USER,
  stripeParentId, stripeRefundId
} from "../constants";

export function createDepositRefundTransaction(
  paymentIntentId: string,
  refundId: string,
  walletUser: string,
  amount: bigint
): NanoUSDWalletTransactionDocInsertable{

  const transactionId = stripeRefundId(refundId);

  return {
    _id: transactionId,
    parentId: stripeParentId(paymentIntentId),
    runnerUser: STRIPE_WEBHOOK_USER,
    transactionType: STRIPE_TRANSACTION_TYPE,
    transactionStep: DEPOSIT_REFUND_STEP,
    allowDebt: true,
    relUsers: [STRIPE_WEBHOOK_USER, walletUser],
    createTimestamp: Date.now(),
    parts: [{
      _id: uniqueId(),
      transactionId,
      label: "Stripe Deposit Refund",
      walletUser,
      partRole: "modifier",
      amount
    }]
  };
}
