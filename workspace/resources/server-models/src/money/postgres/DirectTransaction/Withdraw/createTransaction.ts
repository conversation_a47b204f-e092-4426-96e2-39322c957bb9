import { NanoUSDWalletTransactionDocInsertable } from "../../NanoUSDWallet";
import { uniqueId } from "@divinci-ai/utils";
import {
  STRIPE_TRANSACTION_TYPE, WITHDRAW_STEP, stripeParentId
} from "../constants";

export function createWithdrawTransaction(
  paymentIntentId: string,
  user: string,
  amount: bigint
): NanoUSDWalletTransactionDocInsertable{

  const transactionId = uniqueId();

  return {
    _id: transactionId,
    parentId: stripeParentId(paymentIntentId),
    runnerUser: user,
    transactionType: STRIPE_TRANSACTION_TYPE,
    transactionStep: WITHDRAW_STEP,
    allowDebt: false,
    relUsers: [user],
    createTimestamp: Date.now(),
    parts: [{
      _id: uniqueId(),
      transactionId,
      label: "Stripe Withdraw",
      walletUser: user,
      partRole: "modifier",
      amount: -1n * amount
    }]
  };
}
