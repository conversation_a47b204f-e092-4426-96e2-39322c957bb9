import { NanoUSDWalletTransactionDocInsertable } from "../../NanoUSDWallet";
import { uniqueId } from "@divinci-ai/utils";

export function createSendTransaction(
  fromUser: string, toUser: string, amount: bigint
): NanoUSDWalletTransactionDocInsertable{

  const transactionId = uniqueId();

  return {
    _id: transactionId,
    runnerUser: fromUser,
    transactionType: "DirectTransaction",
    transactionStep: "Send",
    allowDebt: false,
    relUsers: [fromUser, toUser],
    createTimestamp: Date.now(),
    parts: [
      {
        _id: uniqueId(),
        transactionId,
        label: "Direct Send From",
        walletUser: fromUser,
        partRole: "modifier",
        amount: -1n * amount
      },
      {
        _id: uniqueId(),
        transactionId,
        label: "Direct Send To",
        walletUser: toUser,
        partRole: "modifier",
        amount
      },
    ]
  };
}
