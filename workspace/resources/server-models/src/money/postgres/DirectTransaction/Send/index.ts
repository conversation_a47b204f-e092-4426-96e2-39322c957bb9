import { getPostgres } from "@divinci-ai/server-globals";

import { runNanoUSDTransaction } from "../../NanoUSDWallet";
import { createSendTransaction } from "./createTransaction";

export const runNanoUSDSend = async function(fromUser: string, toUser: string, amount: bigint){
  if(amount <= 0n) {
    throw new Error("Amount must be greater than 0");
  }
  if(fromUser === toUser) {
    throw new Error("Cannot send to self");
  }

  const pool = getPostgres();
  const client = await pool.connect();
  try {
    await client.query("BEGIN");

    await runNanoUSDTransaction(
      client, createSendTransaction(fromUser, toUser, amount), true
    );

    await client.query("COMMIT");
  }catch(err){
    await client.query("ROLLBACK");
    throw err; // re-throw or handle the error
  } finally {
    client.release();
  }
};

