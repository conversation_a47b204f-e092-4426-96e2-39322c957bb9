import { NanoUSDWalletTransactionDocInsertable } from "../../NanoUSDWallet";
import { uniqueId } from "@divinci-ai/utils";
import { STRIPE_TRANSACTION_TYPE, DEPOSIT_STEP, stripeParentId } from "../constants";

export function createDepositTransaction(
  paymentIntentId: string,
  user: string,
  amount: bigint
): NanoUSDWalletTransactionDocInsertable{

  const transactionId = uniqueId();

  return {
    _id: transactionId,
    parentId: stripeParentId(paymentIntentId),
    runnerUser: user,
    transactionType: STRIPE_TRANSACTION_TYPE,
    transactionStep: DEPOSIT_STEP,
    allowDebt: false,
    relUsers: [user],
    createTimestamp: Date.now(),
    parts: [{
      _id: uniqueId(),
      transactionId,
      label: "Stripe Deposit",
      walletUser: user,
      partRole: "modifier",
      amount
    }]
  };
}
