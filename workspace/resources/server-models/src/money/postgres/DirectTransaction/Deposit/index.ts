import { getPostgres } from "@divinci-ai/server-globals";

import { runNanoUSDTransaction } from "../../NanoUSDWallet";
import { createDepositTransaction } from "./createTransaction";

export const runNanoUSDStripeDeposit = async function(paymentIntentId: string, userId: string, amount: bigint){
  if(amount <= 0n) {
    throw new Error("Amount must be greater than 0");
  }

  const pool = getPostgres();
  const client = await pool.connect();
  try {
    await client.query("BEGIN");

    await runNanoUSDTransaction(
      client, createDepositTransaction(paymentIntentId, userId, amount), true
    );

    await client.query("COMMIT");
  }catch(err){
    await client.query("ROLLBACK");
    throw err; // re-throw or handle the error
  } finally {
    client.release();
  }
};

