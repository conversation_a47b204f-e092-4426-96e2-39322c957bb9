import { handleFetch, replaceParams, fetchBody } from "@divinci-ai/utils";

import { QA_PROMPT_ROOT, QA_PROMPT_ITEM } from "./paths";
import { WhiteLabelQAPrompts, MessageEnvironmentIds, MessageContextItem } from "@divinci-ai/models";

export async function listQAPrompts(
  fetcher: typeof fetch, params: { whitelabelId: string }
){
  return await handleFetch(fetcher(
    replaceParams(QA_PROMPT_ROOT, params)
  )) as WhiteLabelQAPrompts;
}

export async function createQAPrompt(
  fetcher: typeof fetch,
  params: { whitelabelId: string },
  body: { prompt: string }
){
  return await handleFetch(fetcher(
    replaceParams(QA_PROMPT_ROOT, params),
    fetchBody("POST", body),
  )) as WhiteLabelQAPrompts;
}

export async function updateQAPrompt(
  fetcher: typeof fetch,
  params: { whitelabelId: string, qaId: string },
  body: { prompt: string }
){
  return await handleFetch(fetcher(
    replaceParams(QA_PROMPT_ITEM, params),
    fetchBody("POST", body),
  )) as WhiteLabelQAPrompts;
}

export async function deleteQAPrompt(
  fetcher: typeof fetch,
  params: { whitelabelId: string, qaId: string }){
  return await handleFetch(fetcher(
    replaceParams(QA_PROMPT_ITEM, params),
    { method: "DELETE" },
  )) as WhiteLabelQAPrompts;
}

import { DIVINCI_TEST_PROCESS_Config, DIVINCI_TEST_PROCESS_AddHeader } from "@divinci-ai/utils";
export async function runQAPrompt(
  fetcher: typeof fetch,
  params: { whitelabelId: string, qaId: string },
  body: { release: string } | MessageEnvironmentIds,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  return await handleFetch(fetcher(
    replaceParams(`${QA_PROMPT_ITEM}/run`, params),
    DIVINCI_TEST_PROCESS_AddHeader(fetchBody("POST", removeUndefinedContext(body)), testConfig),
  )) as {
    responseText: string,
    context: Array<MessageContextItem>,
  };
}

type RemoveUndefinedProps<T extends object> = {
  [K in keyof T as T[K] extends undefined ? never : K]: Exclude<T[K], undefined>
};
function removeUndefinedContext<T extends object>(obj: T): RemoveUndefinedProps<T>{
  return Object.fromEntries(
    Object.entries(obj).filter(([, value])=>value !== undefined)
  ) as RemoveUndefinedProps<T>;
}
