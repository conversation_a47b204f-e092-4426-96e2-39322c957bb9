import { fetchBody, handleFetch, replaceParams } from "@divinci-ai/utils";

import { WHITELABEL_ITEM } from "../paths";

export async function addReleaseChatAIMessage(
  fetcher: typeof fetch,
  params: { whitelabelId: string, chatId: string },
  body: {
    content: string,
    replyTo?: string,
    release: string,
 }
){
  return await handleFetch(fetcher(
    replaceParams(`${WHITELABEL_ITEM}/threads/:chatId/message`, params),
    fetchBody("POST", body)
  )) as { messageId: string };
}

export async function addReleaseChatSocialMessage(
  fetcher: typeof fetch,
  params: { whitelabelId: string, chatId: string },
  body: { content: string, release: string }
){
  return await handleFetch(fetcher(
    replaceParams(`${WHITELABEL_ITEM}/threads/:chatId/social-message`, params),
    fetchBody("POST", body)
  )) as { messageId: string };
}
