import { fetchBody, handleFetch, replaceParams } from "@divinci-ai/utils";

export function whitelabelTestTranscriptAddMessage(
  fetcher: typeof fetch,
  params: { whitelabelId: string, transcriptId: string },
  body: {
    content: string,
    replyTo?: string,

    assistantName?: string,
    threadPrefix?: string,
    messagePrefix?: string,
    ragVectorIndex?: string,
  }
){
  return handleFetch(fetcher(
    replaceParams(
      `/white-label/:whitelabelId/transcript/:transcriptId/message`, params
    ),
    fetchBody("POST", body)
  ));
}

export function whitelabelTestTranscriptAddSocialMessage(
  fetcher: typeof fetch,
  params: { whitelabelId: string, transcriptId: string },
  body: { content: string, release?: string }
){
  return handleFetch(fetcher(
    replaceParams(
      `/white-label/:whitelabelId/transcript/:transcriptId/social-message`, params
    ),
    fetchBody("POST", body)
  ));
}
