
import { handleFetch, replaceParams } from "@divinci-ai/utils";

import { FineTuneableInfo, FineTuneCustomModel } from "@divinci-ai/models";
import { FINETUNE_AI } from "../paths";

export async function listFineTuneAvailableModels(
  fetcher: typeof fetch,
  params: { whitelabelId: string }
){
  return await handleFetch(fetcher(
    replaceParams(`${FINETUNE_AI}/base-models`, params),
  )) as {
    usableModels: Array<FineTuneableInfo>,
    baseModels: Array<FineTuneableInfo>,
    fineTunedModels: Array<FineTuneCustomModel>,
  };
}
