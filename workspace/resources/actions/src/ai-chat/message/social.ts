import { fetchBody, handleFetch, replaceParams } from "@divinci-ai/utils";
import { AI_CHAT_ITEM } from "../paths";

export async function addChatSocialMessage(
  fetcher: typeof fetch,
  params: { chatId: string },
  body: { content: string, release?: string }
){
  return await handleFetch(fetcher(
    replaceParams(`${AI_CHAT_ITEM}/social-message`, params),
    fetchBody("POST", body)
  )) as { messageId: string };
}
