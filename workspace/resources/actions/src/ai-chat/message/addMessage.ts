
import { fetchBody, handleFetch, replaceParams } from "@divinci-ai/utils";
import { AI_CHAT_ITEM } from "../paths";
import { DIVINCI_TEST_PROCESS_Config, DIVINCI_TEST_PROCESS_AddHeader } from "@divinci-ai/utils";

type MessageContext = (
  | { assistantName: string }
  | { replyTo: string }
);

export async function aichatAddMessage(
  fetcher: typeof fetch,
  params: { chatId: string },
  body: { content: string } & MessageContext,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  return await handleFetch(fetcher(
    replaceParams(`${AI_CHAT_ITEM}/message`, params),
    DIVINCI_TEST_PROCESS_AddHeader(fetchBody("POST", body), testConfig)
  )) as { requestId: string, responseId: string };
}
