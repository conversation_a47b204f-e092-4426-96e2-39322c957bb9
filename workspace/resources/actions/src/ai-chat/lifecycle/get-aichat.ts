

import { handleFetch, replaceParams } from "@divinci-ai/utils";
import { AI_CHAT_ITEM } from "../paths";
import { AIChat, Transcript, WhiteLabelReleaseDoc } from "@divinci-ai/models";

export async function aichatGet(
  fetcher: typeof fetch,
  params: { chatId: string }
){
  return await handleFetch(fetcher(
    replaceParams(`${AI_CHAT_ITEM}`, params),
  )) as any as { chat: AIChat, transcript: Transcript, releases: Array<WhiteLabelReleaseDoc> };
}

export async function aichatGetTranscript(
  fetcher: typeof fetch,
  params: { chatId: string }
){
  return await handleFetch(fetcher(
    replaceParams(`${AI_CHAT_ITEM}/transcript`, params),
  )) as any as Transcript;
}
