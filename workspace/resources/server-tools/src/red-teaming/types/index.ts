// Core Types for Red Teaming System

export interface RedTeamConfig {
  // Target system configuration
  target: {
    type: "rag" | "agent" | "llm" | "hybrid";
    endpoint?: string;
    provider?: string;
    model?: string;
  };

  // Test configuration
  purpose?: string;
  numTests?: number;
  maxConcurrency?: number;

  // Plugin configuration
  plugins: Array<string | PluginConfig>;

  // Strategy configuration
  strategies?: Array<string | StrategyConfig>;

  // Provider configuration for custom testing
  providers?: Array<ProviderConfig>;

  // Output configuration
  output?: {
    format?: "json" | "yaml" | "html" | "markdown";
    file?: string;
    includeDetails?: boolean;
  };
}

export interface PluginConfig {
  id: string;
  config?: Record<string, any>;
  enabled?: boolean;
}

export interface StrategyConfig {
  id: string;
  config?: Record<string, any>;
  enabled?: boolean;
}

export interface ProviderConfig {
  id: string;
  type: "python" | "javascript" | "http" | "custom";
  config: Record<string, any>;
}

export interface TestCase {
  id: string;
  description: string;
  input: string;
  expectedBehavior?: string;
  metadata?: Record<string, any>;
  plugin: string;
  strategy?: string;
}

export interface TestResult {
  testCase: TestCase;
  output: string;
  passed: boolean;
  score?: number;
  vulnerabilities: Vulnerability[];
  metadata?: Record<string, any>;
  duration: number;
  timestamp: Date;
}

export interface Vulnerability {
  type: VulnerabilityType;
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  evidence: string;
  recommendation?: string;
  cwe?: string;
  owasp?: string;
}

export enum VulnerabilityType {
  PROMPT_INJECTION = "prompt_injection",
  CONTEXT_INJECTION = "context_injection",
  DATA_EXFILTRATION = "data_exfiltration",
  PRIVILEGE_ESCALATION = "privilege_escalation",
  MEMORY_POISONING = "memory_poisoning",
  TOOL_MANIPULATION = "tool_manipulation",
  PII_LEAK = "pii_leak",
  HARMFUL_CONTENT = "harmful_content",
  HALLUCINATION = "hallucination",
  JAILBREAK = "jailbreak",
  RBAC_VIOLATION = "rbac_violation",
  SQL_INJECTION = "sql_injection",
  SSRF = "ssrf",
  CONTEXT_OVERFLOW = "context_overflow",
  DATASET_MANIPULATION = "dataset_manipulation",
  POLICY_VIOLATION = "policy_violation",
}

export interface RedTeamReport {
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    vulnerabilitiesFound: number;
    overallScore: number;
    riskLevel: "low" | "medium" | "high" | "critical";
  };

  vulnerabilities: Vulnerability[];
  testResults: TestResult[];
  recommendations: string[];

  metadata: {
    timestamp: Date;
    duration: number;
    config: RedTeamConfig;
    version: string;
  };
}

export interface Plugin {
  id: string;
  name: string;
  description: string;
  version: string;
  vulnerabilityTypes: VulnerabilityType[];

  generateTestCases(config: PluginConfig, context?: any): Promise<TestCase[]>;
  evaluateResult(testCase: TestCase, output: string): Promise<TestResult>;
}

export interface Strategy {
  id: string;
  name: string;
  description: string;
  version: string;

  generatePrompts(config: StrategyConfig, context?: any): Promise<string[]>;
  combineWithPlugins(plugins: Plugin[]): Promise<TestCase[]>;
}

export interface Provider {
  id: string;
  name: string;
  description: string;
  type: string;

  execute(input: string, config: ProviderConfig): Promise<string>;
  healthCheck(): Promise<boolean>;
}

// Context types for different system components
export interface RAGContext {
  retrievedDocuments?: string[];
  vectorStore?: string;
  embeddingModel?: string;
  retrievalMethod?: string;
}

export interface AgentContext {
  availableTools?: string[];
  permissions?: string[];
  memory?: Record<string, any>;
  objectives?: string[];
}

export interface LLMContext {
  model?: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
  contextWindow?: number;
}

// Event types for monitoring and logging
export interface RedTeamEvent {
  type:
    | "test_started"
    | "test_completed"
    | "vulnerability_found"
    | "error"
    | "warning";
  timestamp: Date;
  data: Record<string, any>;
  testId?: string;
  pluginId?: string;
}

export type RedTeamEventHandler = (event: RedTeamEvent) => void;

// Security Test interface for plugins
export interface SecurityTest {
  id: string;
  name: string;
  description: string;
  severity: "low" | "medium" | "high" | "critical";
  category: "authentication" | "authorization" | "encryption" | "network" | "data" | "other";
}
