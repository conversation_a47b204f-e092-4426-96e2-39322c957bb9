
import { JSON_Object, ShallowObject } from "@divinci-ai/utils";
import { ToolConfig } from "@divinci-ai/tools";
import { PublicToolInfo } from "@divinci-ai/models";

export interface IOrganizer<
  ToolName extends string,
  PaymentInput, PaymentOutput,
  MetaData,
  API
> {
  add<Config extends ShallowObject>(
    toolConfig: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
    api: API
  ): void,
  getOptions(): string[],
  getAll(): Record<string, {
    tool: ToolConfig<ToolName, any, PaymentInput, PaymentOutput, MetaData>,
    api: API,
  }>,
  getAllInfo(): Record<string, PublicToolInfo>,
  getAllAPI(): Record<string, API>,
  getInfo(id: string): PublicToolInfo,
  isValidId(id: string): boolean,
  get<Config extends ShallowObject>(id: string): undefined | {
    tool: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
    api: API,
  },
  tryToGet<Config extends ShallowObject>(
    id: string
  ): {
    tool: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
    api: API,
  },
  tryToGetAndValidateConfig<Config extends ShallowObject>(
    { id, config }: { id: string, config: JSON_Object }
  ): {
    tool: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
    config: Config,
    api: API,
  },
}
