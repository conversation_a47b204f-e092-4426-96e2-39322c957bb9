import { castShallowObject, JSON_Object, JSON_Unknown, ShallowObject } from "@divinci-ai/utils";
import { HTTP_ERRORS_WITH_STACK } from "@divinci-ai/server-utils";
import { ToolConfig } from "@divinci-ai/tools";
import { PublicToolInfo } from "@divinci-ai/models";

import { IOrganizer } from "./APIOrganizer";

type RunnableFunc<Config extends ShallowObject, APIInput, APIOutput> = (
  (config: Config, input: APIInput) => Promise<APIOutput>
);

type FuncItem<
  ToolName extends string,
  Config extends ShallowObject,
  PaymentInput, PaymentOutput,
  MetaData,
  APIInput, APIOutput
> = {
  tool: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
  api: RunnableFunc<Config, APIInput, APIOutput>,
};

export type FuncOrganizerAPI<T> = (
  T extends FuncOrganizer<any, any, any, any, infer APIInput, infer APIOutput>
    ? RunnableFunc<any, APIInput, APIOutput>
    : never
);

type FuncWithConfig<
  ToolName extends string,
  Config extends ShallowObject,
  PaymentInput, PaymentOutput,
  MetaData,
  APIInput, APIOutput
> = {
  tool: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
  config: Config,
  api: RunnableFunc<Config, APIInput, APIOutput>,
};

export class FuncOrganizer<
  ToolName extends string,
  PaymentInput, PaymentOutput,
  MetaData,
  APIInput, APIOutput
> implements IOrganizer<ToolName, PaymentInput, PaymentOutput, MetaData, RunnableFunc<any, APIInput, APIOutput>> {
  ids: Set<string> = new Set();
  tools: Record<string, FuncItem<ToolName, any, PaymentInput, PaymentOutput, MetaData, APIInput, APIOutput>> = {};
  constructor(private toolType: string){}
  add<Config extends ShallowObject>(
    toolConfig: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
    api: RunnableFunc<Config, APIInput, APIOutput>
  ){
    const id = toolConfig.id;
    if(this.ids.has(id)){
      throw new Error(`Item with name ${id} already exists`);
    }
    this.ids.add(id);
    this.tools[id] = { tool: toolConfig, api };
  }
  getOptions(){
    return Array.from(this.ids);
  }
  getAll(){
    return this.tools;
  }
  getAllAPI(){
    const allAPI: Record<string, RunnableFunc<any, APIInput, APIOutput>> = {};
    for(const id of this.ids){
      allAPI[id] = this.tools[id].api;
    }
    return allAPI;
  }
  getAllInfo(){
    const allInfo: Record<string, PublicToolInfo> = {};
    for(const id of this.ids){
      allInfo[id] = this.tools[id].tool.info;
    }
    return allInfo;
  }
  isValidId(id: string){
    return this.ids.has(id);
  }
  getInfo(id: string){
    const tool = this.tools[id];
    if(!tool){
      throw new HTTP_ERRORS_WITH_STACK.BAD_FORM(`${this.toolType} ${id} does not exist`);
    }
    return tool.tool.info;
  }
  get<Config extends ShallowObject>(id: string): (
    undefined | FuncItem<ToolName, Config, PaymentInput, PaymentOutput, MetaData, APIInput, APIOutput>
  ){
    return this.tools[id];
  }
  tryToGet<Config extends ShallowObject>(
    id: string
  ): FuncItem<ToolName, Config, PaymentInput, PaymentOutput, MetaData, APIInput, APIOutput>{
    const tool = this.tools[id];
    if(!tool){
      throw new HTTP_ERRORS_WITH_STACK.BAD_FORM(`${this.toolType} ${id} does not exist`);
    }
    if(tool.tool.info.deprecated){
      throw new HTTP_ERRORS_WITH_STACK.BAD_FORM(`${this.toolType} ${id} is deprecated`);
    }
    return tool;
  }
  tryToGetAndValidateConfig<Config extends ShallowObject>(
    { id, config }: { id: string, config: JSON_Object }
  ): FuncWithConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData, APIInput, APIOutput>{
    const tool = this.tryToGet<Config>(id);
    const validatedConfig = validateConfig(tool.tool.inputConfig, config);
    return { tool: tool.tool, config: validatedConfig, api: tool.api };
  }
  tryToUse(
    id: string, configUncasted: ShallowObject, input: APIInput, e?: (message: string)=>any
  ){
    const tool = this.tools[id];
    if(!tool){
      if(e) throw e(`${this.toolType} ${id} does not exist`);
      throw new Error(`${this.toolType} ${id} does not exist`);
    }
    if(tool.tool.info.deprecated){
      if(e) throw e(`${this.toolType} ${id} is deprecated`);
      throw new Error(`${this.toolType} ${id} is deprecated`);
    }
    const config = validateConfig(tool.tool.inputConfig, configUncasted);
    return tool.api(config, input);
  }
}

function validateConfig<Config extends ShallowObject>(
  inputConfig: ToolConfig<any, Config, any, any>["inputConfig"], config: JSON_Unknown
): Config{
  if(!inputConfig) return {} as Config;
  const casted = castShallowObject(config, inputConfig.schema) as any as Config;
  const errors = inputConfig.validate(casted);
  if(errors.length > 0){
    throw new HTTP_ERRORS_WITH_STACK.BAD_FORM(errors as Array<{ key: string, error: string }>);
  }
  return casted;
}
