import { castShallowObject, JSON_Object, JSON_Unknown, ShallowObject } from "@divinci-ai/utils";
import { ToolConfig } from "@divinci-ai/tools";
import { PublicToolInfo } from "@divinci-ai/models";


import { IOrganizer } from "./APIOrganizer";
import { HTTP_ERRORS_WITH_STACK } from "@divinci-ai/server-utils";

export type JobItem<
  ToolName extends string,
  Config extends ShallowObject,
  PaymentInput, PaymentOutput,
  MetaData,
  JobAPI
> = {
  tool: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
  api: JobAPI,
};

type JobWithConfig<
  ToolName extends string,
  Config extends ShallowObject,
  PaymentInput, PaymentOutput,
  MetaData,
  JobAPI
> = {
  tool: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
  config: Config,
  api: JobAPI,
};

export class JobOrganizer<
  ToolName extends string,
  PaymentInput, PaymentOutput,
  MetaData,
  JobAPI
> implements IOrganizer<ToolName, PaymentInput, PaymentOutput, MetaData, JobAPI> {
  tools: Record<string, JobItem<ToolName, any, PaymentInput, PaymentOutput, MetaData, JobAPI>> = {};
  constructor(private toolType: string){}
  add<Config extends ShallowObject>(
    toolConfig: ToolConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData>,
    api: JobAPI
  ){
    const id = toolConfig.id;
    if(id in this.tools){
      throw new Error(`Item with name ${id} already exists`);
    }
    this.tools[id] = { tool: toolConfig, api };
  }
  getOptions(){
    return Object.keys(this.tools);
  }
  getAll(){
    return this.tools;
  }
  getAllInfo(){
    const allInfo: Record<string, PublicToolInfo> = {};
    for(const id in this.tools){
      allInfo[id] = this.tools[id].tool.info;
    }
    return allInfo;
  }
  getAllAPI(){
    const allAPI: Record<string, JobAPI> = {};
    for(const id in this.tools){
      allAPI[id] = this.tools[id].api;
    }
    return allAPI;
  }
  getInfo(id: string){
    const tool = this.tools[id];
    if(!tool){
      throw new Error(`${this.toolType} ${id} does not exist`);
    }
    return tool.tool.info;
  }
  isValidId(id: string){
    return id in this.tools;
  }
  get(id: string){
    return this.tools[id];
  }
  tryToGet<Config extends ShallowObject>(
    id: string
  ): JobItem<ToolName, Config, PaymentInput, PaymentOutput, MetaData, JobAPI>{
    const tool = this.tools[id];
    if(!tool){
      throw new Error(`${this.toolType} ${id} does not exist`);
    }
    if(tool.tool.info.deprecated){
      throw new Error(`${this.toolType} ${id} is deprecated`);
    }
    return tool;
  }
  tryToGetAndValidateConfig<Config extends ShallowObject>(
    { id, config }: { id: string, config: JSON_Object }
  ): JobWithConfig<ToolName, Config, PaymentInput, PaymentOutput, MetaData, JobAPI>{
    const tool = this.tryToGet<Config>(id);
    const validatedConfig = validateConfig(tool.tool.inputConfig, config);
    return { tool: tool.tool, config: validatedConfig, api: tool.api };
  }
}

function validateConfig<Config extends ShallowObject>(
  inputConfig: ToolConfig<any, Config, any, any>["inputConfig"], config: JSON_Unknown
): Config{
  if(!inputConfig) return {} as Config;
  const casted = castShallowObject(config, inputConfig.schema) as any as Config;
  const errors = inputConfig.validate(casted);
  if(errors.length > 0){
    throw new HTTP_ERRORS_WITH_STACK.BAD_FORM(errors as Array<{ key: string, error: string }>);
  }
  return casted;
}
