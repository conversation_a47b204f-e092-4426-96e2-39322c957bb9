import { ToolConfig } from "@divinci-ai/tools";
import { ShallowObject } from "@divinci-ai/utils";
import { Readable } from "stream";


export type R2FileLocation = {
  originalName: string,
  bucket: string,
  objectKey: string,
};


export type TextChunkType = {
  index: number,
  tokenCount: number,
  text: string,
};

export type StoredTextChunk = {
  id: string,
  fileId: string,
  index: number,
  tokenCount: number,
  text: string,
};

import {
  RawToChunksInput as RawToChunksPricingInput,
  RawToChunksOutput as RawToChunksPricingOutput,
} from "@divinci-ai/tools";


export type RawToChunksInput = R2FileLocation;
export type RawToChunksOutput = {
  readable: Readable & { count: number },
  cancel: () => void,
};
export type RawToChunksAPI<Config> = (
  (config: Config, input: RawToChunksInput)=>Promise<RawToChunksOutput>
);

export type RawToChunksTool<Config extends ShallowObject> = {
  tool: ToolConfig<
    "RawToChunks",
    Config,
    RawToChunksPricingInput, RawToChunksPricingOutput
  >,
  api: RawToChunksAPI<Config>,
};

