
import { FuncOrganizer } from "../../util/FuncOrganizer";
import {
  RawToChunksInput as RawToChunksPricingInput,
  RawToChunksOutput as RawToChunksPricingOutput,
} from "@divinci-ai/tools";

import {
  RawToChunksInput,
  RawToChunksOutput,
} from "./types";

export const RAWFILE_TO_CHUNKS = new FuncOrganizer<
  "RawToChunks",
  RawToChunksPricingInput, RawToChunksPricingOutput,
  void,
  RawToChunksInput, RawToChunksOutput
>("RawToChunks");

import { DIVINCI_OPENPARSE } from "./open-parse";
RAWFILE_TO_CHUNKS.add(DIVINCI_OPENPARSE.tool, DIVINCI_OPENPARSE.api);

import { UNSTRUCTURED } from "./unstructured";
RAWFILE_TO_CHUNKS.add(UNSTRUCTURED.tool, UNSTRUCTURED.api);

export { RawToChunksTool, RawToChunksInput, RawToChunksOutput } from "./types";
