
import fetch from "node-fetch-commonjs";
import FormData from "form-data";
import { Readable } from "node:stream";

import {
  UNSTRUCTURED_API_KEY, UNSTRUCTURED_WORKER_URL,
} from "@divinci-ai/server-globals";
import { UnstructuredConfig } from "@divinci-ai/tools";

export async function runUnstructuredFetch(
  config: UnstructuredConfig,
  file: { filename: string, body: Readable },
){

  const formData = new FormData();
  formData.append("files", file.body, { filename: file.filename });
  formData.append("chunking_strategy", config.chunkingStrategy || "by_title");
  formData.append("max_characters", config.maxCharacters || 500);
  formData.append("similarity_threshold", config.similarityThreshold || 0.5);

  formData.append("strategy", "fast");
  formData.append("new_after_n_chars", 1500);

  formData.append("split_pdf_page", "true");
  formData.append("split_pdf_concurrency_level", 5);
  formData.append("split_pdf_allow_failed", "true");

  const controller = new AbortController();

  const response = await fetch(UNSTRUCTURED_WORKER_URL, {
    signal: controller.signal,
    method: "POST",
    body: formData,
    headers: {
      ...formData.getHeaders(),
      "unstructured-api-key": UNSTRUCTURED_API_KEY,
      "accept": "application/json",
    }
  });

  if(!response.ok) {
    throw new Error(`Unstructured server error: ${response.statusText}`);
  }

  const readable = response.body;
  if(!readable){
    throw new Error("Unable to get reader from response body");
  }

  return {
    stream: readable as Readable,
    cancel: ()=>{
      // Implement cancellation logic here
      controller.abort();
    }
  };
}
