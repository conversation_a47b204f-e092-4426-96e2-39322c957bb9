
import { CLOUDFLARE_ACCOUNT_ID, CLOUDFLARE_API_KEY } from "@divinci-ai/server-globals";
import { StructuredOuputAPI } from "../types";

export type ModelDefintion = {
  model: string,
};

type RawCloudflareResponse<T> = {
  result: T,
  success: boolean,
  errors: Array<{ code: number, message: string }>,
  messages: Array<{ code: number, message: string }>,
};

export function buildAPI({ model }: ModelDefintion): StructuredOuputAPI["runStructuredOutput"]{
  return async function(prefix, outputConfig, outputType, prompt){
    const schema = {
      type: "object",
      properties: {} as Record<string, { type: "boolean" | "number" | "string" }>,
      required: [] as string[],
    };

    for(const [k, t] of Object.entries(outputConfig)) {
      schema.properties[k] = { type: t };
      schema.required.push(k);
    }

    const body = {
      messages: [
        ...prefix.map(c=>({ role: "system", content: c })),
        { role: "user", content: prompt },
      ],
      response_format: {
        type: "json_schema",
        json_schema: schema
      },
    };

    const res = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/ai/run/${model}`,
      {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${CLOUDFLARE_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      }
    );

    if(!res.ok) {
      const err = await res.json().catch(()=>null);
      throw new Error(`Worker AI error: ${res.status} ${res.statusText} ${JSON.stringify(err)}`);
    }

    const json = (await res.json() as RawCloudflareResponse<any>);
    console.log("structured output resposne:", json);
    return json.result.response;
  };
}
