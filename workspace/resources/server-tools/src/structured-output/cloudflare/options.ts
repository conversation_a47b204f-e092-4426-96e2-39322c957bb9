

import { ModelDefintion } from "./api";
import { PublicToolInfo } from "@divinci-ai/models";

const CLOUDFLARE_MODEL = "@cf/meta/llama-3.3-70b-instruct-fp8-fast";
export const CloudflareLlama3_3: { api: ModelDefintion, info: PublicToolInfo } = {
  api: {
    model: CLOUDFLARE_MODEL
  },
  info: {
    id: CLOUDFLARE_MODEL,
    title: "Llama 3.3 70B Instruct",
    description: "Uses Llama 3.3 to generate structured output",
    url: "https://developers.cloudflare.com/workers-ai/models/llama-3.3-70b-instruct-fp8-fast/",

    orgUrl: "https://ai.meta.com/",
    org: "meta",

    provider: {
      name: "Cloudflare",
      url: "https://www.cloudflare.com/"
    },

    deprecated: false,
  }
};
