import { DIVINCI_AUDIO_SPLITTER_FFMPEG_URL } from "./constants";
import { handleFetch, fetchBody } from "@divinci-ai/utils";
import { R2Pointer } from "./types";
import { parse as pathParse } from "node:path";
import { uniqueId } from "@divinci-ai/utils";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { mtlsFetch } from "./mtls-fetch";

export async function canConvertToFlac(filename: string) {
  console.log(
    "🔍 Attempting to make call to:",
    `${DIVINCI_AUDIO_SPLITTER_FFMPEG_URL}/can-convert`
  );

  try {
    const response = (await handleFetch(
      mtlsFetch(
        `${DIVINCI_AUDIO_SPLITTER_FFMPEG_URL}/can-convert`,
        fetchBody("POST", { filename })
      )
    )) as
      | { support: true; type: "audio" | "video"; extension?: string }
      | { support: false; type: "unknown"; message?: string };

    // Log the response for debugging
    console.log(`canConvertToFlac response:`, response);

    return response;
  } catch (e: unknown) {
    console.error(
      `Failed to run ${DIVINCI_AUDIO_SPLITTER_FFMPEG_URL}/can-convert:`,
      e
    );

    // Extract error message safely
    const errorMessage = e instanceof Error ? e.message : String(e);
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
      `Failed to check if the file can convert: ${errorMessage}`
    );
  }
}

export async function convertToFlac(
  sourceR2Pointer: R2Pointer,
  destinationR2Pointer?: R2Pointer
) {
  try {
    // First check if the file can be converted
    const canConvert = await canConvertToFlac(sourceR2Pointer.Key);
    if (!canConvert.support) {
      console.error(
        `File cannot be converted: ${sourceR2Pointer.Key}`,
        canConvert
      );
      // Type narrowing: if support is false, it's the unsupported type with optional message
      const errorMessage = (canConvert as { support: false; type: "unknown"; message?: string }).message ||
        `File type not supported for conversion to FLAC: ${sourceR2Pointer.Key}`;
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(errorMessage);
    }

    console.log(`File can be converted: ${sourceR2Pointer.Key}`, canConvert);

    if (!destinationR2Pointer) {
      const sourceFile = pathParse(sourceR2Pointer.Key);

      // Use FLAC format for better quality with smaller file size
      const extension = "flac";

      // Get the filename without extension
      const filenameWithoutExt = sourceFile.name;

      destinationR2Pointer = {
        Bucket: sourceR2Pointer.Bucket,
        Key: `${
          sourceFile.dir ? sourceFile.dir + "/" : ""
        }${filenameWithoutExt}.${extension}`,
      };
    }

    console.log(
      `Converting file to FLAC: ${sourceR2Pointer.Key} -> ${destinationR2Pointer.Key}`
    );

    try {
      return (await handleFetch(
        mtlsFetch(
          `${DIVINCI_AUDIO_SPLITTER_FFMPEG_URL}/convert-to-mp3`,
          fetchBody("POST", {
            source: sourceR2Pointer,
            destination: destinationR2Pointer,
          })
        )
      )) as { Bucket: string; Key: string };
    } catch (conversionError: unknown) {
      console.error(`Failed to convert file to FLAC:`, conversionError);

      // Check if the error response has a message
      let errorMessage = "Failed to convert audio file";

      // Type guard to check if conversionError has json property
      if (
        conversionError &&
        typeof conversionError === "object" &&
        "json" in conversionError
      ) {
        try {
          const errorObj = conversionError.json;
          if (errorObj && typeof errorObj === "object") {
            if ("message" in errorObj && typeof errorObj.message === "string") {
              errorMessage = errorObj.message;
            }
            if ("context" in errorObj && typeof errorObj.context === "string") {
              errorMessage += `: ${errorObj.context}`;
            }
          }
        } catch (parseError) {
          console.error(`Error parsing error response:`, parseError);
        }
      }

      throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(errorMessage);
    }
  } catch (e: unknown) {
    console.error(`Error in convertToFlac:`, e);

    // Type guard to check if e has statusCode and message properties
    if (
      e &&
      typeof e === "object" &&
      "statusCode" in e &&
      "message" in e &&
      typeof e.message === "string"
    ) {
      throw e;
    }

    // Extract error message safely
    const errorMessage = e instanceof Error ? e.message : String(e);
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
      `Failed to convert audio file: ${errorMessage}`
    );
  }
}

// For backward compatibility, export the old function names as aliases
export const canConvertToMp3 = canConvertToFlac;
export const convertToMp3 = convertToFlac;
