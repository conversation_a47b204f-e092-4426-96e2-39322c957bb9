import { CLOUDFLARE_ACCOUNT_ID, CLOUDFLARE_API_KEY } from "@divinci-ai/server-globals";

import { HTTP_ERRORS } from "@divinci-ai/server-utils";

import { logDebug } from "@divinci-ai/utils";
import { AIAssistantAPI } from "../../../types";
import { AI_CATEGORY_ENUM } from "@divinci-ai/models";
import { GeneratorAssistantInput } from "@divinci-ai/tools";


type CF_Message = {
  role: "system" | "user" | "assistant",
  content: string,
};

interface AssistantOptions {
  model: string,
  context?: Array<CF_Message>,
  category?: AI_CATEGORY_ENUM,
  assistantName?: string,
  extraParameters?: { temperature?: number },
}

type RawCloudflareResponse = {
  result: {
    response: string,
    usage: {
      prompt_tokens: number,
      completion_tokens: number,
      total_tokens: number,
    },
  },
  success: boolean,
  errors: Array<{ code: number, message: string }>,
  messages: Array<{ code: number, message: string }>,
};

export const buildAssistant = ({
  assistantName,
  model,
  context: mandatoryContext,
  category = AI_CATEGORY_ENUM.TEXT,
  extraParameters = {}
}: AssistantOptions): AIAssistantAPI<RawCloudflareResponse>=>{
  return {
    category,
    useContext: true,
    assistantName: assistantName || model,

    generateValue: generateValue.bind(
      null, model, mandatoryContext, extraParameters
    ),
  };
};

const CF_URL = `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/ai/run/`;

async function generateValue(
  model: string,
  mandatoryContext: Array<CF_Message> | undefined,
  extraParameters: { temperature?: number },
  { thread: threadRaw }: GeneratorAssistantInput,
){
  const gptThread: Array<CF_Message> = threadRaw.map((message)=>{
    return { role: message.role, content: message.content };
  });

  if(mandatoryContext) gptThread.push(...mandatoryContext);

  const response = await fetch(
    `${CF_URL}${model}`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${CLOUDFLARE_API_KEY}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        max_tokens: Math.pow(2, 14),
        messages: gptThread,
        ...extraParameters,
      })
    }
  );

  const json = await response.json() as RawCloudflareResponse;

  logDebug("⚙⭐️ cloudflare generateValue model: ", model);
  logDebug("⚙⭐️ cloudflare generateValue thread: ", gptThread);
  logDebug("⚙⭐️ cloudflare generateValue response: ", json);

  if(!response.ok) throw HTTP_ERRORS.SERVER_ERROR;

  return {
    raw: json,
    text: json.result.response,
  };
}
