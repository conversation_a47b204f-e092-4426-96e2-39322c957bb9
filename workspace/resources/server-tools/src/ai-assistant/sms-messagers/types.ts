

export type MessageDirection = (
  | "inbound"
  | "outbound-api"
  | "outbound-call"
  | "outbound-reply"
);

export type MessageStatus = (
  | "queued"
  | "sending"
  | "sent"
  | "failed"
  | "delivered"
  | "undelivered"
  | "receiving"
  | "received"
  | "accepted"
  | "scheduled"
  | "read"
  | "partially_delivered"
  | "canceled"
);

// https://github.com/twilio/twilio-node/blob/main/src/rest/api/v2010/account/message.ts#L436
type MessageInstance = {
  body: string,
  numSegments: string,
  direction: MessageDirection,
  from: string,
  to: string,
  dateUpdated: Date,
  price: string,
  errorMessage: string,
  uri: string,
  accountSid: string,
  numMedia: string,
  status: MessageStatus,
  messagingServiceSid: string,
  sid: string,
  dateSent: Date,
  dateCreated: Date,
  errorCode: number,
  priceUnit: string,
  apiVersion: string,
  subresourceUris: Record<string, string>,
};

export type SMSMessagerType = (
  phoneNumber: string,
  message: { id: string, raw: any, text: string },
  messsageUrl: string,
)=>Promise<MessageInstance>;
