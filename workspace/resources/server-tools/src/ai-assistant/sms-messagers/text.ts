import {
  getTwi<PERSON>,
  TWILIO_PHONE_NUMBER,
} from "@divinci-ai/server-globals";
const { twilioClient } = getTwilio();

import { SMSMessagerType } from "./types";
import { logDebug } from "@divinci-ai/utils";

export const messenger: SMSMessagerType = function(
  phoneNumber, response, messageUrl
){
  logDebug("📲sendSMSMessage ai messenger messageUrl: ", messageUrl);

  let text = response.text;
  const codeText = "\nTo View this Code go to " + messageUrl + "\n";
  text = text.replaceAll(/```(.|\n)*\n```/gm, codeText);

  if(text.length >= 1000) {
    const restText = "...\n\nTo view the rest of this, go to " + messageUrl;
    const lastIndex = text.lastIndexOf(codeText);
    if(lastIndex >= 1000 - codeText.length - restText.length) {
      text = text.slice(0, lastIndex);
    }
    text = text.slice(1000).slice(0, -restText.length) + restText;
  }

  logDebug({
    from: TWILIO_PHONE_NUMBER,
    to: phoneNumber,
    body: text,
  });

  return twilioClient.messages.create({
    from: TWILIO_PHONE_NUMBER,
    to: phoneNumber,
    body: text,
  });
};
