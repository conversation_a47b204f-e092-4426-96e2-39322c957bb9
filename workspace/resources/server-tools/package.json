{"name": "@divinci-ai/server-tools", "version": "1.3.4", "description": "", "main": "dist/index.js", "typings": "src/index.ts", "scripts": {"build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "build:ci": "tsc --skipL<PERSON><PERSON><PERSON><PERSON> --project tsconfig.ci.json", "build:ignore-errors": "tsc --skipLib<PERSON>heck --noEmit || echo 'TypeScript errors ignored'", "build:ignore-errors:ci": "tsc --skipLib<PERSON>heck --noEmit --project tsconfig.ci.json || echo 'TypeScript errors ignored'", "prepare": "rimraf ./dist && tsc --skipLibCheck", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "author": "", "license": "JSON", "devDependencies": {"@types/busboy": "^1.5.4", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/node": "^22.5.2", "@types/stream-json": "^1.7.7", "@types/node-fetch": "^2.6.11", "dotenv": "^16.4.5", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.2.5", "typescript": "^5.8.3", "vitest": "^3.1.1"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@divinci-ai/models": "file:../models", "@divinci-ai/server-globals": "file:../server-globals", "@divinci-ai/server-models": "file:../server-models", "@divinci-ai/server-utils": "file:../server-utils", "@divinci-ai/tools": "file:../tools", "@divinci-ai/utils": "file:../utils", "express": "^4.19.2", "@mendable/firecrawl-js": "^1.11.2", "cheerio": "^1.0.0-rc.12", "fast-xml-parser": "^4.5.1", "form-data": "^4.0.1", "gpt-tokens": "^1.3.12", "mime-types": "^2.1.35", "mongoose": "^8.8.3", "node-fetch-commonjs": "^3.3.2", "node-html-markdown": "^1.3.0", "openai": "file:../../tarballs/openai-4.73.0.tgz", "prompt-engine": "^0.0.31", "stream-json": "^1.8.0", "zod": "^3.23.8"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b", "pnpm": {"neverBuiltDependencies": []}}