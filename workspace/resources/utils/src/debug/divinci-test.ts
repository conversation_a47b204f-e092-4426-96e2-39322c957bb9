

export const DIVINCI_TEST_ENVIRONMENT = process.env.DIVINCI_TEST_ENVIRONMENT === "true";

export type DIVINCI_TEST_PROCESS_Config = {
  delay: boolean,
  fail: boolean,
};

export const DIVINCI_TEST_PROCESS_HEADER = "x-divinci-test-process-delay";

/**
 * Adds process configuration to request headers
 * @param init - Request initialization options
 * @param config - Process configuration
 * @returns Modified request initialization options
 */
export function DIVINCI_TEST_PROCESS_AddHeader(
  init: undefined | RequestInit, config: null | DIVINCI_TEST_PROCESS_Config
){
  if(!DIVINCI_TEST_ENVIRONMENT) return init;
  if(config === null) return init;
  if(!init) init = {};
  init.headers = new Headers(init.headers || {});
  init.headers.set(DIVINCI_TEST_PROCESS_HEADER, btoa(JSON.stringify(config)));
  return init;
}

