#!/usr/bin/env ts-node

import { Command } from "commander";

import { setupEnv } from "@divinci-ai/server-utils";
import { resolve as pathResolve } from "path";
import { stat as fsStat } from "fs/promises";

const program = new Command();

program
.argument("<ticket>", "Jira Ticket (e.g., AS-382)")
.option("--env <folder>", "Environment Variables Folder", "local")

.action(async (ticket: string, { env }: { env: string })=>{
  try {
    const ticketPath = pathResolve(__dirname, "tickets", ticket, `index.ts`);
    if(!await fsExists(ticketPath)){
      throw new Error(`❌ Migration for ${ticket} not found`);
    }

    await handleEnv(env);

    const { setupDBs } = require(pathResolve(__dirname, "./setupDBs.ts"));
    await setupDBs();

    const { run: runnableTicket } = require(ticketPath);
    if(typeof runnableTicket !== "function"){
      throw new Error(`❌ Migration for ${ticket} does not have a run function`);
    }

    await runnableTicket();
    console.log(`✅ Migration ${ticket} completed.`);
    process.exit(0);
  }catch(err: any){
    console.error(err.message);
    process.exit(1);
  }
});

program.parseAsync(process.argv);

async function fsExists(path: string){
  try {
    await fsStat(path);
    return true;
  }catch(e){
    return false;
  }
}

async function handleEnv(folder: string){
  const envPath = pathResolve(__dirname, `../../../private-keys/${folder}`);
  const envStat = await fsStat(envPath);
  if(!envStat.isDirectory()){
    throw new Error(`❌ Environment folder ${envPath} is not a directory`);
  }
  setupEnv({ envPath });

  process.env.MONGO_DOMAIN_HOSTNAME = "localhost:27017";

}

