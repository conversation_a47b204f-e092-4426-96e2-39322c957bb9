
import {
  getMongoose,
  getPostgres,
} from "@divinci-ai/server-globals";

export async function setupDBs(){

  const [mongoose] = await Promise.all([
    connectToMongodb(),
    // connectToPostgres(),
  ]);

  return {
    mongoose,
  };
}

async function connectToMongodb(){
  try {
    console.log("Attempting Mongoose Connection");
    const { mongoose, connect: connectToMongoose } = getMongoose();
    await connectToMongoose();
    console.log("✅🌱 Successfully connected to MongoDB.");
    return mongoose;
  }catch(err){
    console.error("❌ Failed to connect to MongoDB: \n", err);
    throw err;
  }
}

import { buildPostgresMoney } from "@divinci-ai/server-models";
async function connectToPostgres(){
  try {
    console.log("Attempting Postgres Connection... ");
    const postgres = getPostgres();
    await postgres.query("SELECT NOW()");
    console.log("✅🌱 Successfully connected to Postgres.");
    await buildPostgresMoney();
    console.log("(╯°□°）╯︵ ┻━┻ Prepared Postgres Money Tables");

    return postgres;
  }catch(err){
    console.error("❌ Failed to connect to Postgres: \n", err);
    throw err;
  }
}
