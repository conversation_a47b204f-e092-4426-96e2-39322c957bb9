import {
  updateChunkingTool,
  updateHTMLRagFileFilenames,
  updateAudioRagFileTitles,
  updateRagByteLength
} from "./RagFiles";

const PARTS = [
  { title: "Update Chunking Tool IDs", fn: updateChunkingTool },
  { title: "Update HTML Rag Files Readable Info", fn: updateHTMLRagFileFilenames },
  { title: "Update Audio Rag Files Readable Info", fn: updateAudioRagFileTitles },
  { title: "Update Rag Files Byte Length", fn: updateRagByteLength },
];

export async function run(){
  for(const part of PARTS){
    console.log("Running:", part.title);
    await part.fn();
  }
}
