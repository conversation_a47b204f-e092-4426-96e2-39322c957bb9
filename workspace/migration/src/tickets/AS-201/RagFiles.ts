
import { RagVectorFileModel, DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { RAWFILE_TO_CHUNKS } from "@divinci-ai/server-tools";
import { RawToChunksUnstructured, RawToChunksDivinciOpenparse } from "@divinci-ai/tools";
console.log("RAWFILE_TO_CHUNKS:", RAWFILE_TO_CHUNKS.getOptions());

export async function updateChunkingTool(){
  const unstrcturedDocs = await RagVectorFileModel.find({
    chunkingTool: "unstructured",
  });

  await Promise.all(unstrcturedDocs.map(async (doc)=>{
    console.log("Updating:", doc.originalFilename);
    doc.chunkingTool = RawToChunksUnstructured.id;
    await doc.save();
  }));

  const openParseDocs = await RagVectorFileModel.find({
    chunkingTool: "openparse",
  });

  await Promise.all(openParseDocs.map(async (doc)=>{
    console.log("Updating:", doc.originalFilename);
    doc.chunkingTool = RawToChunksDivinciOpenparse.id;
    await doc.save();
  }));
}


export async function updateHTMLRagFileFilenames(){
  const htmlDocs = await RagVectorFileModel.find({
    "source.model": "RagVectorHTMLPage",
  });

  await Promise.all(htmlDocs.map(async (doc)=>{
    if(!doc.source) return;
    const expectedFilename = doc.source._id + ".html";
    if(doc.originalFilename === expectedFilename) return;

    console.log("Updating:", doc.originalFilename, "to", expectedFilename);
    doc.source.title = doc.originalFilename;
    doc.originalFilename = expectedFilename;
    await doc.save();
  }));
}

export async function updateAudioRagFileTitles(){
  const audioDocs = await RagVectorFileModel.find({
    "source.model": "DataSourceAudioTranscript",
  });

  await Promise.all(audioDocs.map(async (doc)=>{
    if(!doc.source) return;
    const audioDoc = await DataSourceAudioTranscriptModel.findById(doc.source._id);
    if(!audioDoc){
      return console.log("Audio Doc Not Found:", doc.source);
    }
    if(doc.source.title === audioDoc.sourceOrigin.info.rawValue) return;

    console.log("Updating:", doc.source.title, "to", audioDoc.sourceOrigin.info.rawValue);
    doc.source.title = audioDoc.sourceOrigin.info.rawValue as string;
    await doc.save();
  }));
}


import { getByteLengthFromS3 } from "@divinci-ai/server-utils";
import { getWhitelabelVectorR2Instance } from "@divinci-ai/server-globals";
export async function updateRagByteLength(){
  const docs = await RagVectorFileModel.find({
    originalFileByteLength: { $exists: false },
  });

  await Promise.all(docs.map(async (doc)=>{
    console.log("Updating:", doc.originalFilename);
    doc.originalFileByteLength = await getByteLengthFromS3(
      getWhitelabelVectorR2Instance(),
      { Bucket: doc.rawR2Pointer().bucket, Key: doc.rawR2Pointer().objectKey }
    );
    await doc.save();
  }));
}
