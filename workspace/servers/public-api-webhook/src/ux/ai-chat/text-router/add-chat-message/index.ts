import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { WEB_CLIENT_IS_SECURE, WEB_CLIENT_HOST } from "@divinci-ai/server-globals";

import { retrieveSMS } from "../../../../utils/phone-number";

import {
  ChatModel,
  CHAT_CATEGORY_ENUM,
  PhoneNumberModel,
  TranscriptModel,
  CHAT_CATEGORY,
  InputMessageContext
} from "@divinci-ai/server-models";

import { unravelTarget, TranscriptMessage } from "@divinci-ai/models";

import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { CHAT_AI_ASSISTANT } from "@divinci-ai/server-tools";

function messageToURL(
  chat: InstanceType<typeof ChatModel>,
  message: TranscriptMessage,
) {
  //http://localhost:8080/ai-chat/abc123#message-xyz789
  const url = new URL(
    `http://${WEB_CLIENT_HOST}/ai-chat/${chat._id}#message-${message._id}`,
  );
  if(WEB_CLIENT_IS_SECURE) url.protocol = "https:";
  return url.href;
}

export const addChatMessage: RequestHandler = async (req, res, next) => {
  console.log("📲 text-router addChatMessage");
  try {
    const { From: phoneNumber, Body: textMessage } = retrieveSMS(req);
    const phone = await PhoneNumberModel.findOne({
      phoneNumber: phoneNumber,
    }).exec();

    if(phone === null){
      throw HTTP_ERRORS.FORBIDDEN;
    }

    if(typeof phone.subscription === "undefined"){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const { category } = await TranscriptModel.categorizeText(textMessage);

    const userId = phone.ownerUser;
    const chatId = unravelTarget(phone.subscription).id;

    const chat = await ChatModel.findById(chatId);
    if(chat === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const context: InputMessageContext = await Promise.resolve().then(async function(){
      const replyTo = await getReplyTo(category, chat.transcriptId);
      if(typeof replyTo !== "undefined") return { type: "replyTo", value: replyTo };
      const assistant = CHAT_AI_ASSISTANT.getAssistant(category);
      return { type: "assistant", value: assistant.info.assistantName };
    });


    chat.addMessage(
      context,
      {
        userId,
        content: textMessage,
        phoneNumber: phoneNumber,
      },
      (chat, message: TranscriptMessage) => messageToURL(chat, message),
    );


    res.statusCode = 200;
    res.send({ status: "ok" });
  }catch(e){
    next(e);
  }
};

async function getReplyTo(
  category: CHAT_CATEGORY,
  transcriptId: string,
){
  if(category !== CHAT_CATEGORY_ENUM.TEXT && category !== CHAT_CATEGORY_ENUM.UNKNOWN) return;
  const transcript = await TranscriptModel.findById(
    transcriptId,
  );

  if(!transcript) throw HTTP_ERRORS.NOT_FOUND;

  const lastSent = transcript.messages.reverse().find((message: TranscriptMessage)=>(
    message.role === "assistant" &&
    message.category === CHAT_CATEGORY_ENUM.TEXT
  ));

  if(typeof lastSent === "undefined") return;

  return lastSent._id;
}
