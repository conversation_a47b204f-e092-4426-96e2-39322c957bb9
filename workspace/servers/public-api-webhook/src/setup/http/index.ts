import express, { Router } from "express";
import cors from "cors";

import { handleSMSBody } from "../../utils/phone-number";

import { router as userTextRouter } from "../../ux/user/text-router";
import { router as aiChatTextRouter } from "../../ux/ai-chat/text-router";

import { createCorsOptions } from "@divinci-ai/server-globals";
import { notFoundHandler, errorHandler } from "./common-handlers";

export async function setupHttpApp(){
  const app = express();

  const corsOptions = createCorsOptions();

  // app.use(morgan('combined'));
  app.use(cors(corsOptions));

  // https://github.com/microsoft/TypeScript/issues/9458
  app.get('/', function (_, res) {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    res.json({
      "hello": "world",
      "whoami": "an api webhook server!",
      "server": process.env.HELLO_WORLD
    });
  });
  const smsRouter = Router();
  app.post("/twilio-sms", handleSMSBody, smsRouter);
  smsRouter.use(userTextRouter);
  smsRouter.use(aiChatTextRouter);


  app.use(notFoundHandler);
  app.use(errorHandler);

  return app;
}
