import { Request, Response, NextFunction } from "express";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";


export const notFoundHandler = (req: Request, res: Response, next: NextFunction)=>{
  next(HTTP_ERRORS.NOT_FOUND);
};


export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  if(typeof err !== "object") {
    err = {
      status: HTTP_ERRORS.SERVER_ERROR.statusCode,
      message: "non-object error",
    };
  }
  if(err === null){
    err = {
      status: HTTP_ERRORS.SERVER_ERROR.statusCode,
      message: "non-object error",
    };
  }
  if(Array.isArray(err)){
    err = {
      status: HTTP_ERRORS.SERVER_ERROR.statusCode,
      message: "non-object error",
    };
  }
  const status = err.statusCode || err.status || HTTP_ERRORS.SERVER_ERROR.statusCode;
  const message = err.message || err.statusMessage || "Unknown error";
  const context = err.context || {};

  // Enhanced logging
  console.error("❌ [Error Handler] Request URL:", req.originalUrl);
  console.error("❌ [Error Handler] Request Method:", req.method);
  console.error("❌ [Error Handler] Status:", status);
  console.error("❌ [Error Handler] Message:", message);

  if(err.stack) {
    console.error("❌ [Error Handler] Stack Trace:", err.stack);
  }

  // Logging all properties of the error object for detailed debugging
  for(const key in err) {
    // eslint-disable-next-line no-prototype-builtins
    if(err.hasOwnProperty(key) && !["status", "message", "context", "stack"].includes(key)) {
      console.error(`❌ [Error Handler] ${key}:`, err[key]);
    }
  }

  // If the status is 500 or greater, it's a server error
  if(status >= 500) {
    console.error("❌ Internal Error. If in production, notify the team:", err);
  }

  res.statusCode = status;
  return res.json({
    status: "error",
    message: message,
    context: context
  });
};
