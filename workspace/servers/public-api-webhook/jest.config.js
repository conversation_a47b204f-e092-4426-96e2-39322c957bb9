module.exports = {
    verbose: true,
    testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$',
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
    setupFilesAfterEnv: ['./jest.setup.js'],
    transform: {
      '^.+\\.tsx?$': 'ts-jest',
    },
    transformIgnorePatterns: [
      "/node_modules/(?!react-markdown|vfile|unist-util-stringify-position|unified|bail).+\\.js$"
    ],
    // globals: {
    //   'ts-jest': {
    //     tsconfig: '<rootDir>/workspace/clients/web/tsconfig.json'
    //   }
    // },
    // testNamePattern: "skip",
    // reporters: ['default', '<rootDir>/jestReporter.js'],
    // testEnvironment: "jsdom",
};
