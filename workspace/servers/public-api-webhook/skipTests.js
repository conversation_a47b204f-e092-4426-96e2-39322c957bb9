const fs = require('fs');
const path = require('path');
const stripAnsi = require('strip-ansi');
const { glob } = require('glob');

const directoryPath = process.argv[2]; // Get the directory path argument from the command line
const jestOutputPath = directoryPath ? path.join(directoryPath, 'jest-output.txt') : './jest-output.txt';

let output;
try {
    output = fs.readFileSync(jestOutputPath, 'utf8');
} catch (error) {
    console.error(`Could not find jest-output.txt at ${jestOutputPath}. Please provide a directory path as an argument if jest-output.txt is located elsewhere.`);
    process.exit(1);
}
const cleanOutput = stripAnsi(output);

// Example jest-output.txt:
// [7m11[0m     await clientError(mockReq, mockRes, mockNext);
//     [7m  [0m [91m                      ~~~~~~~[0m
//     [96m../../servers/public-api/tests/unit/src/ux/issue/router/client-error/clientError.test.ts[0m:[93m28[0m:[93m23[0m - [91merror[0m[90m TS2345: [0mArgument of type '{}' is not assignable to parameter of type 'IncomingMessage'.
//       Type '{}' is missing the following properties from type 'IncomingMessage': aborted, httpVersion, httpVersionMajor, httpVersionMinor, and 51 more.

//     [7m28[0m     await clientError(mockReq, mockRes, mockNext);
//     [7m  [0m [91m                      ~~~~~~~[0m

// Updated regex with a capturing group for the file path
const regex = /tests\/unit\/src\/(.*\.test\.ts):\d+:\d+ - error/g;

// Extract matching file paths from cleanOutput
const erroredFiles = Array.from(cleanOutput.matchAll(regex), m => m[1]);

(async () => {
    const files = await glob("tests/unit/src/**/*.test.{ts,tsx}", { cwd: __dirname });
    console.log("🗄️ files: ", files);

    if (erroredFiles) {
        erroredFiles.forEach(errorLine => {
            const filePath = errorLine.split(' ')[0];  // Extract the file path part
            console.log("🔍 filePath: ", filePath);
            const relativePath = filePath.replace(/^..\/..\/[^\/]+\/[^\/]+\/tests\/unit\/src\//, '');
            console.log("🗑️ relativePath: ", relativePath);

            // Check if the file exists in the glob list
            if (files.includes(`tests/unit/src/${relativePath}`)) {
                const absoluteFilePath = path.resolve(__dirname, `tests/unit/src/${relativePath}`);
                const newFilePath = absoluteFilePath.replace('.test.', '.test.skip.');
                try {
                    fs.renameSync(absoluteFilePath, newFilePath);
                    console.log(`📛 Renamed ${absoluteFilePath} to ${newFilePath}`);
                } catch (error) {
                    console.error(`❌ Failed to rename ${absoluteFilePath} to ${newFilePath}:`, error);
                }
            }
        });
    }
})();
