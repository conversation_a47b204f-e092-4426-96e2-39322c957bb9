
const fs = require("fs");
const path = require('path');

module.exports.specifiedModules = ["node:stream", "node:crypto"];

// Resolve paths relative to the rollup-config folder
// eslint-disable-next-line @typescript-eslint/no-var-requires
module.exports.nodeBuiltins = require('module').builtinModules.filter((module) => !module.startsWith('_'));

// Check for native modules that may use node-gyp
module.exports.getNativeModules = function getNativeModules(dirPath){
  const dirPackage = JSON.parse(fs.readFileSync(path.join(dirPath, 'package.json')));
  return Object.keys(dirPackage.dependencies || {}).filter((mod) => {
    const pkgJsonPath = path.join(dirPath, `node_modules/${mod}/package.json`);
    if(!fs.existsSync(pkgJsonPath)) return false;
    const pkgJson = JSON.parse(fs.readFileSync(pkgJsonPath));
    if(typeof pkgJson !== "object") return false;
    return "gypfile" in pkgJson;
  });
};
