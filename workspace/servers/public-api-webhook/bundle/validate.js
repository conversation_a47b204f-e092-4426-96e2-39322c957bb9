const fs = require("fs");
const path = require('path');

module.exports.validateInputFolder = function(folder){
  folder = path.resolve(process.cwd(), folder);
  if(!validateFolder(folder)){
    throw new Error(`Invalid input folder: ${folder}`);
  }
  const file = getTypescriptIndexPath(folder);
  if(!file){
    throw new Error(`No TypeScript file found in the folder: ${folder}`);
  }
  const appPath = path.resolve(folder, "./src/app.ts");
  if(!fs.existsSync(appPath)){
    throw new Error(`Input file doesn't exist: ${appPath}`);
  }
  const envPath = path.resolve(folder, "./src/env.ts");
  if(!fs.existsSync(envPath)){
    throw new Error(`Input file doesn't exist: ${envPath}`);
  }

  return { folder, file, appPath, envPath };
};

module.exports.validateOutputFolder = function(folder){
  folder = path.resolve(process.cwd(), folder);
  if(!validateFolder(folder)){
    throw new Error(`Invalid output folder: ${folder}`);
  }
  const appPath = path.resolve(folder, "./app.bundle.js");
  if(fs.existsSync(appPath)){
    throw new Error(`Output file already exists: ${appPath}`);
  }
  const envPath = path.resolve(folder, "./env.bundle.js");
  if(fs.existsSync(envPath)){
    throw new Error(`Output file already exists: ${envPath}`);
  }

  return { folder, appPath, envPath };
};

function validateFolder(serverDir){
  if(!fs.existsSync(serverDir)){
    return false;
  }
  if(!fs.statSync(serverDir).isDirectory()){
    return false;
  }

  return true;
}



function getTypescriptIndexPath(dirPath){
  const tsconfigPath = path.join(dirPath, "tsconfig.json");
  if(!fs.existsSync(tsconfigPath)) return null;
  const json = JSON.parse(fs.readFileSync(tsconfigPath, "utf8"));
  if(!("files" in json) || !Array.isArray(json.files)) return null;
  if(json.files.length === 0) return null;
  const file = json.files[0];
  const indexPath = path.resolve(dirPath, file);
  if(!fs.existsSync(indexPath)) return null;
  return indexPath;
}
