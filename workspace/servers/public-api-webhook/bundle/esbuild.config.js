
const { build } = require("esbuild");

const { validateInputFolder, validateOutputFolder } = require("./validate");
const { specifiedModules, nodeBuiltins: nodeBuiltinsUnclean, getNativeModules } = require("./skip-modules");

const [IN_FOLDER, OUT_FOLDER] = process.argv.slice(2);

const { folder: inputFolder, appPath: inputAppPath, envPath: inputEnvPath } = validateInputFolder(IN_FOLDER);
const { folder: outputFolder, appPath: outputAppPath, envPath: outputEnvPath } = validateOutputFolder(OUT_FOLDER);

const requiredModules = ["string_decoder", "punycode"];
const nodeBuiltins = nodeBuiltinsUnclean.filter((m)=>(!requiredModules.includes(m)));
const nativeModules = getNativeModules(inputFolder).filter((m)=>(!requiredModules.includes(m)));

console.log("Input folder:", inputFolder);
console.log("Output folder:", outputFolder);
console.log("Not Native:", requiredModules);
console.log("Node builtins:", nodeBuiltins);
console.log("Native modules:", nativeModules);

/*
=============================
BUILD Environement Variable Logger
=============================
*/
build({
  sourcemap: true,
  tsconfig: `${inputFolder}/tsconfig.json`,
  entryPoints: [inputEnvPath], // Path to your main server file
  outfile: outputEnvPath, // Output file
  platform: "node", // Specify 'node' platform for server-side
  target: "node22", // Target Node.js 14
  bundle: true, // Bundle into one file
  minify: process.env.NODE_ENV === "production", // Optional: Minify to reduce file size
  treeShaking: true, // Enable tree shaking
  external: [
    // Exclude external dependencies
    ...specifiedModules,
    ...nodeBuiltins,
    ...nativeModules,
  ],
  loader: { ".wasm": "file" }, // Tell esbuild to treat .wasm files as binary files
}).catch((e) => {
  console.error(e);
  process.exit(1);
});

/*
=============================
BUILD Application
=============================
*/
build({
  sourcemap: true,
  tsconfig: `${inputFolder}/tsconfig.json`,
  entryPoints: [inputAppPath], // Path to your main server file
  outfile: outputAppPath, // Output file
  platform: "node", // Specify 'node' platform for server-side
  target: "node22", // Target Node.js 14
  bundle: true, // Bundle into one file
  minify: process.env.NODE_ENV === "production", // Optional: Minify to reduce file size
  treeShaking: true, // Enable tree shaking
  external: [
    // Exclude external dependencies
    ...specifiedModules,
    ...nodeBuiltins,
    ...nativeModules,
  ],
  loader: { ".wasm": "file" }, // Tell esbuild to treat .wasm files as binary files
}).catch((e) => {
  console.error(e);
  process.exit(1);
});
