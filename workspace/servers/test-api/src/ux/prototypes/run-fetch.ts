

import fetch from "node-fetch-commonjs";
import FormData from 'form-data';

import { prepareFileStream } from "./prepare-file-stream";
(async function(){

  const STREAM_ITEM_LENGTH = 25;
  const HTTP_PORT = "18084";

  console.log("Preparing ");

  const body = new FormData();
  body.append("name", `test ${STREAM_ITEM_LENGTH}`);
  body.append('jsonFile', prepareFileStream(STREAM_ITEM_LENGTH), `stream-items.json`);

  console.log("About to fetch:", `http://localhost:${HTTP_PORT}/prototype/handle-fetch-stream`);

  const response = await fetch(`http://localhost:${HTTP_PORT}/prototype/handle-fetch-stream`, {
    method: "POST",
    headers: body.getHeaders(),
    body: body,
  });

  const json = await response.json();

  if(!response.ok) throw json;

  console.log("success", json);
})();
