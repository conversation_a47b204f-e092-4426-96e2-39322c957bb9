
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { CastedBusBoy } from "@divinci-ai/server-utils";

import { BufferBusBoyFileHandler } from "./buffer-file-handler";

const busboyHandleBody = CastedBusBoy.create(
  { name: "string", jsonFile: "file" },
  new BufferBusBoyFileHandler()
);

export const handleFetchStream: RequestHandler = async function(req, res, next){
  try {
    console.log("recieved request");
    const { name, jsonFile } = await busboyHandleBody.consumeRequest(req);
    console.log("handled body");

    const value = JSON.parse(jsonFile.buffer.join(""));
    console.log("parsed json:", value);


    res.status(200).json({ name, numChunks: jsonFile.buffer.length });

  }catch(e){
    next(e);
  }
};

import fetch from "node-fetch-commonjs";
import FormData from 'form-data';
import { prepareFileStream } from "./prepare-file-stream";
const STREAM_ITEM_LENGTH = 10;
export const runFetchStream: RequestHandler = async function(req, res, next){
  try {

    console.log("Preparing ");

    const body = new FormData();
    body.append("name", `test ${STREAM_ITEM_LENGTH}`);
    body.append('jsonFile', prepareFileStream(STREAM_ITEM_LENGTH), `stream-items.json`);

    console.log("About to fetch:", `http://localhost:${process.env.HTTP_PORT}/prototype/handle-fetch-stream`);

    const response = await fetch(`http://localhost:${process.env.HTTP_PORT}/prototype/handle-fetch-stream`, {
      method: "POST",
      headers: body.getHeaders(),
      body: body as any,
    });

    console.log("Parsing JSON");

    const json = await response.json();

    console.log("Throw or Return");

    if(!response.ok) throw json;

    res.status(200).json({ status: "ok", response: json });

  }catch(e){
    next(e);
  }
};

