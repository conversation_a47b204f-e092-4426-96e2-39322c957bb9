import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { get<PERSON><PERSON>oose, qdrantFetch } from '@divinci-ai/server-globals';
const { mongoose } = getMongoose();

export const clearDB: RequestHandler = async (req, res, next) => {
  try {
    // await mongoose.connection.dropDatabase();

    await Promise.all([
      clearMongoDB(),
      clearQdrant(),
    ]);


    res.send({ status: "ok" });
  }catch(e){
    next(e);
  }

};

function clearMongoDB(){
  const collections = mongoose.connection.collections;
  return Promise.all(Object.values(collections).map((collection) =>
    collection.deleteMany({})
  ));
}

async function clearQdrant(){
  const { result: { collections } } = await qdrantFetch<{ collections: Array<{ name: string }> }>("/collections");
  return Promise.all(collections.map(({ name }) =>
    qdrantFetch(`/collections/${name}`, { method: "DELETE" })
  ));
}

/*

async function runFetch(url){
  const response = await fetch(url);
  const json = await response.json();
  if(!response.ok) throw json;
  return json;
}


http://localhost:18084/prototype/run-fetch-stream
*/
