
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { calculateUsage } from "@divinci-ai/models";

import { HTTP_ERRORS_WITH_CONTEXT, jsonExtraResponse, jsonBody } from "@divinci-ai/server-utils";
import { TranscriptValueWalletModel, WalletConfig, WithdrawableWalletModel } from "@divinci-ai/server-models";
import { getUserId } from "@divinci-ai/server-globals";

import { PAYOUT_CONFIG } from "@divinci-ai/server-models";
import { TIME_MONTH_TIME, castShallowObject } from "@divinci-ai/utils";

export const makeTranscriptValueWalletFreeGoBroke: RequestHandler = async function (req, res, next){
  try {
    const userId = getUserId(req);

    const [body, wallet, subscription] = await Promise.all([
      jsonBody(req),
      TranscriptValueWalletModel.findOrCreate(userId),
      TranscriptValueWalletModel.getSubscriptionStatus(userId),
    ]);

    if(!subscription.active){
      console.warn("Inactive subscription, can still continue");
    }

    const { recieverId } = castShallowObject(body, { recieverId: "string?" });

    const freeUsage = calculateUsage(wallet.freeTransactions, Date.now() - TIME_MONTH_TIME);
    const paidUsage = calculateUsage(wallet.paidTransactions, subscription.startTimestamp);

    if(paidUsage > 0n){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Using up monthly subscription value");
    }

    const { maxFree } = TranscriptValueWalletModel.calculateMaximums(subscription);

    const totalValue = maxFree - freeUsage;

    if(totalValue <= 0){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Already maxed out free usage");
    }

    const walletConfig: WalletConfig = {
      payer: { wallet: wallet, callback: (payment, inputOrOutput)=>({
        walletOwner: userId, runnerUser: userId, timestamp: Date.now(),
        amount: payment.amount, maximum: payment.maximum, toUSDDivisor: payment.divisor,
        meta: {
          targetUsage: `go-free-broke`,
          inputOrOutput: inputOrOutput,
        }
      }) },
      reciever: typeof recieverId === "undefined" ? void 0 : {
        wallet: await WithdrawableWalletModel.findOrCreate(recieverId),
        callback: (payment, inputOrOutput)=>({
          fromUser: userId,
          toUser: recieverId,

          timestamp: Date.now(),
          finished: -1,

          transactionType: "send",
          amount: payment.amount,
          toUSDDivisor: payment.divisor,

          meta: {
            type: "transcript",
            parent: "debug",
            release: "debug",
            targetUsage: "go-free-broke",
            inputOrOutput: inputOrOutput,
          }
        })
      }
    };

    const inputValue = totalValue / 2n;
    const outputValue = totalValue - inputValue;

    const { input, output } = await TranscriptValueWalletModel.wrapTransaction(
      walletConfig, PAYOUT_CONFIG,
      async ()=>({ value: inputValue }),
      async ()=>({ value: outputValue })
    );

    res.statusCode = 200;
    jsonExtraResponse(res, { input, output });
  }catch(e){
    next(e);
  }
};
