

import { runNanoUSDStripeDeposit } from "@divinci-ai/server-models";
import { getParam, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject, uniqueId } from "@divinci-ai/utils";

import { isDivinciEmail } from "../../util/user";
import { getUserById, getUserId } from "@divinci-ai/server-globals";
import { RequestHandler } from "express";

/**
 * Add funds to a user's NanoUSD wallet for testing (server-side operation)
 */

export const addFundsToCurrentUserWallet: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const user = await getUserById(userId);

    isDivinciEmail(user.email);

    const body = await jsonBody(req);

    const { funds } = castShallowObject(body, { funds: "string" });

    await runNanoUSDStripeDeposit(`test-${uniqueId()}`, userId, BigInt(funds));

    res.statusCode = 200;
    res.json({ status: "ok" });

  }catch(e){
    next(e);
  }
};

export const addFundsToTargetUserWallet: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getParam(req, "userId");
    const user = await getUserById(userId);
    isDivinciEmail(user.email);

    const body = await jsonBody(req);

    const { funds } = castShallowObject(body, { funds: "string" });

    await runNanoUSDStripeDeposit(`test-${uniqueId()}`, userId, BigInt(funds));

    res.statusCode = 200;
    res.json({ status: "ok" });

  }catch(e){
    next(e);
  }
};
