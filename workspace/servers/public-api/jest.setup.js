const path = require("path");
const fs = require("fs");
const dotenv = require("dotenv");

function getEnvDir(){
  let projectRoot;
  if(process.env.GITHUB_WORKSPACE) {
    // In GitHub Actions, use the GITHUB_WORKSPACE environment variable
    projectRoot = process.env.GITHUB_WORKSPACE;
    console.log("🙋🏻‍♂️ Using GITHUB_WORKSPACE environment variable: " + projectRoot);
    // const parentDir = path.resolve(projectRoot, '..');
    // console.log('🙋🏻‍♂️ Parent directory of GITHUB_WORKSPACE list dir: ', fs.readdirSync(parentDir));
  } else {
    // Locally, calculate the project root based on the directory structure
    projectRoot = path.resolve(__dirname, "./../../../");
    console.log("🙋🏻‍♂️❔ Using project root: " + projectRoot);
  }
  return path.join(projectRoot, "private-keys/local");
}

const envDir = getEnvDir();

// const currentWorkingDirectory = process.cwd();
// console.log("📛↕️ currentWorkingDirectory: ", currentWorkingDirectory);


// Read the directory and filter out files that don't end with '.env'
const envFiles = fs.readdirSync(envDir).filter(file=>file.endsWith(".env"));

// Load each .env file
envFiles.forEach(file=>{
  const envPath = path.join(envDir, file);
  console.log("⬇️  Loading environment variables from:", envPath);
  dotenv.config({ path: envPath });
});
