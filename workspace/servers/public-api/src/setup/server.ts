import { Server } from "http";
import { Server as HTTPSServer } from "https";
import { createSecureContext } from "tls";
import { constants as cryptoConstants } from "crypto";
import { join as pathJoin } from "path";
import { existsSync as fsExistsSync, readFileSync as fsReadFileSync } from "fs";

export const ENABLE_MTLS = envIsTrue(process.env.ENABLE_MTLS);


export function setupServer(): Server | HTTPSServer{
  // Create a new env variable to specifically bypass mTLS for OPTIONS
  process.env.MTLS_BYPASS_OPTIONS = "true";

  if(!ENABLE_MTLS){
    // Use regular HTTP server
    console.log("🔑 mTLS is disabled, using regular HTTP server");
    return new Server();
  }
  // Enable server-only TLS mode by default (simplifies configuration)
  process.env.ENABLE_SERVER_ONLY_TLS = process.env.ENABLE_SERVER_ONLY_TLS || "true";

  // Setup HTTPS with mTLS
  console.log("🔐 mTLS is enabled");
  console.log("🔑 mTLS OPTIONS bypass:", process.env.MTLS_BYPASS_OPTIONS === "true" ? "enabled" : "disabled");

  // Base directory for mTLS certificates
  // In production/staging: Set MTLS_CERT_DIR=/etc/ssl to use standard Linux SSL paths
  // In local development: Defaults to /private-keys/{env}/certs/mtls if MTLS_CERT_DIR is not set
  // This is set in private-keys/staging/endpoints.shared.env for staging environment
  const certDir = process.env.MTLS_CERT_DIR || `/private-keys/${process.env.ENVIRONMENT || "local"}/certs/mtls`;
  console.log("📎 Certificate directory:", certDir);

  // Read the certificate files
  const options: any = {
    // Add this option to help with possible encoding issues with TLS 1.3
    secureOptions: cryptoConstants.SSL_OP_NO_TLSv1_3
  };

  // Try to load server certificate and key
  try {
    // First try direct paths (flat structure)
    // If MTLS_CERT_DIR=/etc/ssl, this will look for /etc/ssl/server.crt and /etc/ssl/server.key
    let certPath = pathJoin(certDir, "server.crt");
    let keyPath = pathJoin(certDir, "server.key");

    // If direct paths don't exist, try the nested structure (standard Linux SSL paths)
    // If MTLS_CERT_DIR=/etc/ssl, this will look for /etc/ssl/certs/server.crt
    if(!fsExistsSync(certPath)) {
      certPath = pathJoin(certDir, "certs", "server.crt");
    }

    // If MTLS_CERT_DIR=/etc/ssl, this will look for /etc/ssl/private/server.key
    if(!fsExistsSync(keyPath)) {
      keyPath = pathJoin(certDir, "private", "server.key");
    }

    // Try to load server certificate
    console.log("🔍 Looking for server certificate at", certPath);
    if(!fsExistsSync(certPath)) {
      console.error("❌ Server certificate not found at", certPath);
      throw new Error("Server certificate not found at " + certPath);
    }

    try {
      const certData = fsReadFileSync(certPath, "utf8");
      console.log("📄 Certificate content (first 100 chars):", certData.substring(0, 100));

      // Check if certificate is in PEM format
      if(!certData.includes("-----BEGIN CERTIFICATE-----")) {
        console.warn("⚠️ WARNING: Certificate does not appear to be in PEM format!");
        console.log("📊 Certificate format check:");
        console.log("  - Starts with BEGIN CERTIFICATE:", certData.includes("-----BEGIN CERTIFICATE-----"));
        console.log("  - Contains line breaks:", certData.includes("\n"));
        console.log("  - Length:", certData.length);
      }

      options.cert = certData;
      console.log("✅ Loaded server certificate from", certPath);
    }catch(error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("❌ Error loading server certificate:", errorMessage);
      throw new Error("Failed to load server certificate: " + errorMessage);
    }

    // Try to load server key
    console.log("🔍 Looking for server key at", keyPath);
    if(!fsExistsSync(keyPath)) {
      console.error("❌ Server key not found at", keyPath);
      throw new Error("Server key not found at " + keyPath);
    }
    try {
      const keyData = fsReadFileSync(keyPath, "utf8");
      console.log("📄 Key content (first 100 chars):", keyData.substring(0, 100));

      // Check if key is in PEM format
      if(!keyData.includes("-----BEGIN PRIVATE KEY-----") &&
          !keyData.includes("-----BEGIN RSA PRIVATE KEY-----")) {
        console.warn("⚠️ WARNING: Private key does not appear to be in PEM format!");
        console.log("📊 Key format check:");
        console.log("  - Starts with BEGIN PRIVATE KEY:",
          keyData.includes("-----BEGIN PRIVATE KEY-----") ||
          keyData.includes("-----BEGIN RSA PRIVATE KEY-----"));
        console.log("  - Contains line breaks:", keyData.includes("\n"));
        console.log("  - Length:", keyData.length);
      }

      options.key = keyData;
      console.log("✅ Loaded server key from", keyPath);
    }catch(error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("❌ Error loading server key:", errorMessage);
      throw new Error("Failed to load server key: " + errorMessage);
    }

    // Server-only mTLS setup - don't verify client certificates
    // This is a simplified setup that only uses the server certificate
    // for TLS encryption but doesn't authenticate clients

    console.log("🔐 Setting up server-only mTLS (no client certificate verification)");

    // Set options for TLS without client verification
    options.requestCert = false;  // Don't request client certificates
    options.rejectUnauthorized = false;  // Don't reject clients without certificates

    // Setup a basic SNICallback that allows all requests through
    options.SNICallback = (servername: string, cb: any)=>{
      // Create secure context with server credentials only
      const serverOnlyContext = createSecureContext({
        cert: options.cert,
        key: options.key,
      });

      if(cb) {
        // Callback style
        process.nextTick(()=>cb(null, serverOnlyContext));
      } else {
        // Return style
        return serverOnlyContext;
      }
    };

    console.log("🔐 Set up server-only TLS context that allows all clients");

    // Create HTTPS server with the options
    console.log("🔐 Creating HTTPS server with mTLS options");
    return new HTTPSServer(options);
  }catch(error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("❌ Error setting up mTLS:", errorMessage);
    console.log("⚠️ Falling back to regular HTTP server");
    return new Server();
  }
}

function envIsTrue(envVar: undefined | string){
  return envVar === "true" || envVar === "1";
}
