import { Request, Response, NextFunction } from "express";

/**
 * Middleware to add CORS headers for local development
 * This is a simple middleware that adds CORS headers to all responses
 * when running in the local environment
 */
export function localCorsMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  // Only apply in local environment
  if (
    process.env.ENVIRONMENT === "local" ||
    process.env.NODE_ENV === "development" ||
    process.env.NODE_ENV === "local"
  ) {
    // Get the origin from the request
    const origin = req.headers.origin;

    // Log remote address for debugging
    console.log(`🪪 remote address: `, req.socket.remoteAddress);

    // Set CORS headers for all origins in local development
    // This is needed for worker services to communicate with the API
    const allowedOrigins = [
      /^http:\/\/localhost(:\d+)?$/,
      /^http:\/\/127\.0\.0\.1(:\d+)?$/,
      /^http:\/\/local-[a-zA-Z0-9-]+(:\d+)?$/,
      /^http:\/\/[a-zA-Z0-9-]+\.divinci\.local(:\d+)?$/,
      // GitHub Codespaces patterns
      /^https:\/\/[a-zA-Z0-9-]+-\d+\.app\.github\.dev$/,
    ];

    // Check if the origin is allowed
    const isAllowedOrigin =
      origin && allowedOrigins.some((pattern) => pattern.test(origin));

    if (isAllowedOrigin) {
      // Set CORS headers for allowed origins
      res.header("Access-Control-Allow-Origin", origin);
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS, PATCH"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
      );
      res.header("Access-Control-Allow-Credentials", "true");
      res.header("Access-Control-Max-Age", "86400"); // 24 hours
      res.header(
        "Access-Control-Expose-Headers",
        "x-file-name, x-file-id, x-target, x-processor, x-vectorize-config, x-processor-config, x-debug-client, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
      );
    } else if (!origin) {
      // For requests without an origin (like from a worker), set permissive CORS headers
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS, PATCH"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
      );
      res.header("Access-Control-Max-Age", "86400"); // 24 hours
      res.header(
        "Access-Control-Expose-Headers",
        "x-file-name, x-file-id, x-target, x-processor, x-vectorize-config, x-processor-config, x-debug-client, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
      );
      console.log(
        "🌐 Setting permissive CORS headers for request without origin"
      );
    }

    // In BARE METAL MODE, we still need to authenticate Cloudflare Workers.
    // In production, this is handled by Cloudflare's authentication system.
    // In local development, we use a special header to simulate this authentication.
    if (req.headers["cloudflare-worker-x-dev-auth"]) {
      console.log(
        `🔑 Cloudflare Worker dev auth detected - setting auth payload`
      );
      // Add a proper auth payload for local development
      (req as any).auth = {
        payload: {
          sub: "cloudflare-worker-local-dev",
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + 3600,
          iss: "https://divinci.us.auth0.com/",
          aud: "https://api.divinci.ai",
        },
      };
    } else if (!req.headers.authorization) {
      // Check and log if no auth is present
      console.log(`🙅🏻‍♂️🪪 No auth in req: `, req.headers.authorization);
    }

    // Handle OPTIONS requests immediately, but only if no headers have been sent yet
    if (req.method === "OPTIONS" && !res.headersSent) {
      console.log(
        "🌐 Local CORS Middleware - Handling OPTIONS preflight request"
      );
      res.status(204).end();
      return;
    }
  }

  // Continue to the next middleware
  next();
}
