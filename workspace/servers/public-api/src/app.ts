import { setupGlobalErrorCatchers } from "./setup/error";
import { setupDBs } from "./setup/database";
import { setupHttpApp } from "./setup/http";
import { setupServer, ENABLE_MTLS } from "./setup/server";

import { DIVINCI_TEST_PROCESS_DELAY, applyCorsHeaders, createCorsOptions } from "@divinci-ai/server-globals";
import express from "express";
import cors from "cors";

Promise.resolve().then(async ()=>{
  setupGlobalErrorCatchers();

  await attemptExternal();

  await setupDBs();

  const httpApp = await setupHttpApp();


  const server = setupServer();

  // Create a separate server for OPTIONS requests if mTLS is enabled
  if(server instanceof HTTPSServer && process.env.MTLS_BYPASS_OPTIONS === "true") {
    console.log("🚀 Creating separate HTTP server for OPTIONS requests to bypass mTLS entirely");

    try {
      // Create a simplified express app just for OPTIONS
      const optionsApp = express();

      // Add CORS middleware
      optionsApp.use(cors(createCorsOptions()));

      // Add a catch-all middleware to handle ALL requests, not just OPTIONS
      // This will convert any request to an OPTIONS response
      optionsApp.use((req, res)=>{
        // console.log("⭐ Handling request on dedicated OPTIONS server:", req.method, req.path);

        // Apply CORS headers for any request type
        applyCorsHeaders(req, res);

        // Always return 204 No Content
        if(!res.writableEnded) {
          res.status(204).end();
        }
      });

      // Create HTTP server (not HTTPS) for OPTIONS
      const optionsServer = new Server();

      // Handle all requests with the options app
      optionsServer.on("request", optionsApp);

      // Listen on same port as main server (!) - this won't work in typical setups
      // Instead, we need to rely on a load balancer or proxy to route OPTIONS requests
      // to this separate server
      const OPTIONS_PORT = process.env.OPTIONS_PORT ? parseInt(process.env.OPTIONS_PORT) : 8082;
      optionsServer.listen(OPTIONS_PORT, ()=>{
        // console.log(`✅ OPTIONS server running on port ${OPTIONS_PORT}`);
      });
    }catch(error) {
      console.error("❌ Error setting up OPTIONS server:", error);
    }
  }

  server.on("request", function(req: IncomingMessage, res: ServerResponse){
    // console.log("🌐 url:", req.method, req.url);
    // https://stackoverflow.com/questions/8107856/how-to-determine-a-users-ip-address-in-node
    // issue is that it seems docker always returns a docker address
    console.log("🪪 remote address: ", req.socket.remoteAddress);

    // Always allow OPTIONS requests to bypass mTLS checks
    if(req.method === "OPTIONS") {
      // console.log("✅ Allowing OPTIONS request to bypass mTLS checks");
      // Use the centralized CORS function from server-globals
      applyCorsHeaders(req, res);

      // Ensure the OPTIONS request is properly closed with a 204 status code
      if(!res.writableEnded) {
        res.writeHead(204);
        res.end();
      }
      return; // Return early, bypassing mTLS checks
    }

    // Use the centralized CORS function from server-globals for non-OPTIONS requests
    const isOriginAllowed = applyCorsHeaders(req, res);

    // Log the result for debugging
    if(!isOriginAllowed && req.headers.origin) {
      console.log(`❌ Origin not allowed by CORS: ${req.headers.origin}`);
    }

    // For mTLS, just verify the connection is encrypted
    if(ENABLE_MTLS) {
      const socketAsTLS = req.socket as TLSSocket;

      // Always completely bypass mTLS for OPTIONS requests
      if(req.method === "OPTIONS") {
        // console.log("✅ OPTIONS request - bypassing mTLS verification completely");
      }
      // For non-OPTIONS requests, just check that TLS is established
      else if(socketAsTLS.encrypted) {
        console.log("✅ TLS connection established for", req.method, req.url);
      } else {
        console.log("⚠️ Non-encrypted connection for", req.method, req.url);
        // This shouldn't happen with mTLS enabled, but just in case
        if(process.env.REQUIRE_TLS === "true") {
          res.writeHead(400, {
            "Content-Type": "application/json"
          });
          res.end(JSON.stringify({
            error: "Bad Request - TLS required",
            code: "TLS_REQUIRED"
          }));
          return;
        }
      }
    }

    httpApp(req, res);
  });

  const PORT = process.env.HTTP_PORT ? parseInt(process.env.HTTP_PORT) : 8081;
  await waitForListen(server, PORT);
  console.log(`✅ Server is running on port: ${PORT}`);
  if(DIVINCI_TEST_PROCESS_DELAY > 0){
    console.log(`🚦 Waiting ${DIVINCI_TEST_PROCESS_DELAY}ms for tests to run...`);
  }
})
.catch((error)=>{
  console.error("❌ Error while starting the server: ", error);
});

import { delay } from "@divinci-ai/utils";
import { IncomingMessage, Server, ServerResponse } from "http";
import { Server as HTTPSServer } from "https";
import { TLSSocket } from "tls";

function waitForListen(server: Server, port: number){
  const p = new Promise<void>((res, rej)=>{
    server.on("listening", res);
    server.on("error", rej);
  });
  server.listen(port);
  return p;
}

function attemptExternal(){
  return Promise.race([
    Promise.resolve().then(async ()=>{
      await delay(5 * 1000);
      throw new Error("⌛️ Timed out making external call.");
    }),
    Promise.resolve().then(async ()=>{
      const resp = await fetch("https://pokeapi.co/api/v2/pokemon/ditto");
      await resp.json();
      if(!resp.ok){
        console.error("🙅🏻‍♂️ public-api can't make external calls. ");
        throw new Error("🫣 public-api can't make external calls.");
      }
      console.log(
        "🙌 Successful external call to: https://pokeapi.co/api/v2/pokemon/ditto",
      );
    }),
  ]);
}
