import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { RequestHandler } from "express";
import { IPermissionGetterConfig } from "@divinci-ai/server-permissions";
import { getUserId, getUsersActiveGroup } from "@divinci-ai/server-globals";
import { DocPermissionModel } from "@divinci-ai/server-models";
import { DIVINCI_TEST_ENVIRONMENT, DIVINCI_PERMISSION_DRY_HEADER } from "@divinci-ai/server-globals";

// Extend Express Request interface to include permissions property
declare global {
  namespace Express {
    interface Request {
      permissions?: {
        whitelabel?: {
          admin?: boolean;
          [key: string]: any;
        };
        [key: string]: any;
      };
    }
  }
}


export function prepareMiddleware(config: IPermissionGetterConfig){
  return (permissionType: string)=>{
    return middleware(config, permissionType);
  };
}

export function middleware(config: IPermissionGetterConfig, permission: string): RequestHandler{
  if(!config.verbosePermissions.find(({ key })=>(key === permission))){
    throw new Error(`❌ permission ${permission} missing from config ${config.name}`);
  }
  return async (req, res, next)=>{
    try {
      // In local development, bypass permission check for Cloudflare Worker requests
      if((process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local") &&
         (req.headers["cloudflare-worker-x-dev-auth"] || req.headers["x-worker-local-dev"] === "true")) {
        console.log("🔑 Bypassing permission check for Cloudflare Worker request", {
          environment: process.env.ENVIRONMENT || process.env.NODE_ENV,
          hasDevAuth: !!req.headers["cloudflare-worker-x-dev-auth"],
          hasLocalDev: req.headers["x-worker-local-dev"] === "true",
          path: req.path,
          method: req.method
        });

        // Ensure we have the right permissions for the whitelabel
        if (!req.permissions) {
          req.permissions = {
            whitelabel: {
              admin: true
            }
          };
        }

        return next();
      }

      const userId = getUserId(req);
      const activeUserOrg = getUsersActiveGroup(req);
      const doc = await config.getDocument(req.params);
      if(doc === null) throw HTTP_ERRORS.NOT_FOUND;

      const permissionDoc = await DocPermissionModel.findOrCreatePermission(doc, config);
      if(!await permissionDoc.isUserAllowed(
        { allValidPermissions: config.defaultValues.allValidPermissions },
        { userId, activeUserOrg },
        permission
      )){
        throw HTTP_ERRORS.FORBIDDEN;
      }
      if(!DIVINCI_TEST_ENVIRONMENT) return next();

      const overrideHeader = req.headers[DIVINCI_PERMISSION_DRY_HEADER];
      if(overrideHeader === "true"){
        return res.status(200).send({ status: "permission-override", });
      }
      next();
    }catch(e){
      next(e);
    }
  };
}
