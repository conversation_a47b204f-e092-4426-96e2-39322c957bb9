import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { CrudArgs } from "./types";
import { HTTP_ERRORS, getParam } from "@divinci-ai/server-utils";


export const deleteDoc: RequestHandler = async function (this: CrudArgs<any>, req, res, next) {
  try {
    const { model, getTarget, idParam } = this;
    const itemId = getParam(req, idParam);
    const [item, target] = await Promise.all([
      model.findById(itemId),
      getTarget(req),
    ]);
    if(item === null) throw HTTP_ERRORS.NOT_FOUND;
    if(item.target !== target) throw HTTP_ERRORS.NOT_FOUND;

    await item.deleteOne();

    res.statusCode = 200;
    res.json(item); // Note: changed 'prefix' to 'item' to match the util return value
  }catch(e){
    next(e);
  }
};
