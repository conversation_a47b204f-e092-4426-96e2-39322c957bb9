
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";

import { CrudArgs } from "./types";


export const listDocs: RequestHandler = async function(this: CrudArgs<any>, req, res, next){
  try {
    const { model, getTarget } = this;
    const target = await getTarget(req);
    const items = await model.find({ target: target });
    res.statusCode = 200;
    res.json(items); // Note: changed 'prefix' to 'item' to match the util return value
  }catch(e){
    next(e);
  }
};

export const getDoc: RequestHandler = async function(this: CrudArgs<any>, req, res, next){
  try {
    const { model, getTarget, idParam } = this;
    const itemId = getParam(req, idParam);
    const [item, target] = await Promise.all([
      model.findById(itemId),
      getTarget(req),
    ]);
    if(item === null) throw HTTP_ERRORS.NOT_FOUND;
    if(item.target !== target) throw HTTP_ERRORS.NOT_FOUND;

    res.statusCode = 200;
    res.json(item); // Note: changed 'prefix' to 'item' to match the util return value
  }catch(e){
    next(e);
  }
};
