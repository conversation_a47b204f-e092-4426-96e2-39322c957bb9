import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getUserId } from "@divinci-ai/server-globals";

import {
  ChatModel,
  TranscriptModel,
  WhiteLabelReleaseModel,
} from "@divinci-ai/server-models";

import {
  getParam,
  jsonBody,
  HTTP_ERRORS,
  HTTP_ERRORS_WITH_CONTEXT,
} from "@divinci-ai/server-utils";
import { castToObject, castShallowObject } from "@divinci-ai/utils";

export const getOwnChats: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);
    const url = new URL(req.url, "http://localhost");
    const release = url.searchParams.get("release");
    const chats = await ChatModel.find({
      ownerUser: user_id, ...(release ? { releases: release } : {})
    })
      .select([
        "_id", "title", "ownerUser", "releases",
        "createdAt", "updatedAt"
      ])
      .populate({ path: "releases", model: WhiteLabelReleaseModel, select: ["_id", "title"] })
      .exec();

    res.statusCode = 200;
    res.json(chats);
  }catch(e){
    next(e);
  }
};

import { messageToURL } from "./utils";
export const createChatThread: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const bodyUncasted = await jsonBody(req);

    const { title, titleSet, releases } = castShallowObject(
      bodyUncasted,
      { title: "string", titleSet:"boolean?", releases: "string[]" },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM,
    );

    if(title === ""){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Title is required");
    }

    const { chat, transcript } = await Promise.resolve().then(async function(){
      const body = castToObject(bodyUncasted);
      if(typeof body.initialMessage !== "object"){
        return ChatModel.createChat({ userId, title, titleSet, releases });
      }
      const { assistantName, content } = castShallowObject(
        body.initialMessage, { assistantName: "string", content: "string" }
      );
      const { docs } = await ChatModel.createChatWithContent(
        { userId, releases }, { assistantName, content }, messageToURL
      );
      return docs;
    });

    res.statusCode = 200;
    res.json({ chat, transcript });
  }catch(e){
    next(e);
  }


};

export const collectChatMetaData: RequestHandler = async (req, res, next)=>{
  try {
    const chatId = getParam(req, "chatId");

    const chat = await ChatModel.findById(chatId);
    if(chat === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const transcript = await TranscriptModel.findById(chat.transcriptId);
    if(transcript === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const json = castToObject(await jsonBody(req), HTTP_ERRORS.BAD_FORM);
    if(
      !(typeof json.replyTo === "string" || typeof json.replyTo === "undefined")
    ){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "replyTo: " + typeof json.replyTo,
      );
    }
    const replyTo = json.replyTo;
    if(typeof json.content !== "string"){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("bad content");
    }
    const content = json.content;

    console.log("about to collect metadata");

    const metadata = await transcript.collectMetaData(replyTo, content);

    console.log("collected metadata:", metadata);

    res.statusCode = 200;
    res.json(metadata);
  }catch(e){
    next(e);
  }
};

export const getChatThread: RequestHandler = async (req, res, next)=>{
  try {
    const chatId = getParam(req, "chatId");
    const chat = await ChatModel.findById(chatId);
    if(chat === null){
      console.log("no chat");
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const [transcript, releases] = await Promise.all([
      TranscriptModel.findById(chat.transcriptId),
      Promise.resolve().then(async ()=>{
        if(chat.releases.length === 0) return [];
        return await WhiteLabelReleaseModel
        .find({ _id: { $in: chat.releases } })
        .select(["_id", "slug", "version", "title", "description", "category", "status"]);
      })
    ]);
    if(transcript === null){
      console.log("no transcript:", chat.transcriptId);
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json({ chat, transcript, releases });
  }catch(e){
    next(e);
  }
};

export const forkChatThread: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);
    const chatId = getParam(req, "chatId");

    const json = castToObject(await jsonBody(req), HTTP_ERRORS.BAD_FORM);
    if(typeof json.title !== "string") throw HTTP_ERRORS.BAD_FORM;
    const title = json.title;

    const oldChat = await ChatModel.findById(chatId).exec();
    if(!oldChat) throw HTTP_ERRORS.NOT_FOUND;

    const { chat, transcript } = await oldChat.forkChat(user_id, title);

    res.statusCode = 200;
    res.json({ chat, transcript });
  }catch(e){
    next(e);
  }
};


export const renameChatThread: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);

    const json = castToObject(await jsonBody(req), HTTP_ERRORS.BAD_FORM);
    if(typeof json.title !== "string") throw HTTP_ERRORS.BAD_FORM;
    const title = json.title;

    const existing = await ChatModel.findOne({
      ownerUser: user_id,
      title: title,
    });
    if(existing){
      throw HTTP_ERRORS.ALREADY_EXISTS;
    }

    const chatId = getParam(req, "chatId");
    const updated = await ChatModel.findOneAndUpdate(
      { _id: chatId },
      {
        title: title,
        titleSet: true,
      },
      { returnDocument: "after" },
    );
    if(updated === null){
      console.error("no update");
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(updated);
  }catch(e){
    next(e);
  }
};

export const deleteChatThread: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);
    const chatId = getParam(req, "chatId");

    // Find and delete the chat
    const chat = await ChatModel.findOne({
      _id: chatId,
      ownerUser: user_id,
    });
    if(chat === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    await chat.deleteOne();

    // Check if there are any remaining chats for the user
    res.status(200).json({ message: "Chat deleted 🚮." });
  }catch(e){
    next(e);
  }
};
