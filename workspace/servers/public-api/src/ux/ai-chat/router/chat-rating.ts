import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { Types } from "mongoose";
import {
  ChatModel,
  getRatingOfTarget,
  setRating,
  getTrending,
  getContreversial,
  TranscriptModel,
} from "@divinci-ai/server-models";

import {
  RatedSubTarget,
  unravelTarget,
  AI_CHAT_LOCATION,
} from "@divinci-ai/models";

import { getUserId, getUserIdOptional } from "@divinci-ai/server-globals";
import {
  HTTP_ERRORS,
  HTTP_ERRORS_WITH_CONTEXT,
  getParam,
  jsonBody,
} from "@divinci-ai/server-utils";

import { castToObject } from "@divinci-ai/utils";

export const getChatRating: RequestHandler = async function(req, res, next){
  try {
    console.log("⌻ getChatRating()");
    const userId = getUserIdOptional(req);
    const chatId = getParam(req, "chatId");
    const exists = await ChatModel.exists({ _id: chatId });
    if(!exists){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("🔍 Chat doesn't exist.");
    }

    const rating = await getRatingOfTarget(userId, {
      ...AI_CHAT_LOCATION,
      id: chatId,
    });

    res.statusCode = 200;
    res.json(rating);
  }catch(e){
    next(e);
  }
};

export const setChatRating: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const chatId = getParam(req, "chatId");
    const messageId = getParam(req, "messageId");
    const [body, chat] = await Promise.all([
      jsonBody(req),
      ChatModel.findById(chatId),
    ]);
    if(chat === null){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("chat doesn't exist");
    }

    const transcript = await TranscriptModel.findById(chat.transcriptId);
    if(transcript === null){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("transcript doesn't exist");
    }
    if(
      typeof transcript.messages.find(
        (message)=>(message._id === messageId),
      ) === "undefined"
    ){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("message doesn't exist");
    }
    const json = castToObject(body, HTTP_ERRORS.BAD_FORM);

    if(typeof json.rating !== "number") throw HTTP_ERRORS.BAD_FORM;
    const ratingValue = json.rating;
    if([-1, 0, 1].indexOf(ratingValue) === -1)
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("rating must be -1, 0 or 1");

    await setRating(
      userId,
      { ...AI_CHAT_LOCATION, id: chatId, propId: messageId },
      ratingValue,
    );

    const rating = await getRatingOfTarget(userId, {
      ...AI_CHAT_LOCATION,
      id: chatId,
    });

    res.statusCode = 200;
    res.json(rating);
  }catch(e){
    next(e);
  }
};

const DAY_IN_MILLI = 24 * 60 * 60 * 1000;

export const getTrendingChats: RequestHandler = async function(req, res, next){
  try {
    if(req.auth){
      console.log("🙋🏻‍♂️ getTrendingChats(req) req.auth: ", req.auth);
    } else {
      console.log("🙋🏻‍♂️ getTrendingChats(req) req: ", req.res?.statusMessage);
    }
    const userId = getUserIdOptional(req);

    const targetsAny = await getTrending(DAY_IN_MILLI);
    const chatRatings = await trendingTargetsToChatRatings(userId, targetsAny);

    res.statusCode = 200;
    res.json(chatRatings);
  }catch(e){
    next(e);
  }
};

export const getContreversialChats: RequestHandler = async function(req, res, next){
  try {
    console.log("⌻ getContreversialChats()");
    const userId = getUserIdOptional(req);

    const targetsAny = await getContreversial(DAY_IN_MILLI);
    const chatRatings = await trendingTargetsToChatRatings(userId, targetsAny);

    res.statusCode = 200;
    res.json(chatRatings);
  }catch(e){
    next(e);
  }
};

async function trendingTargetsToChatRatings(
  userId: null | string,
  targetsAny: Array<RatedSubTarget>,
){
  const chatIds: Array<Types.ObjectId> = [];
  const values: { [id: string]: { up: number, down: number } } = {};

  targetsAny.forEach((doc)=>{
    const target = unravelTarget(doc.parent);
    if(target.database !== AI_CHAT_LOCATION.database) return;
    if(target.model !== AI_CHAT_LOCATION.model) return;
    const id = target.id;
    const idString = id.toString();
    chatIds.push(new Types.ObjectId(id));
    values[idString] = { up: doc.up, down: doc.down };
  });
  const [chats] = await Promise.all([
    ChatModel.find({ _id: { $in: chatIds } }).select([
      "_id",
      "title",
      "ownerUser",
    ]),
  ]);

  return chats.map((chat)=>({
    chat,
    rating: { ...values[chat._id.toString()] },
  }));
}
