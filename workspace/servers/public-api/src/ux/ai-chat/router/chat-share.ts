import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { Types, ObjectId, Schema } from "mongoose";
import { getUserId, getUserById } from "@divinci-ai/server-globals";
import {
  SavedChats,
  SavedChatsModel,
  ChatModel,
  getMaxRatingOfEachTarget,
  DocPermissionModel,
  emailToKey,
} from "@divinci-ai/server-models";

import {
  CondensedTarget,
  unravelTarget,
  condenseTarget,
  AI_CHAT_LOCATION,
} from "@divinci-ai/models";

import { jsonBody, HTTP_ERRORS } from "@divinci-ai/server-utils";

import { castToObject, escapeRegExp } from "@divinci-ai/utils";

export const getSharedChats: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);
    const user = await getUserById(user_id);

    const emailKey = emailToKey(user.email);

    const [invited, permitted] = await Promise.all([
      Promise.resolve().then(async ()=>{
        const permissions = await DocPermissionModel.find({
          target: {
            $regex: new RegExp(
              "^" +
                escapeRegExp(condenseTarget({ ...AI_CHAT_LOCATION, id: "" })),
            ),
          },
          ["userInvitations." + emailKey]: {
            $exists: true,
          },
        })
          .hint({ target: 1 })
          .exec();

        return getChatInfos(permissions);
      }),
      Promise.resolve().then(async ()=>{
        const permissions = await DocPermissionModel.find({
          target: {
            $regex: new RegExp(
              "^" +
                escapeRegExp(condenseTarget({ ...AI_CHAT_LOCATION, id: "" })),
            ),
          },
          ["userPermissions." + user_id]: {
            $exists: true,
          },
        })
          .hint({ target: 1 })
          .exec();

        return getChatInfos(permissions);
      }),
    ]);
    res.statusCode = 200;
    res.json(permitted);
  }catch(e) {
    next(e);
  }

  async function getChatInfos(permissions: Array<{ target: string }>){
    const targets: Array<CondensedTarget> = [];
    const ids = permissions.map((permission)=>{
      const target = unravelTarget(permission.target);
      targets.push(permission.target);
      return new Types.ObjectId(target.id);
    });
    const [chats, ratings] = await Promise.all([
      ChatModel.find({
        _id: { $in: ids },
      })
        .select(["_id", "title", "ownerUser"])
        .exec(),
      getMaxRatingOfEachTarget(targets),
    ]);
    return chats.map((chat, i)=>({ chat, rating: ratings[i] }));
  }
};

export const getSavedChats: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);

    const savedChats = await SavedChatsModel.findOne({
      ownerUser: user_id,
    });

    if(savedChats === null) {
      res.statusCode = 200;
      res.json([]);
      return;
    }

    const list = await resolveSavedChats(user_id, savedChats);

    res.statusCode = 200;
    res.json(list);
  }catch(e) {
    next(e);
  }
};

export const setSavedChats: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);

    const [savedChats, body] = await Promise.all([
      SavedChatsModel.findOneAndUpdate(
        { ownerUser: user_id },
        {
          $setOnInsert: { ownerUser: user_id, chats: [] },
        },
        {
          new: true, // return new doc if one is upserted
          upsert: true, // insert the document if it does not exist
        }
      ),
      jsonBody(req),
    ]);

    if(savedChats === null) {
      throw HTTP_ERRORS.SERVER_ERROR;
    }

    const json = castToObject(body, HTTP_ERRORS.BAD_FORM);
    if(typeof json.chatId !== "string") throw HTTP_ERRORS.BAD_FORM;
    const chatId = json.chatId;
    const chatExists = await ChatModel.exists({ _id: chatId }).exec();
    if(!chatExists) throw HTTP_ERRORS.BAD_FORM;

    if(typeof json.description !== "string") throw HTTP_ERRORS.BAD_FORM;
    const description = json.description;

    const found = savedChats.chats.findIndex(
      (chat)=>chat.id.toString() === chatId,
    );

    const newItem: { id: ObjectId, description: string } = { id: new Schema.Types.ObjectId(chatId), description: description };

    savedChats.chats = (function(){
      switch(found) {
        case -1: {
          return savedChats.chats = savedChats.chats.concat([newItem]);
        }
        case 0: {
          return savedChats.chats = [newItem].concat(savedChats.chats.slice(1));
        }
        case savedChats.chats.length - 1: {
          return savedChats.chats = savedChats.chats.slice(0, -1).concat(newItem);
        }
        default: {
          return savedChats.chats
            .slice(0, found)
            .concat([newItem])
            .concat(savedChats.chats.slice(found + 1));
          break;
        }
      }
    })();

    await savedChats.save();
    const list = await resolveSavedChats(user_id, savedChats);

    res.statusCode = 200;
    res.json(list);
  }catch(e) {
    next(e);
  }
};

export const removeSavedChats: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);

    const [savedChats, body] = await Promise.all([
      await SavedChatsModel.findOne({ ownerUser: user_id }),
      jsonBody(req),
    ]);

    if(savedChats === null) {
      res.statusCode = 200;
      res.json([]);
      return;
    }

    const json = castToObject(body, HTTP_ERRORS.BAD_FORM);
    if(typeof json.chatId !== "string") throw HTTP_ERRORS.BAD_FORM;
    const chatId = json.chatId;

    const found = savedChats.chats.findIndex(
      (chat)=>chat.id.toString() === chatId,
    );

    switch(found) {
      case -1: {
        break;
      }
      case 0: {
        savedChats.chats = savedChats.chats.slice(1);
        break;
      }
      case savedChats.chats.length - 1: {
        savedChats.chats = savedChats.chats.slice(0, -1);
        break;
      }
      default: {
        savedChats.chats = savedChats.chats
          .slice(0, found)
          .concat(savedChats.chats.slice(found + 1));
        break;
      }
    }

    await savedChats.save();
    const list = await resolveSavedChats(user_id, savedChats);

    res.statusCode = 200;
    res.json(list);
  }catch(e) {
    next(e);
  }
};

async function resolveSavedChats(user_id: string, savedChats: SavedChats){
  const targets: Array<CondensedTarget> = [];
  const ids = savedChats.chats.map((savedChat)=>{
    targets.push(
      condenseTarget({ ...AI_CHAT_LOCATION, id: savedChat.id.toString() }),
    );
    return savedChat.id;
  });

  const [chats, ratings] = await Promise.all([
    ChatModel.find({
      _id: { $in: ids },
    })
      .select(["_id", "title", "ownerUser"])
      .exec(),
    getMaxRatingOfEachTarget(targets),
  ]);
  return chats.map((chat, i)=>({ chat, rating: ratings[i] }));
}
