import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { getUserId } from "@divinci-ai/server-globals";
import { jsonBody, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { castToObject } from "@divinci-ai/utils";

import { TranscriptModel } from "@divinci-ai/server-models";

export const categorizeText: RequestHandler = async (req, res, next) => {
  try {
    // Ensure there is a user
    getUserId(req);

    const json = castToObject(await jsonBody(req), HTTP_ERRORS.BAD_FORM);
    if(typeof json.content !== "string") throw HTTP_ERRORS.BAD_FORM;
    const content = json.content;

    const { category, confidence } =
      await TranscriptModel.categorizeText(content);

    res.statusCode = 200;
    res.json({ category, confidence });
  }catch(e){
    console.error("❌ Categorization error: ", e);
    next(new Error("❌ Thrown in categorization. "));
  }
};
