import { Router } from "express";
import {
  ensureAuthMiddlewareHTTP,
  optionalAuthMiddlewareHTTP,
} from "@divinci-ai/server-globals";
import { AICHAT_ENUM } from "@divinci-ai/models";
import { permissionMiddleware } from "../permission";

import {
  getOwnChats,
  createChatThread,
  getChatThread,
  collectChatMetaData,
  renameChatThread,
  deleteChatThread,
  forkChatThread,
} from "./chat";

import {
  getSharedChats,
  getSavedChats,
  setSavedChats,
  removeSavedChats,
} from "./chat-share";

import {
  addChatMessage,
  deleteChatMessage,
  handleEmojiReaction,
} from "./chat-message";

import {
  getChatRating,
  getTrendingChats,
  getContreversialChats,
  setChatRating,
} from "./chat-rating";

import { categorizeText } from "./message-assistants/chat-categorizing";
import { chatMessageToSpeech } from "./chat-text-to-speech";

export const router = Router();
router.get("/", ensureAuthMiddlewareHTTP, getOwnChats);
router.post("/", ensureAuthMiddlewareHTTP, createChatThread);

import { anonymousChat, anonymousToOwned } from "./anonymous-chat";
router.post("/anonymous-chat", anonymousChat);
router.post("/anonymous-to-owned", anonymousToOwned);

router.get("/shared", ensureAuthMiddlewareHTTP, getSharedChats);
// Explicitly handle OPTIONS requests for the trending endpoint
router.options("/trending", (req, res) => {
  console.log("✅ Handling OPTIONS request for /ai-chat/trending");
  res.setHeader("Access-Control-Allow-Origin", req.headers.origin || "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization");
  res.setHeader("Access-Control-Allow-Credentials", "true");
  res.setHeader("Access-Control-Max-Age", "86400"); // 24 hours
  res.status(204).end();
});

router.get("/trending", optionalAuthMiddlewareHTTP, getTrendingChats);
router.get("/contreversial", optionalAuthMiddlewareHTTP, getContreversialChats);

router.get("/saved", ensureAuthMiddlewareHTTP, getSavedChats);
router.post("/saved", ensureAuthMiddlewareHTTP, setSavedChats);
router.delete("/saved", ensureAuthMiddlewareHTTP, removeSavedChats);

router.post("/categorize-text", ensureAuthMiddlewareHTTP, categorizeText);
import { getDefaultMessageAssistants } from "./message-assistants/get-default-messenger-assistants";
router.get("/message-assistants", ensureAuthMiddlewareHTTP, getDefaultMessageAssistants);

router.get(
  "/:chatId",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.viewTranscript),
  getChatThread,
);

router.delete(
  "/:chatId",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.deleteChat),
  deleteChatThread,
);

import { getChatMessageAssistants } from "./message-assistants/get-chat-messenger-assistants";
router.get("/:chatId/message-assistants",
  permissionMiddleware(AICHAT_ENUM.viewTranscript),
  getChatMessageAssistants
);
import { addAssistantToChat } from "./message-assistants/add-assistant";
router.post("/:chatId/message-assistants",
  permissionMiddleware(AICHAT_ENUM.useTranscript),
  addAssistantToChat
);

router.post("/:chatId/rename",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.configChat),
  renameChatThread,
);
router.post(
  "/:chatId/fork",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.viewTranscript),
  forkChatThread,
);

import { chatPermissionRouter } from "../permission";

router.use("/:chatId/permission", chatPermissionRouter);

import { getChatTranscript } from "./chat-message";
router.get("/:chatId/transcript",
  permissionMiddleware(AICHAT_ENUM.viewTranscript),
  getChatTranscript,
);

router.post(
  "/:chatId/message",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.useTranscript),
  addChatMessage,
);

router.post(
  "/:chatId/metadata",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.utilityChat),
  collectChatMetaData,
);

import { generateChatTitle } from "./chat-generate-title";
router.post(
  "/:chatId/generate-chat-title",
  ensureAuthMiddlewareHTTP,
  generateChatTitle,
);

router.post(
  "/:chatId/message/:messageId/emoji-reaction",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.interactTranscript),
  handleEmojiReaction,
);

router.get(
  "/:chatId/message/:messageId/text-to-speech",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.utilityChat),
  chatMessageToSpeech,
);

router.delete(
  "/:chatId/message/:messageId",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.moderateTranscript),
  deleteChatMessage,
);

router.get(
  "/:chatId/rating",
  optionalAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.viewTranscript),
  getChatRating,
);
router.post(
  "/:chatId/message/:messageId/rating",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(AICHAT_ENUM.interactTranscript),
  setChatRating,
);
