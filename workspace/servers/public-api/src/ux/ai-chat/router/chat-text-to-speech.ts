import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";

import { ChatModel, CHAT_CATEGORY_ENUM, TranscriptModel } from "@divinci-ai/server-models";
import { TranscriptMessage, } from "@divinci-ai/models";
import { getTextToSpeech } from "@divinci-ai/server-globals";

const { textToSpeech: googleTextToSpeech } = getTextToSpeech();

export const chatMessageToSpeech: RequestHandler = async (req, res, next)=>{
  try {

    const chatId = getParam(req, "chatId");
    const messageId = getParam(req, "messageId");


    const chat = await ChatModel.findById(chatId);

    if(chat === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const transcript = await TranscriptModel.findById(chat.transcriptId);

    if(transcript === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const message = transcript.messages.find(
      (message: TranscriptMessage)=>(message._id === messageId)
    );

    if(typeof message === "undefined"){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(message.category !== CHAT_CATEGORY_ENUM.TEXT){
      throw HTTP_ERRORS.BAD_FORM;
    }

    try {
      const speech = await googleTextToSpeech(message.content);

      res.statusCode = 200;
      res.set('Content-Type', speech.mimetype);
      res.send(speech.content);
    }catch(e){
      console.error("google text to speech error:", e);
      throw HTTP_ERRORS.SERVER_ERROR;
    }
  }catch(e){
    console.error("text to speech error:", e);
    next(e);
  }
};
