
import { getUserId } from "@divinci-ai/server-globals";
import { NanoUSDWalletMethods } from "@divinci-ai/server-models";
import { RequestHandler } from "express";
import { JSON_EXTRA_stringify } from "@divinci-ai/utils";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

export const getRelatedTransactions: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const start = getAsNumber("start", req.query.start);
    const end = getAsNumber("end", req.query.end);

    if(start > end){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Start must be before end");
    }

    const transactions = await NanoUSDWalletMethods.getTransactionsByDate(
      userId, start, end
    );

    res.statusCode = 200;
    res.type("application/json");
    res.end(JSON_EXTRA_stringify(transactions));
  }catch(e){
    next(e);
  }
};

function getAsNumber(key: string, value: unknown){
  if(typeof value === "undefined"){
    throw new Error(`Missing ${key}`);
  }
  if(typeof value !== "string"){
    throw new Error(`Invalid Input ${key}`);
  }
  const num = Number.parseInt(value);
  if(Number.isNaN(num)){
    throw new Error(`Invalid Number ${key}`);
  }
  return num;
}
