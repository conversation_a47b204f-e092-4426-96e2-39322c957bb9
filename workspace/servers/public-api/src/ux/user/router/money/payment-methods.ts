import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getStripeInstance, WEB_CLIENT_IS_SECURE, WEB_CLIENT_HOST, getUserId } from "@divinci-ai/server-globals";
import { StripeCustomerModel } from "@divinci-ai/server-models";

const WEB_CLIENT_ORIGIN = `http${WEB_CLIENT_IS_SECURE ? "s" : ""}://${WEB_CLIENT_HOST}`;

export const setupPaymentMethod: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const customer = await StripeCustomerModel.findOrCreate(userId);
    const stripe = getStripeInstance();

    const session = await stripe.checkout.sessions.create({
      customer: customer.stripeCustomerId,
      payment_method_types: ["card"],
      mode: "setup",
      success_url: `${WEB_CLIENT_ORIGIN}/user/wallet?status=success&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${WEB_CLIENT_ORIGIN}/user/wallet?status=cancel`,
    });


    res.statusCode = 200;
    res.json({ stripeUrl: session.url });
  }catch(e){
    next(e);
  }
};

export const listPaymentMethods: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const customer = await StripeCustomerModel.findOrCreate(userId);
    const stripe = getStripeInstance();

    const cards = await stripe.paymentMethods.list({
      customer: customer.stripeCustomerId,
      type: "card",
    });

    res.statusCode = 200;
    res.json(cards.data);
  }catch(e){
    next(e);
  }
};

export const managePaymentMethods: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const customer = await StripeCustomerModel.findOrCreate(userId);
    const stripe = getStripeInstance();

    const portalSession = await stripe.billingPortal.sessions.create({
      customer: customer.stripeCustomerId,
      return_url: `${WEB_CLIENT_ORIGIN}/user/wallet`,
    });

    res.statusCode = 200;
    res.json({ stripeUrl: portalSession.url });
  }catch(e){
    next(e);
  }
};
