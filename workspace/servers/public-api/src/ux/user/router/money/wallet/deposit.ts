import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getStripeInstance, getUserId } from "@divinci-ai/server-globals";
import { StripeCustomerModel } from "@divinci-ai/server-models";
import { jsonBody, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { castShallowObject, delay } from "@divinci-ai/utils";

// Helper function to run a deposit transaction
import { balanceUSDToNanoAmount } from "@divinci-ai/tools";
import { runNanoUSDStripeDeposit } from "@divinci-ai/server-models";

// Maximum amount to deposit in cents
// At the moment it's 10,000
const MAX_DEPOSIT_AMOUNT = 10_000_00;

export const createDepositSession: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const body = await jsonBody(req);
    const { amount, paymentMethodId } = castShallowObject(body, {
      amount: "number",
      paymentMethodId: "string"
    });

    // Validate amount
    if(amount <= 0) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "The amount to deposit must be greater than 0"
      );
    }

    if(amount % 1 !== 0) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "The amount to deposit must be a whole number of cents"
      );
    }

    if(amount > MAX_DEPOSIT_AMOUNT) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        `The maximum amount to deposit is ${MAX_DEPOSIT_AMOUNT/100} USD`
      );
    }

    // Validate payment method
    if(!paymentMethodId) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "Payment method is required"
      );
    }

    const customer = await StripeCustomerModel.findOrCreate(userId);
    const stripe = getStripeInstance();

    // Verify the payment method belongs to this customer
    const paymentMethod = await (async function(){
      try {
        const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
        if(paymentMethod.customer !== customer.stripeCustomerId) {
          throw new Error("payment method does not belong to this customer");
        }
        return paymentMethod;
      }catch(e){
        console.error(e);
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("invalid payment method");
      }
    })();

    // Create a payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: "usd",
      customer: customer.stripeCustomerId,
      payment_method: paymentMethod.id,
      off_session: false,
      confirm: true,
      description: "Deposit to Divinci Wallet",
      metadata: {
        userId,
        type: "deposit"
      },
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: "never"
      }
    });

    // Check if payment was successful
    if(paymentIntent.status !== "succeeded" && paymentIntent.status !== "processing") {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        `Payment failed with status: ${paymentIntent.status}`
      );
    }

    if(paymentIntent.status === "processing"){
      const status = await delegatePoller(paymentIntent.id);
      if(status === "processing"){
        await stripe.paymentIntents.cancel(paymentIntent.id);
        throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
          `Payment took to long`
        );
      }
      if(status !== "succeeded"){
        throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
          `Payment failed with status: ${status}`
        );
      }
    }

    await finalizePayment(userId, paymentIntent.id, amount);

    res.statusCode = 200;
    res.json({ status: "success", amount });
  }catch(e) {
    next(e);
  }
};

async function delegatePoller(paymentIntentId: string){
  const stripe = getStripeInstance();
  let paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
  for(let i = 0; i < 20 && paymentIntent.status === "processing"; i++){
    await delay(500);
    paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
  }
  return paymentIntent.status;
}

async function finalizePayment(userId: string, paymentIntentId: string, amount: number){
  const nanoAmount = balanceUSDToNanoAmount(amount/100);
  try {
    // Add the amount to the user's wallet
    await runNanoUSDStripeDeposit(paymentIntentId, userId, nanoAmount);
  }catch(e){
    console.error("Failed Saving Deposit:", e);
    const stripe = getStripeInstance();
    await stripe.refunds.create({
      payment_intent: paymentIntentId,
      metadata: {
        reason: "Failed Saving Deposit"
      },
    });
    throw e;
  }
}
