import { Router } from "express";

export const router = Router();

// Get user's money balance
import { getBalance } from "./wallet/balance";
router.get("/balance", getBalance);

// Process deposit
import { createDepositSession } from "./wallet/deposit";
router.post("/deposit", createDepositSession);

import { getRelatedTransactions } from "./transactions/is-related";
router.get("/transactions", getRelatedTransactions);

import { setupPaymentMethod, listPaymentMethods, managePaymentMethods } from "./payment-methods";
router.get("/payment-method/create", setupPaymentMethod);
router.get("/payment-method/list", listPaymentMethods);
router.get("/payment-method/manage", managePaymentMethods);
