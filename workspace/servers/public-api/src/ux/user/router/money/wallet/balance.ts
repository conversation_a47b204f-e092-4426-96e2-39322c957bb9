import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { getUserId } from "@divinci-ai/server-globals";
import { amountPartsToSingle } from "@divinci-ai/tools";
import { NanoUSDWalletMethods } from "@divinci-ai/server-models";
import { JSON_EXTRA_stringify } from "@divinci-ai/utils";

export const getBalance: RequestHandler = async function(
  req, res, next
){
  try {
    const userId = getUserId(req);

    const balanceParts = await NanoUSDWalletMethods.getWallet(userId);

    const balance = !balanceParts ? 0n : amountPartsToSingle({
      usd: balanceParts.usd_balance,
      nano: balanceParts.nano_balance
    });

    res.statusCode = 200;
    res.type("application/json");
    res.end(JSON_EXTRA_stringify({ balance }));
  }catch(e) {
    next(e);
  }
};
