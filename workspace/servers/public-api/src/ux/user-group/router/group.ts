import { Request<PERSON><PERSON><PERSON> } from "express";

import { UserGroupModel } from "@divinci-ai/server-models";
import { getUserById, getUserId, getUserIdOptional } from "@divinci-ai/server-globals";
import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { Types } from "mongoose";

/*

# How is the group supposed to work?

## Ethereum
- A smart contract can be considered a user
- You may use the smart contract as a "glove"
  - Run a function on a smart contract (GLOVE) that runs a function on another smart contract (TARGET)
  - the TARGET will consider the user thats currently running it's function as GLOVE

## As needed?
- Create a group model
- Make other models work with the Group Model as needed

*/


export const createGroup: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const user = await getUserById(userId);
    const { name, slug } = castShallowObject(
      await jsonBody(req),
      { name: "string", slug: "string" },
      HTTP_ERRORS.BAD_FORM
    );

    const newGroup = new UserGroupModel({
      ownerUser: userId,
      name,
      slug: slug.toLocaleLowerCase(),
      users: [{ userId, email: user.email }]
    });

    await newGroup.save();

    res.statusCode = 200;
    res.json(newGroup);
  }catch(e){
    next(e);
  }
};

export const getOwnGroups: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const groups = await UserGroupModel.find({
      $or: [{ "users.userId": userId }, { ownerUser: userId }],
    }).select({ invitations: 0, users: 0 });

    res.statusCode = 200;
    res.json(groups);
  }catch(e){
    next(e);
  }
};

export const getGroup: RequestHandler = async function(req, res, next){
  try {

    const usergroupId = getParam(req, "usergroupId");
    const group = await UserGroupModel.findById(usergroupId);

    if(group === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(group);

  }catch(e){
    next(e);
  }
};


export const getGroupInfo: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserIdOptional(req);
    const usergroupId = getParam(req, "usergroupId");
    const [user, group] = await Promise.all([
      !userId ? void 0 : getUserById(userId),
      UserGroupModel.findOne({
        _id: new Types.ObjectId(usergroupId)
      })
    ]);
    if(!group) throw HTTP_ERRORS.NOT_FOUND;

    res.statusCode = 200;
    res.json({
      _id: group._id, name: group.name, slug: group.slug,
      inGroup: group.users.some(({ userId: itemId })=>(itemId === userId)),
      isInvited: !user ? false : group.invitations.includes(user.email)
    });
  }catch(e){
    next(e);
  }
};

export const updateGroup: RequestHandler = async function(req, res, next){
  try {
    const usergroupId = getParam(req, "usergroupId");
    const { name } = castShallowObject(
      await jsonBody(req),
      { name: "string" },
      HTTP_ERRORS.BAD_FORM
    );


    const group = await UserGroupModel.findByIdAndUpdate(usergroupId, { name });

    if(group === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(group);
  }catch(e){
    next(e);
  }
};

export const deleteGroup: RequestHandler = async function(req, res, next){
  try {
    const usergroupId = getParam(req, "usergroupId");

    const group = await UserGroupModel.findByIdAndDelete(usergroupId);

    if(group === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(group);
  }catch(e){
    next(e);
  }
};
