
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  getRatingOfTarget, setRating,
  WhiteLabelReleaseModel
} from "@divinci-ai/server-models";

import { WHITE_LABEL_RELEASE_LOCATION } from "@divinci-ai/models";

import { getUserId } from "@divinci-ai/server-globals";
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, getParam, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

export const setReleaseRating: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const releaseId = getParam(req, "releaseId");
    const [body, release] = await Promise.all([jsonBody(req), WhiteLabelReleaseModel.findById(releaseId)]);
    if(release === null){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("release doesn't exist");
    }

    const { rating: ratingValue } = castShallowObject(body, { rating: "number?" }, HTTP_ERRORS.BAD_FORM) as { rating: number };

    if([-1, 0, 1].indexOf(ratingValue) === -1) throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("rating must be -1, 0 or 1");

    await setRating(
      userId, { ...WHITE_LABEL_RELEASE_LOCATION, id: releaseId, propId: "" }, ratingValue
    );

    const rating = await getRatingOfTarget(
      userId, { ...WHITE_LABEL_RELEASE_LOCATION, id: releaseId }
    );

    res.statusCode = 200;
    res.json(rating);
  }catch(e){
    next(e);
  }
};
