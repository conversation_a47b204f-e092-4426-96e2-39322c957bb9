
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  getRatingOfTarget,
  WhiteLabelReleaseModel
} from "@divinci-ai/server-models";

import { WHITE_LABEL_RELEASE_LOCATION, } from "@divinci-ai/models";

import { getUserIdOptional } from "@divinci-ai/server-globals";
import { HTTP_ERRORS_WITH_CONTEXT, getParam } from "@divinci-ai/server-utils";

export const getChatRating: RequestHandler = async function(req, res, next){
  try {
    console.log("⌻ getChatRating()");
    const userId = getUserIdOptional(req);
    const releaseId = getParam(req, "releaseId");
    const exists = await WhiteLabelReleaseModel.exists({ _id: releaseId });
    if(!exists){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("🔍 Chat doesn't exist.");
    }
  
    const rating = await getRatingOfTarget(userId, { ...WHITE_LABEL_RELEASE_LOCATION, id: releaseId });
  
    res.statusCode = 200;
    res.json(rating);
  }catch(e){
    next(e);
  }
};

