import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { ThreadPrefixModel } from "@divinci-ai/server-models";
import {
  getItemFromIdRequest, getFirstItemFromRequest, getItems,
} from "../util";

export const getPrefixId: RequestHandler = async function(req, res, next){
  try {
    // Pass "prefixId" as the param string and MessagePrefixModel as the model
    const { item } = await getItemFromIdRequest(req, "prefixId", ThreadPrefixModel);

    res.statusCode = 200;
    res.json(item); // Note: changed 'prefix' to 'item' to match the util return value
  }catch(e){
    next(e);
  }
};

export const getFirstPrefix: RequestHandler = async function(req, res, next){
  try {
    const { item } = await getFirstItemFromRequest(req, ThreadPrefixModel);

    res.statusCode = 200;
    res.json(item); // Note: changed 'prefix' to 'item' to match the util return value
  }catch(e){
    next(e);
  }
};

export const getPrefixes: RequestHandler = async function(req, res, next){
  try {
    const { items } = await getItems(req, ThreadPrefixModel);

    res.statusCode = 200;
    res.json(items); // Note: changed 'prefix' to 'item' to match the util return value
  }catch(e){
    next(e);
  }
};
