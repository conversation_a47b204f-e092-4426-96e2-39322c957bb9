import { Request<PERSON><PERSON><PERSON> } from "express";
import { ThreadPrefixModel } from "@divinci-ai/server-models";
import {
  getItemFromIdRequest,
} from "../util";

export const deletePrefix: RequestHandler = async function(req, res, next){
  try {
    // Pass "prefixId" as the param string and MessagePrefixModel as the model
    const { item } = await getItemFromIdRequest(req, "prefixId", ThreadPrefixModel);

    await item.deleteOne();

    res.statusCode = 200;
    res.json(item); // Note: changed 'prefix' to 'item' to match the util return value
  }catch(e){
    next(e);
  }
};

