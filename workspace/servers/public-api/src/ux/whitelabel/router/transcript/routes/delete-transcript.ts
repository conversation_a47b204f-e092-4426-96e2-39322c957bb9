import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { WhiteLabelModel, TranscriptModel, } from "@divinci-ai/server-models";

import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, } from "@divinci-ai/server-utils";



export const deleteWhiteLabelTranscript: RequestHandler = async (req, res, next)=>{
  try {
    const transcriptId = getParam(req, "transcriptId");
    const whitelabelId = getParam(req, "whitelabelId");

    const whitelabel = await WhiteLabelModel.findById(whitelabelId);

    if(!whitelabel){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("No whitelabel found");
    }

    if(!whitelabel.transcriptIds.includes(transcriptId)){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("No transcript belonging to whitelabel found");
    }

    const transcript = await TranscriptModel.findById(transcriptId);

    // Find and delete the transcript
    if(!transcript){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    await TranscriptModel.findByIdAndDelete(transcriptId);

    // Remove the deleted transcript ID from the whitelabel's transcript IDs
    whitelabel.transcriptIds = whitelabel.transcriptIds.filter(
      (id)=>(id !== transcriptId),
    );

    // Check if there are no more transcripts for this whitelabel
    if(whitelabel.transcriptIds.length === 0){
      // Create a new transcript
      const newTranscript: any = await TranscriptModel.create({
        awaitingResponse: false,
        messages: [],
      });

      // Add the new transcript ID to the whitelabel
      whitelabel.transcriptIds.push(newTranscript._id);
      await whitelabel.save();

      // Redirect to the new transcript
      res.status(200).json({
        message: "Transcript deleted and new transcript created",
        newTranscript,
      });
    } else {
      await whitelabel.save();
      res.status(200).json({ message: "Transcript deleted" });
    }
  }catch(e){
    next(e);
  }
};
