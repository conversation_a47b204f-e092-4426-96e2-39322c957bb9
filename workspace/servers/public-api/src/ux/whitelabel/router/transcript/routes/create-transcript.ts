import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  WhiteLabelModel,
  TranscriptModel,
} from "@divinci-ai/server-models";

import {
  jsonBody,
  HTTP_ERRORS,
  HTTP_ERRORS_WITH_CONTEXT,
  getParam,
} from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";


export const createWhitelabelTranscript: RequestHandler = async (req, res, next,)=>{
  try {

    const whitelabelId = getParam(req, "whitelabelId");

    const [whitelabelBefore, body] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      jsonBody(req)
    ]);

    const { title } = castShallowObject(
      body,
      { title: "string" },
      HTTP_ERRORS.BAD_FORM,
    );

    if(!whitelabelBefore){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Whitelabel doesn't exist");
    }

    const transcript = await TranscriptModel.create({
      awaitingResponse: false,
      messages: [],
    });

    const whitelabel = await WhiteLabelModel.findByIdAndUpdate(
      whitelabelId,
      { $push: { transcriptIds: transcript._id } },
      { new: true, useFindAndModify: false },
    );

    if(!whitelabel){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("whitelabel not found");
    }

    const newChat = {
      _id: transcript._id,
      title,
      whitelabelId,
      createdAt: new Date(),
    };

    return res.status(201).json(newChat);
  }catch(error){
    console.error("❌ Internal Error: ", error);
    next(error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};
