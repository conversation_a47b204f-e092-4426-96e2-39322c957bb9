import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import {
  getUserId,
} from "@divinci-ai/server-globals";
import {
  TranscriptModel,
  WhiteLabelModel,
} from "@divinci-ai/server-models";
import {
  getParam,
  jsonBody,
  HTTP_ERRORS_WITH_CONTEXT,
} from "@divinci-ai/server-utils";

import { addEmojiToMessage } from "../../../../../emoji";


export const addEmojiReactionToMessage: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const messageId = getParam(req, "messageId");
    const whitelabelId = getParam(req, "whitelabelId");
    const transcriptId = getParam(req, "transcriptId");
    const [transcript, body] = await Promise.all([
      Promise.resolve().then(async ()=>{
        const whitelabel = await WhiteLabelModel.findById(whitelabelId);
        if(whitelabel === null){
          throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("🔍👀 Whitelabel not found");
        }
        if(!whitelabel.transcriptIds.includes(transcriptId)){
          throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("🔍👀 Transcript not found");
        }
        const transcript = await TranscriptModel.findById(transcriptId);
        if(transcript === null){
          throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("🔍👀 Transcript not found");
        }
        return transcript;
      }),
      jsonBody(req),
    ]);

    await addEmojiToMessage(userId, messageId, body, transcript);

    res.status(200).send({ status: "ok" });
  }catch(e){
    console.error("❌ Error in addEmojiToMessage:", e);
    next(e);
  }
};

