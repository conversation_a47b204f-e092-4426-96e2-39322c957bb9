import { Model } from "mongoose";
import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  VerifiedEmail,
  IVerifiedEmailDoc,
  NotificationSettingsDefaultModel,
} from "@divinci-ai/server-models";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

const VerifiedEmailModel: Model<IVerifiedEmailDoc> = VerifiedEmail as any;

export const createVerifiedEmail: RequestHandler = async (req, res) => {
  try {
    const {
      email,
      // whitelabelId,
    } = req.params; // Assuming email and whitelabelId are passed in the request body

    // Check if the email already exists for the given whitelabelId
    const existingEmail = await VerifiedEmail.findOne({
      email,
      // whitelabelId,
    });
    console.log("📧 createVerifiedEmai::existingEmail: ", existingEmail);
    if (existingEmail) {
      // return res.status(409).json(
      //   {
      //     message: '🟢 Email already exists.',
      //     data: existingEmail,
      //   }
      // );
      res.status(201).json({
        success: true,
        _id: existingEmail._id,
      });
    }

    // Create a new VerifiedEmail document
    const newVerifiedEmail = await VerifiedEmail.create({
      email,
      verified: false, // Assuming a new email is not verified by default
      // whitelabelId,
    });

    // Successfully created the document, return the _id
    res.status(201).json({
      success: true,
      _id: newVerifiedEmail._id,
    });
  } catch (error) {
    console.error("❌ Failed to create verified email: ", error);
    throw HTTP_ERRORS.BAD_FORM;
  }
};

export const updateVerifiedEmail: RequestHandler = async (req, res) => {
  try {
    const { email, whitelabelIds } = req.body;
    const updatedEmail = await VerifiedEmail.findByIdAndUpdate(
      req.params.id,
      {
        email,
        whitelabelIds,
      },
      { new: true },
    );

    if (!updatedEmail) {
      return res.status(404).json({
        message: "❌ Email not found or already verified.",
      });
    }

    res.json({
      success: true,
      message: "🟢 Email updated successfully.",
      data: updatedEmail,
    });
  } catch (error) {
    console.error("❌ An error occurred while updating the VerifiedEmail.");
    throw HTTP_ERRORS.BAD_FORM;
  }
};

export const getWhitelabelEmails: RequestHandler = async (req, res) => {
  try {
    const { whitelabelId } = req.params;
    const emails = await VerifiedEmailModel.find({
      whitelabelIds: whitelabelId,
    });

    if (!emails) {
      console.error("❌ No Emails found.");
      return res.status(404).json({
        message: "❌ No Emails found.",
      });
    }

    res.json({
      success: true,
      message: "🟢 Emails retrieved successfully.",
      data: emails,
    });
  } catch (error) {
    console.error(
      "❌ An error occurred while retrieving VerifiedEmails for this Whitelabel.",
    );
    throw HTTP_ERRORS.BAD_FORM;
  }
};

export const getVerifiedEmail: RequestHandler = async (req, res) => {
  try {
    const { id } = req.params;
    const retrievedEmail = await VerifiedEmailModel.findOne(
      { _id: id },
      { verified: true },
      { new: true },
    );

    if (!retrievedEmail) {
      console.error(
        "❌ An error occurred while retrieving this VerifiedEmail.",
      );
      return res.status(404).json({
        message: "❌ Email not found.",
      });
    }

    res.status(200).json({
      success: true,
      message: "🟢 Email retrieved successfully.",
      data: retrievedEmail,
    });
  } catch (error) {
    console.error("❌ An error occurred while retrieving this VerifiedEmail.");
    throw HTTP_ERRORS.BAD_FORM;
  }
};

// Deletes a VerifiedEmail document from the database
async function deleteVerifiedEmailById(emailId: string): Promise<void> {
  const emailToDelete = await VerifiedEmail.findById(emailId);
  if (!emailToDelete) {
    throw new Error("🔎 Email not found.");
  }
  await VerifiedEmail.findByIdAndDelete(emailId);
}

// Updates NotificationDefaultSetting entries to remove the deleted email
async function updateNotificationSettingsAfterEmailDeletion(
  email: string,
): Promise<void> {
  await NotificationSettingsDefaultModel.updateMany(
    { "emails.email": email }, // Match the email field within the emails array
    { $pull: { emails: { email } } }, // Remove the email object from the emails array
  );
}

export const deleteDefaultVerifiedEmail: RequestHandler = async (
  req,
  res,
  next,
) => {
  try {
    const { id } = req.params; // The _id of the VerifiedEmail to delete

    // Retrieve the email to delete for its details
    const emailToDelete = await VerifiedEmail.findById(id);
    if (!emailToDelete) {
      return res.status(404).json({ message: "🔎 Email not found." });
    }

    // Delete the VerifiedEmail document
    await deleteVerifiedEmailById(id);

    // Update NotificationDefaultSetting entries
    await updateNotificationSettingsAfterEmailDeletion(emailToDelete.email);

    res.status(200).json({
      success: true,
      message: "🟢 Email successfully deleted and settings updated.",
    });
  } catch (error) {
    console.error("❌ Error during email deletion and settings update:", error);
    next(error);
  }
};

/**
 * Adds a whitelabelId to the whitelabelIds array of a VerifiedEmail document.
 */
export const createOrAddWhitelabelIdToEmail: RequestHandler = async (
  req,
  res,
) => {
  const { email, whitelabelId } = req.body;

  try {
    // Check if the email already exists
    let verifiedEmail = await VerifiedEmail.findOne({ email });

    if (verifiedEmail) {
      // If it exists, push the new whitelabelId
      verifiedEmail = await VerifiedEmail.findByIdAndUpdate(
        verifiedEmail._id,
        {
          $addToSet: { whitelabelIds: whitelabelId }, // Use $addToSet to avoid duplicates
        },
        { new: true },
      );
    } else {
      // If it doesn't exist, create a new document
      verifiedEmail = await VerifiedEmail.create({
        email,
        whitelabelIds: [whitelabelId],
        verified: false, // Assuming a new email is not verified by default
      });
    }

    res.status(200).json({
      success: true,
      message: "🟢 Email processed successfully.",
      data: verifiedEmail,
    });
  } catch (error: any) {
    console.error("❌ Failed to process verified email: ", error);
    res.status(500).json({
      success: false,
      message: "❌ An error occurred while processing the VerifiedEmail.",
      error: error.message,
    });
  }
};
