import { Router } from "express";

export const router = Router({ mergeParams: true });

import { createPrefix } from "./create-prefix";
router.post("/", createPrefix);

import { getPrefixes } from "./get-prefix";
router.get("/", getPrefixes);

import { getFirstPrefix } from "./get-prefix";
router.get("/get-first", getFirstPrefix);

import { getPrefixId } from "./get-prefix";
router.get("/:prefixId", getPrefixId);

import { updatePrefix } from "./update-prefix";
router.patch('/:prefixId', updatePrefix);

// import { listPrefix } from "./list-prefixs";
// router.get("/", listPrefix);

import { deletePrefix } from "./delete-prefix";
router.delete("/:prefixId", deletePrefix);
