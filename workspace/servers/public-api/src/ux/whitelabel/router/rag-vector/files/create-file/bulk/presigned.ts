import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";

import {
  WhiteLabelModel,
} from "@divinci-ai/server-models";


import {
  createPresignedURL, castPresignedFileInfo, castPresignedConfig, handlePresignedURL
} from "../../../../../../../util/presigned-r2";

import { castShallowObject } from "@divinci-ai/utils";
import { DIVINCI_TEST_PROCESS_CONFIG, getUserId } from "@divinci-ai/server-globals";

import { R2_INSTANCE } from "../../file-handler";
const PRESIGNED_PURPOSE = "RagVector File Create";
export const presignedPrepare: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const whitelabelId = getParam(req, "whitelabelId");
    const [unCastedBody, whitelabel] = await Promise.all([
      jsonBody(req),
      WhiteLabelModel.findById(whitelabelId),
    ]);
    if(whitelabel === null){
      console.error("❌ Whitelabel not found");
      throw HTTP_ERRORS.NOT_FOUND;
    }
    if(!Array.isArray(unCastedBody)){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Expecting an array of files");
    }
    if(unCastedBody.length === 0){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Expecting at least one file");
    }
    const files = unCastedBody.map(castPresignedFileInfo);

    const presigned = await Promise.all(files.map((file)=>(
      createPresignedURL(
        R2_INSTANCE,
        { userId, purpose: PRESIGNED_PURPOSE },
        file,
      )
    )));
    res.statusCode = 200;
    res.json(presigned);
  }catch(e){
    next(e);
  }
};

import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { createFiles } from "./createFiles";
export const presignedFinalized: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const whitelabelId = getParam(req, "whitelabelId");

    // Consume the request body using busboy and log the results for debugging
    const [whitelabel, unCastedBody] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      jsonBody(req),
    ]);

    // Check if the WhiteLabel object exists
    if(whitelabel === null){
      console.error("❌ Whitelabel not found");
      throw HTTP_ERRORS.NOT_FOUND;
    }

    // Log the target condense location
    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    const {
      title,
      description,
      chunkingTool,
      chunkingToolConfig: chunkingToolConfigUncasted,
      file: fileUncasted,
    } = castShallowObject(unCastedBody, {
      title: "string",
      description: "string",
      chunkingTool: "string",
      chunkingToolConfig: "string?",
      file: "string[]",
    });

    const chunkingToolConfig = (function(){
      if(!chunkingToolConfigUncasted) return void 0;
      try {
        return JSON.parse(chunkingToolConfigUncasted);
      }catch(e){
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid chunkingTool Config");
      }
    })();

    const files = await Promise.all(fileUncasted.map(async (fileStr)=>{
      const file = await handlePresignedURL(
        R2_INSTANCE,
        { userId, purpose: PRESIGNED_PURPOSE },
        castPresignedConfig(fileStr),
      );
      return {
        bucket: file.Bucket, objectKey: file.Key, filename: file.filename
      };
    }));

    const savedFiles = await createFiles(
      target, userId,
      { id: chunkingTool, config: chunkingToolConfig },
      { title: title, description },
      files,
      DIVINCI_TEST_PROCESS_CONFIG.getHeader(req.headers)
    );

    // Send the response with the array of saved files
    res.status(202).json(savedFiles);

  }catch(e){
    next(e);
  }
};
