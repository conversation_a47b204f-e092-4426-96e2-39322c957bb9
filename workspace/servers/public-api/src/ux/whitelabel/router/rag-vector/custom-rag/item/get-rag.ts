import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { RagVectorModel } from "@divinci-ai/server-models"; // Import the appropriate model
import { getWhiteLabelContext } from "../../../util";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { Types } from "mongoose";
import { populateReleases } from "../../../release/populate";

export const getCustomRag: RequestHandler = async function(req, res, next) {
  try {
    const { target } = await getWhiteLabelContext(req);
    // Pass "ragId" (or whatever the parameter name is) and CustomRagModel
    const docId = getParam(req, "ragId");

    const docs = await RagVectorModel.aggregate([
      {
        // 1. Filter HTMLPages by host
        $match: { target, _id: new Types.ObjectId(docId) }
      },
      populateReleases({
        target,
        $expr: { $eq: ["$ragIndex.id" , "$$docId"] }
      }),
    ]);
    if(docs.length === 0) throw HTTP_ERRORS.NOT_FOUND;
    const doc = docs[0];

    res.statusCode = 200;
    res.json(doc); // Changed from 'rag' to 'item' to match util return value
  }catch(e){
    next(e);
  }
};
