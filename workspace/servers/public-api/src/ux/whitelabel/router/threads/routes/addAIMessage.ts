import { ChatModel, InputMessageContext, WhiteLabelModel, WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { RequestHandler } from "express";

import { DIVINCI_TEST_PROCESS_CONFIG, getUserId } from "@divinci-ai/server-globals";
import {
  HTTP_ERRORS,
  HTTP_ERRORS_WITH_CONTEXT,
  getParam,
  jsonBody,
} from "@divinci-ai/server-utils";

import { castShallowObject } from "@divinci-ai/utils";

import { messageToURL } from "../../../../ai-chat/router/utils";

export const addWhitelabelAIMessage: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const whitelabelId = getParam(req, "whitelabelId");
    const chatId = getParam(req, "chatId");

    const [body, whitelabel, chat] = await Promise.all([
      jsonBody(req),
      WhiteLabelModel.findById(whitelabelId),
      ChatModel.findById(chatId),
    ]);

    if(whitelabel === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("White Label Doesn't Exist");
    }

    if(chat === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Chat Thread Doesn't Exist");
    }

    // Verify the chat belongs to this whitelabel by checking if any of the chat's releases belong to this whitelabel

    const { content, release, replyTo } = castShallowObject(
      body,
      {
        content: "string",
        release: "string",
        replyTo: "string?",
      },
      HTTP_ERRORS.BAD_FORM,
    );

    const msgContext: InputMessageContext = (function(){
      if(typeof replyTo !== "undefined") {
        return { type: "replyTo", value: replyTo };
      }
      return { type: "assistant", value: release };
    })();

    if(!chat.releases.includes(release)){
      // Chat doesn't include the release
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Chat Thread Doesn't Exist");
    }

    const releaseDoc = await WhiteLabelReleaseModel.findOne({
      _id: release, whitelabel: whitelabelId
    });

    if(releaseDoc === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Release Doesn't Exist");
    }

    const messageIds = await chat.addPrivledgedAIMessage(
      msgContext,
      { userId, content, release },
      (chat, message)=>(messageToURL(chat, message)),
      void 0,
      DIVINCI_TEST_PROCESS_CONFIG.getHeader(req.headers),
    );

    res.statusCode = 200;
    res.send(messageIds);

  }catch(e){
    console.error("❌ Error in addWhitelabelUserMessage: ", e);
    next(e);
  }
};
