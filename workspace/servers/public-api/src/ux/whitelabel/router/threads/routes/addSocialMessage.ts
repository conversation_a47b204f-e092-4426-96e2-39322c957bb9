import { ChatModel, TranscriptModel, WhiteLabelModel, WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { RequestHandler } from "express";

import { getUserId } from "@divinci-ai/server-globals";
import {
  HTTP_ERRORS_WITH_CONTEXT,
  getParam,
  jsonBody,
} from "@divinci-ai/server-utils";

import { castShallowObject } from "@divinci-ai/utils";

import { TRANSCRIPT_USER_COMPANY } from "@divinci-ai/models";

import { messageToURL } from "../../../../ai-chat/router/utils";

export const addWhitelabelSocialMessage: RequestHandler = async (req, res, next)=>{
  try {
    getUserId(req);
    const whitelabelId = getParam(req, "whitelabelId");
    const chatId = getParam(req, "chatId");

    const [body, whitelabel, chat] = await Promise.all([
      jsonBody(req),
      WhiteLabelModel.findById(whitelabelId),
      ChatModel.findById(chatId),
    ]);

    if(whitelabel === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("White Label Doesn't Exist");
    }

    if(chat === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Chat Thread Doesn't Exist");
    }

    // Verify the chat belongs to this whitelabel by checking if any of the chat's releases belong to this whitelabel

    const { content, release } = castShallowObject(
      body,
      {
        content: "string",
        release: "string"
      },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM,
    );

    if(!chat.releases.includes(release)){
      // Chat doesn't include the release
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Chat Thread Doesn't Exist");
    }

    const releaseDoc = await WhiteLabelReleaseModel.findOne({
      _id: release, whitelabel: whitelabelId
    });

    if(releaseDoc === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Release Doesn't Exist");
    }

    await chat.addSocialMessage(
      { content, release },
      { userId: TRANSCRIPT_USER_COMPANY },
      (chat, message)=>(messageToURL(chat, message))
    );

    res.statusCode = 200;
    res.send({ status: "ok" });

  }catch(e){
    console.error("❌ Error in addWhitelabelUserMessage: ", e);
    next(e);
  }
};
