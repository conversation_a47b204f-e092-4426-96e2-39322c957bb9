import { AUDIO_TOOLS } from "@divinci-ai/server-tools";
import { uniqueId } from "@divinci-ai/utils";
import { getAudioR2Instance } from "@divinci-ai/server-globals";
import { IS_LOCAL_MODE } from "../../../util/r2-constants";

// Initialize a backup r2 client for fallbacks
const fallbackR2 = getAudioR2Instance();

export async function ensureValidFlac(
  source: { Bucket: string, Key: string },
  whitelabelId: string,
  isLocalMode: boolean = false
) {
  try {
    console.log(`📄 Ensuring valid audio for ${source.Bucket}/${source.Key}`, { isLocalMode });

    // Create destination location with better organization
    const timestamp = Date.now();

    // Extract the original filename from the source key
    const sourceKey = source.Key;
    const sourceKeyParts = sourceKey.split('/');
    const originalFilename = sourceKeyParts[sourceKeyParts.length - 1];

    // Get the filename without extension and extension
    const filenameWithoutExt = originalFilename.includes('.')
      ? originalFilename.substring(0, originalFilename.lastIndexOf('.'))
      : originalFilename;

    // Create a more organized key structure:
    // workspace-audio/{whitelabelId}/audio/{year}/{month}/{day}/{timestamp}_{originalFilename}.flac
    // Using FLAC format for better quality with smaller file size
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    // Get metadata from the source object to retrieve the original filename
    let originalMetadataFilename = originalFilename;
    try {
      const headResponse = await fallbackR2.headObject({
        Bucket: source.Bucket,
        Key: source.Key
      });

      if (headResponse.Metadata && headResponse.Metadata['original-filename']) {
        originalMetadataFilename = headResponse.Metadata['original-filename'];
        console.log(`✅ Retrieved original filename from metadata: ${originalMetadataFilename}`);
      }
    } catch (metadataError) {
      const errorMessage = metadataError instanceof Error
        ? metadataError.message
        : String(metadataError);
      console.warn(`⚠️ Could not retrieve metadata from source object: ${errorMessage}`);
    }

    // Use the original filename from metadata if available
    const finalFilename = originalMetadataFilename || originalFilename;
    const finalFilenameWithoutExt = finalFilename.includes('.')
      ? finalFilename.substring(0, finalFilename.lastIndexOf('.'))
      : finalFilename;

    // Create the destination object
    // We'll let the audio-splitter-ffmpeg service construct the path
    // This is just a placeholder that will be replaced by the actual path
    // returned by the audio-splitter-ffmpeg service
    const destination = {
      Bucket: "workspace-audio",
      Key: `${whitelabelId}/audio/${year}/${month}/${day}/${timestamp}_${finalFilenameWithoutExt}.flac`,
    };

    // In local mode, retry with different bucket names if the first attempt fails
    if (isLocalMode) {
      let success = false;

      // Try multiple bucket options for greater resilience
      const bucketOptions = ["workspace-audio", "audio-transcript-files", "local-audio", "private-temporary-uploads"];

      // Try alternative paths
      for (const bucket of bucketOptions) {
        try {
          destination.Bucket = bucket;
          console.log(`🔄 Attempting audio conversion to ${bucket}/${destination.Key}`);

          const result = await AUDIO_TOOLS.convertToFlac(
            {
              Bucket: source.Bucket,
              Key: source.Key,
            },
            {
              Bucket: bucket,
              Key: destination.Key,
            }
          );

          console.log(`✅ Successfully converted file to ${bucket}/${destination.Key}`);

          // Check if the result has a different Key than what we expected
          // This happens when the audio-splitter-ffmpeg service constructs its own path
          if (result && result.Key && result.Key !== destination.Key) {
            console.log(`⚠️ Audio-splitter-ffmpeg returned a different path: ${result.Key}`);
            console.log(`⚠️ Using the returned path instead of our constructed path`);
            destination.Key = result.Key;
          }

          success = true;
          break;
        } catch (conversionError) {
          console.warn(`⚠️ Conversion to ${bucket}/${destination.Key} failed:`, conversionError);

          // Try a fallback approach - direct copy without conversion
          try {
            console.log(`🔄 Attempting fallback: direct copy to ${bucket}/${destination.Key}`);

            // Try to copy the file directly without conversion
            await fallbackR2.copyObject({
              Bucket: bucket,
              Key: destination.Key,
              CopySource: `${source.Bucket}/${source.Key}`,
              ContentType: "audio/flac",
              Metadata: {}
            });

            console.log(`✅ Fallback: Successfully copied file to ${bucket}/${destination.Key}`);
            success = true;
            break;
          } catch (fallbackError) {
            console.warn(`⚠️ Fallback copy also failed:`, fallbackError);
          }
        }
      }

      if (!success) {
        // Even in local mode, we need to throw if we can't convert the file
        console.error("❌ All audio conversion attempts failed in local mode - cannot proceed");
        throw new Error(`Failed to convert file to FLAC after trying all bucket options: ${source.Bucket}/${source.Key}`);
      }

      return destination;
    } else {
      // Standard production mode
      const result = await AUDIO_TOOLS.convertToFlac(
        {
          Bucket: source.Bucket,
          Key: source.Key,
        },
        destination
      );

      // Check if the result has a different Key than what we expected
      // This happens when the audio-splitter-ffmpeg service constructs its own path
      if (result && result.Key && result.Key !== destination.Key) {
        console.log(`⚠️ Audio-splitter-ffmpeg returned a different path: ${result.Key}`);
        console.log(`⚠️ Using the returned path instead of our constructed path`);
        return result;
      }

      return result;
    }
  } catch (error) {
    console.error("❌ Error ensuring valid audio:", error);

    // Return fallback location for local mode
    if (isLocalMode) {
      // Use the same organized structure for fallback location
      const fallbackTimestamp = Date.now();
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');

      // Extract the original filename from the source key
      const sourceKeyParts = source.Key.split('/');
      const originalFilename = sourceKeyParts[sourceKeyParts.length - 1];

      // Get metadata from the source object to retrieve the original filename
      let originalMetadataFilename = originalFilename;
      try {
        const headResponse = await fallbackR2.headObject({
          Bucket: source.Bucket,
          Key: source.Key
        });

        if (headResponse.Metadata && headResponse.Metadata['original-filename']) {
          originalMetadataFilename = headResponse.Metadata['original-filename'];
          console.log(`✅ Retrieved original filename from metadata for fallback: ${originalMetadataFilename}`);
        }
      } catch (metadataError) {
        const errorMessage = metadataError instanceof Error
          ? metadataError.message
          : String(metadataError);
        console.warn(`⚠️ Could not retrieve metadata from source object for fallback: ${errorMessage}`);
      }

      // Use the original filename from metadata if available
      const finalFilename = originalMetadataFilename || originalFilename;
      const finalFilenameWithoutExt = finalFilename.includes('.')
        ? finalFilename.substring(0, finalFilename.lastIndexOf('.'))
        : finalFilename;

      const destination = {
        Bucket: "workspace-audio",
        Key: `${whitelabelId}/audio/${year}/${month}/${day}/${fallbackTimestamp}_${finalFilenameWithoutExt}.flac`,
      };

      // Try to convert the file using the audio-splitter-ffmpeg service
      try {
        console.log(`🔄 Attempting fallback conversion with audio-splitter-ffmpeg`);
        const result = await AUDIO_TOOLS.convertToFlac(
          {
            Bucket: source.Bucket,
            Key: source.Key,
          },
          destination
        );

        // Check if the result has a different Key than what we expected
        if (result && result.Key && result.Key !== destination.Key) {
          console.log(`⚠️ Audio-splitter-ffmpeg returned a different path for fallback: ${result.Key}`);
          console.log(`⚠️ Using the returned path instead of our constructed path`);
          destination.Key = result.Key;
        }

        console.log(`✅ Successfully converted file to ${destination.Bucket}/${destination.Key} using fallback conversion`);
      } catch (fallbackConversionError) {
        console.warn(`⚠️ Fallback conversion failed:`, fallbackConversionError);
      }

      console.warn(`⚠️ Using fallback location in local mode: ${destination.Bucket}/${destination.Key}`);
      return destination;
    }

    throw error;
  }
}

// For backward compatibility
export const ensureValidMp3 = ensureValidFlac;
