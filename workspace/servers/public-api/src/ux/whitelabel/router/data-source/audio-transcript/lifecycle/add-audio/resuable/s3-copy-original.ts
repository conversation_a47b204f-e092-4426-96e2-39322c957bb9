import { r2, IS_LOCAL_MODE } from "../../../util/r2-constants";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { uniqueId } from "@divinci-ai/utils";
import { parse as pathParse } from "node:path";

export async function getS3Info(
  s3Location: { Bucket: string, Key: string },
  isLocalMode: boolean = false
) {
  try {
    console.log(`📄 Getting S3 info for ${s3Location.Bucket}/${s3Location.Key}`, { isLocalMode });

    // Add retry logic with multiple endpoints for local mode
    let attempts = 0;
    const maxAttempts = 3;
    let lastError: any = null;

    while (attempts < maxAttempts) {
      try {
        const headResult = await r2.headObject({
          Bucket: s3Location.Bucket,
          Key: s3Location.Key,
        });

        const etag = headResult.ETag;
        if (!etag && !isLocalMode) {
          throw HTTP_ERRORS_WITH_CONTEXT.UNAVAILABLE("Missing Etag on file");
        }

        const contentLength = headResult.ContentLength;
        if (!contentLength && !isLocalMode) {
          throw HTTP_ERRORS_WITH_CONTEXT.UNAVAILABLE("Missing contentLength on file");
        }

        return {
          etag: etag?.replace(/"/g, "") || Date.now().toString(),
          contentLength: contentLength || 0,
        };
      } catch (error) {
        console.error(`❌ S3 info attempt ${attempts + 1}/${maxAttempts} failed:`, error);
        lastError = error;
        attempts++;

        // If in local mode and not the last attempt, try with alternative endpoints
        if (isLocalMode && attempts < maxAttempts) {
          console.log("🔄 Retrying with alternative endpoint for local mode");
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait before retry
        } else if (!isLocalMode) {
          // In production mode, don't retry
          throw error;
        }
      }
    }

    // For local mode, we need to be more aggressive in retrying
    if (isLocalMode) {
      console.warn("⚠️ All standard S3 info attempts failed in local mode");
      console.warn("🔄 Making one final attempt with additional logging");

      try {
        // Try one more time with the default client but deeper logging
        const finalAttempt = await r2.headObject({
          Bucket: s3Location.Bucket,
          Key: s3Location.Key,
        });

        console.log("✅ Final attempt succeeded:", finalAttempt);

        return {
          etag: finalAttempt.ETag?.replace(/"/g, "") || Date.now().toString(),
          contentLength: finalAttempt.ContentLength || 0,
        };
      } catch (finalError) {
        console.error("❌ Final attempt also failed:", finalError);
        // We really need to throw here to maintain integrity
        throw new Error(`Failed to get S3 info after all attempts: ${s3Location.Bucket}/${s3Location.Key}`);
      }
    }

    throw lastError || new Error("Failed to get S3 info after multiple attempts");
  } catch (error) {
    console.error("❌ Error in getS3Info:", error);

    // Even in local mode, we need to throw here to maintain integrity
    // The caller can handle the error appropriately
    console.error(`⚠️ Critical error in getS3Info - unable to proceed: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

export async function copyOriginalToS3(
  source: { Bucket: string, Key: string },
  whitelabelId: string,
  isLocalMode: boolean = false
) {
  try {
    console.log(`📄 Copying original file from ${source.Bucket}/${source.Key}`, { isLocalMode });

    const parsed = pathParse(source.Key);
    if (!parsed.ext) {
      if (isLocalMode) {
        console.warn("⚠️ Source file has no extension, using .unknown in local mode");
        parsed.ext = ".unknown";
      } else {
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Url requires an extension");
      }
    }

    // Create destination location with better organization
    const timestamp = Date.now();

    // Extract the original filename from the source key
    const sourceKeyParts = source.Key.split('/');
    const originalFilename = sourceKeyParts[sourceKeyParts.length - 1];

    // Get the filename without extension
    const filenameWithoutExt = originalFilename.includes('.')
      ? originalFilename.substring(0, originalFilename.lastIndexOf('.'))
      : originalFilename;

    // Create a more organized key structure:
    // workspace-audio/{whitelabelId}/original/{year}/{month}/{day}/{timestamp}_{originalFilename}
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    const destination = {
      Bucket: "workspace-audio",
      Key: `${whitelabelId}/original/${year}/${month}/${day}/${timestamp}_${filenameWithoutExt}${parsed.ext}`
    };

    // In local mode, retry with different bucket names if the first attempt fails
    if (isLocalMode) {
      let success = false;

      // Try multiple bucket options for greater resilience
      const bucketOptions = ["workspace-audio", "audio-transcript-files", "local-audio", "private-temporary-uploads"];

      // Use the reliable MinIO endpoint
      const minioEndpoints = [
        { endpoint: "http://minio.divinci.local:9000" } // Reliable DNS alias
      ];

      // Try each bucket and endpoint combination
      for (const bucket of bucketOptions) {
        for (const endpoint of minioEndpoints) {
          try {
            destination.Bucket = bucket;

            // For logging - show which endpoint we're using
            const endpointStr = endpoint?.endpoint || 'default';
            console.log(`🔄 Attempting copy to ${bucket}/${destination.Key} with endpoint: ${endpointStr}`);

            // Create a new client with the current endpoint if provided
            const client = endpoint ? {
              ...r2,
              config: { ...r2.config, endpoint: endpoint.endpoint }
            } : r2;

            // Try to copy the file with the current bucket/client combination
            await client.copyObject({
              ...destination,
              CopySource: `${source.Bucket}/${source.Key}`,
            });

            console.log(`✅ Successfully copied to ${bucket}/${destination.Key} with endpoint: ${endpointStr}`);
            success = true;
            break;
          } catch (error) {
            const endpointStr = endpoint?.endpoint || 'default';
            console.warn(`⚠️ Failed to copy to ${bucket} with endpoint ${endpointStr}`);
          }
        }

        if (success) break; // If we succeeded with any endpoint, exit bucket loop
      }

      if (!success && isLocalMode) {
        // Even in local mode, we need to throw if we can't copy the file
        console.error("❌ All copy attempts failed in local mode - cannot proceed");
        throw new Error(`Failed to copy file after trying all bucket/endpoint combinations: ${source.Bucket}/${source.Key}`);
      }
    } else {
      // Standard production mode
      await r2.copyObject({
        ...destination,
        CopySource: `${source.Bucket}/${source.Key}`,
      });
    }

    console.log(`✅ File copy completed to ${destination.Bucket}/${destination.Key}`);
    return destination;
  } catch (error) {
    console.error("❌ Error copying original file to S3:", error);

    // Return fallback location for local mode
    if (isLocalMode) {
      const parsed = pathParse(source.Key);
      const ext = parsed.ext || ".unknown";

      // Use the same organized structure for fallback location
      const timestamp = Date.now();
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');

      // Extract the original filename from the source key
      const sourceKeyParts = source.Key.split('/');
      const originalFilename = sourceKeyParts[sourceKeyParts.length - 1];

      // Get the filename without extension
      const filenameWithoutExt = originalFilename.includes('.')
        ? originalFilename.substring(0, originalFilename.lastIndexOf('.'))
        : originalFilename;

      const destination = {
        Bucket: "workspace-audio",
        Key: `${whitelabelId}/original/${year}/${month}/${day}/${timestamp}_${filenameWithoutExt}${ext}`
      };

      console.warn(`⚠️ Using fallback location in local mode: ${destination.Bucket}/${destination.Key}`);
      return destination;
    }

    throw error;
  }
}
