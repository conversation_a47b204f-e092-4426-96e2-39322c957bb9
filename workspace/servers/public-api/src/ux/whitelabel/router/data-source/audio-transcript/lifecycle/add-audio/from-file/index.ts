import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { CastedBusBoy, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { transcribeMedia } from "../resuable/transcribe-media";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { R2BusBoyFileHandler } from "@divinci-ai/server-globals";

import { r2, IS_LOCAL_MODE } from "../../../util/r2-constants";
// Use a bucket name that doesn't include the hostname in local mode
// In local mode, use a different bucket name that's guaranteed to exist
const BUCKET_NAME = IS_LOCAL_MODE ? "workspace-audio" : "private-temporary-uploads";

console.log(`🪣 Using bucket name: ${BUCKET_NAME}, isLocalMode: ${IS_LOCAL_MODE}`);

// Use the existing r2 client which already has the correct endpoint configuration
// The r2 client from server-globals already handles multiple endpoints in local mode
const audioR2Client = r2;

const fileHandler = new R2BusBoyFileHandler(
  audioR2Client,
  BUCKET_NAME,
  {}, // Empty metadata object
);

const castAudioBody = CastedBusBoy.create(
  {
    mediaFile: "file",
    diarizerTool: "string",
    transcriberTool: "string",
    isLocalMode: "string?", // Make this optional for local mode
  }, fileHandler,
  { mediaFile: (info)=>{
    ensureValidMediaName(info.filename);
    return true;
  } }
);

import { ensureValidMediaName } from "../resuable/validate-media-name";

export const createAudioTranscriptFromFile: RequestHandler = async function(req, res, next){
  try {
    // Get the body and target information
    const [{ target, whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      castAudioBody.consumeRequest(req),
    ]);

    // Check for local mode from environment or form data (avoiding headers for CORS)
    const isLocalMode = IS_LOCAL_MODE ||
                        (body.isLocalMode && body.isLocalMode === "true") ||
                        process.env.ENVIRONMENT === "local" ||
                        process.env.NODE_ENV === "development" ||
                        process.env.NODE_ENV === "local";

    // Force local mode in development environments
    if (process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development") {
      console.log("🔍 Forcing local mode based on environment");
    }

    console.log("📝 Audio transcript file upload request:", {
      method: req.method,
      formLocalMode: body.isLocalMode === "true",
      environment: process.env.ENVIRONMENT || process.env.NODE_ENV,
      isLocalMode
    });

    console.log("📦 Request body received:", {
      diarizerTool: body.diarizerTool,
      transcriberTool: body.transcriberTool,
      mediaFileInfo: body.mediaFile.info ? {
        filename: body.mediaFile.info.filename,
        mimeType: body.mediaFile.info.mimeType,
        // Only log properties that we know exist on FileInfo
      } : null,
      mediaFile: {
        bucket: body.mediaFile.bucket,
        objectKey: body.mediaFile.objectKey
      },
      localModeRequested: body.isLocalMode === "true"
    });

    // Set special options for transcription in local mode
    const localModeOptions = isLocalMode ? {
      useLocalMinio: true,
      skipCloudServices: true,
      environment: 'local',
      forceLocalMode: true
    } : undefined;

    if (isLocalMode) {
      console.log("🔍 Using LOCAL MODE for audio transcript");
    }

    // Start the transcription process asynchronously
    const doc = await transcribeMedia(
      target, whitelabel._id.toString(),
      { diarizer: body.diarizerTool, transcriber: body.transcriberTool },
      { Bucket: body.mediaFile.bucket, Key: body.mediaFile.objectKey },
      {
        filename: body.mediaFile.info.filename,
        sourceType: "file",
        rawValue: body.mediaFile.info.filename,
      },
      undefined, // copiedLocation
      localModeOptions    // Added options parameter for local mode
    );

    // Return immediately with the document ID and status URL
    res.statusCode = 202; // Accepted
    res.json({
      id: doc._id,
      status: doc.processStatus,
      statusUrl: `/white-label/${whitelabel._id}/data-source/audio-transcript/${doc._id}/status`,
      message: `Audio transcription job started. ${isLocalMode ? 'Using local mode.' : ''} Poll the statusUrl to check progress.`,
      isLocalMode // Include this flag so the frontend knows we're in local mode
    });
  }catch(e){
    console.error("❌ Error in createAudioTranscriptFromFile:", e);
    next(e);
  }
};
