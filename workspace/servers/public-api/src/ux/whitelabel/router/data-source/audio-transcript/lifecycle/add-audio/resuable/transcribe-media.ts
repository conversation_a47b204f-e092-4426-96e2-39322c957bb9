
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { getAudioTools } from "../../../util/audio-tools";
import { getAudioR2Instance } from "@divinci-ai/server-globals";
import { CLOUDFLARE_AUDIO_PUBLIC_URL, LOCAL_AUDIO_PUBLIC_URL, IS_LOCAL_MODE, r2 } from "../../../util/r2-constants";
import { AUDIO_TOOLS } from "@divinci-ai/server-tools";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

import { getS3Info, copyOriginalToS3 } from "./s3-copy-original";
import { ensureValidFlac } from "./ensure-valid-flac";

// Initialize a backup r2 client for fallbacks
const fallbackR2 = getAudioR2Instance();

// Add this type for local mode options
interface LocalModeOptions {
  useLocalMinio: boolean;
  skipCloudServices: boolean;
  environment: string;
  forceLocalMode?: boolean;
}

export async function transcribeMedia(
  target: string, whitelabelId: string,
  toolIds: { diarizer: string, transcriber: string },
  originalS3Location: { Bucket: string, Key: string },
  sourceInfo: { filename: string, sourceType: "url" | "file", rawValue: string },
  copiedLocation?: { Bucket: string, Key: string },
  localModeOptions?: LocalModeOptions
){
  const isLocalMode = localModeOptions?.forceLocalMode ||
                      localModeOptions?.useLocalMinio ||
                      IS_LOCAL_MODE ||
                      process.env.ENVIRONMENT === "local" ||
                      process.env.NODE_ENV === "development" ||
                      process.env.NODE_ENV === "local";

  console.log("🔄 Transcribe media called with options:", {
    target,
    whitelabelId,
    toolIds,
    originalS3Location,
    sourceType: sourceInfo.sourceType,
    filename: sourceInfo.filename,
    isLocalMode,
    localModeOptions
  });

  const convertArgs = await AUDIO_TOOLS.canConvertToFlac(sourceInfo.filename);
  if(!convertArgs.support){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Unsupported file type");
  }

  // Implement the logic to transcribe the video
  const tools = getAudioTools({
    diarizer: toolIds.diarizer,
    transcriber: toolIds.transcriber,
  });

  // In local mode, we might not be able to get S3 info, so we handle failures differently
  let etag = "";
  let contentLength = 0;

  try {
    const s3Info = await getS3Info(originalS3Location, isLocalMode);
    etag = s3Info.etag;
    contentLength = s3Info.contentLength;
  } catch (error) {
    if (isLocalMode) {
      console.warn("⚠️ Failed to get S3 info in local mode, using dummy values", error);
      etag = `dummy-etag-${Date.now()}`;
      contentLength = 12345;
    } else {
      throw error;
    }
  }

  // Process the audio file using MP3 conversion and S3 copy
  let audioS3Location;
  let rawS3Location;

  try {
    [audioS3Location, rawS3Location] = await Promise.all([
      ensureValidFlac(originalS3Location, whitelabelId, isLocalMode),
      copiedLocation ? copiedLocation : copyOriginalToS3(originalS3Location, whitelabelId, isLocalMode),
    ]);
  } catch (error) {
    if (isLocalMode) {
      console.warn("⚠️ Failed to process files in local mode, using dummy values", error);
      const timestamp = Date.now();
      const filenameWithoutExt = sourceInfo.filename.replace(/\.[^.]+$/, '');
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');

      audioS3Location = {
        Bucket: "workspace-audio",
        Key: `${whitelabelId}/audio/${year}/${month}/${day}/${timestamp}_${filenameWithoutExt}.flac`
      };

      rawS3Location = {
        Bucket: "workspace-audio",
        Key: `${whitelabelId}/original/${year}/${month}/${day}/${timestamp}_${filenameWithoutExt}${sourceInfo.filename.includes('.') ? '.' + sourceInfo.filename.split('.').pop() : ''}`
      };
    } else {
      throw error;
    }
  }

  // Choose the appropriate public URL based on environment
  // Note: We're now using FLAC files instead of MP3
  const publicUrl = isLocalMode
    ? (LOCAL_AUDIO_PUBLIC_URL || "http://localhost:9000") + "/" + audioS3Location.Key
    : CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + audioS3Location.Key;

  console.log("📄 Using public URL:", publicUrl);

  // Create a pending transcript record
  const { doc } = await DataSourceAudioTranscriptModel.createTranscript(
    target, tools, {
      sourceType: sourceInfo.sourceType,
      mediaType: convertArgs.type,
      sourceId: `${etag}-${contentLength}`,
      info: {
        rawValue: sourceInfo.rawValue,
        rawKey: rawS3Location.Key,
        rawBucket: rawS3Location.Bucket,
        fileType: convertArgs.type,
        filename: sourceInfo.filename,
        hash: etag,
        filesize: contentLength,
        isLocalMode // Add flag for local mode
      }
    },
    {
      Bucket: audioS3Location.Bucket,
      Key: audioS3Location.Key,
      filename: sourceInfo.filename,
      publicUrl
    }
  );

  // For local mode, just log additional information but process normally
  if (isLocalMode) {
    console.log("📄 Using local mode, real audio file will be processed");
    console.log(`📄 Audio file (FLAC) located at: ${audioS3Location.Bucket}/${audioS3Location.Key}`);

    // Define alternative URLs for debugging
    const minioHttpUrls = [
      `http://minio.divinci.local:9000/${audioS3Location.Key}`, // DNS alias - reliable option
      `http://localhost:9000/${audioS3Location.Key}`,
      `http://127.0.0.1:9000/${audioS3Location.Key}`,
      `http://host.docker.internal:9000/${audioS3Location.Key}`
    ];

    console.log("🔍 MinIO HTTP access URLs (for debugging):");
    minioHttpUrls.forEach(url => console.log(`   - ${url}`));

    // Verify if the file exists in MinIO using the reliable endpoint
    let fileVerified = false;
    const minioEndpoint = "http://minio.divinci.local:9000"; // Reliable DNS alias

    try {
      // Create a client with the reliable endpoint
      const client = {
        ...fallbackR2,
        config: { ...fallbackR2.config, endpoint: minioEndpoint }
      };

      console.log(`🔍 Verifying file existence with endpoint: ${minioEndpoint}`);

      const headResult = await client.headObject({
        Bucket: audioS3Location.Bucket,
        Key: audioS3Location.Key
      });

      console.log(`✅ Verified audio file exists in MinIO with endpoint ${minioEndpoint}:`, {
        Bucket: audioS3Location.Bucket,
        Key: audioS3Location.Key,
        ContentLength: headResult.ContentLength,
        ContentType: headResult.ContentType
      });

      fileVerified = true;
    } catch (verifyError) {
      console.warn(`⚠️ Failed to verify file with endpoint ${minioEndpoint}`);
    }

    if (!fileVerified) {
      console.error("❌ Could not verify audio file existence in MinIO with any endpoint.");
      console.log("🔍 This may be OK if the file is accessible via HTTP. Audio processing will continue.");
    }

    // Add additional logging but don't modify the normal process
    console.log("📝 In local mode, will now continue with normal processing");
  }

  return doc;
}


