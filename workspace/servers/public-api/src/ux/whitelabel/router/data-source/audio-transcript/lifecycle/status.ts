import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getParam } from "@divinci-ai/server-utils";
import { getWhitelabelTarget } from "../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { AudioTranscriptStatus } from "@divinci-ai/models";
import { getChatRedisClient } from "@divinci-ai/server-globals";

/**
 * Get the status of an audio transcription job
 */
export const getAudioTranscriptStatus: RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");
    const { target } = await getWhitelabelTarget(req);

    const doc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    }).select("processStatus errorStatus samples ignoredSamples failedSamples");

    if(!doc) throw HTTP_ERRORS.NOT_FOUND;

    // Prepare the response based on the process status
    const response: any = {
      id: doc._id,
      status: doc.processStatus,
    };

    // Try to get detailed progress from Redis
    let detailedProgress = null;
    try {
      const { chatRedisClient } = getChatRedisClient();
      const progressKey = `audio:progress:${audioId}`;
      const progressData = await chatRedisClient.get(progressKey);

      if (progressData) {
        detailedProgress = JSON.parse(progressData);
      }
    } catch (redisError) {
      const errorMessage = redisError instanceof Error
        ? redisError.message
        : String(redisError);
      console.warn(`Failed to get progress data from Redis: ${errorMessage}`);
    }

    // Add additional information based on the status
    switch(doc.processStatus) {
      case AudioTranscriptStatus.Completed:
        response.message = "Transcription completed successfully";
        response.progress = 100;
        response.step = "completed";
        response.samplesCount = doc.samples.length;
        response.ignoredCount = doc.ignoredSamples.length;
        response.failedCount = doc.failedSamples.length;
        break;

      case AudioTranscriptStatus.Failed:
        response.message = "Transcription failed";
        response.error = doc.errorStatus;
        response.progress = 0;
        response.step = "failed";
        break;

      case AudioTranscriptStatus.Diarization:
        response.message = "Processing audio diarization";

        // Use detailed progress from Redis if available
        if (detailedProgress && detailedProgress.step === "segmentation") {
          response.progress = Math.min(detailedProgress.progress, 50); // Cap at 50% for diarization phase
          response.step = "diarization";
          response.detailedStep = detailedProgress.step;
        } else if (detailedProgress && detailedProgress.step === "speaker_counting") {
          response.progress = 50; // Speaker counting is halfway through diarization
          response.step = "diarization";
          response.detailedStep = detailedProgress.step;
        } else if (detailedProgress && detailedProgress.step === "embeddings") {
          response.progress = 50 + (detailedProgress.progress / 2); // Embeddings is second half of diarization
          response.step = "diarization";
          response.detailedStep = detailedProgress.step;
        } else {
          response.progress = 25; // Default fallback
          response.step = "diarization";
        }
        break;

      case AudioTranscriptStatus.Transcription:
        response.message = "Processing audio transcription";

        // Use detailed progress from Redis if available
        if (detailedProgress && detailedProgress.step === "transcription") {
          response.progress = 75 + (detailedProgress.progress / 4); // Transcription is last 25%
          response.step = "transcription";
          response.detailedStep = detailedProgress.step;
        } else {
          response.progress = 75; // Default fallback
          response.step = "transcription";
        }
        break;

      default:
        response.message = "Unknown status";
        response.progress = 0;
        response.step = "unknown";
    }

    res.statusCode = 200;
    res.json(response);
  } catch(e) {
    next(e);
  }
};
