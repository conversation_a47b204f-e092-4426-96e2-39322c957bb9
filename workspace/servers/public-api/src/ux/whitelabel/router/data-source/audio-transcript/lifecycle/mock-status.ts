import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getParam } from "@divinci-ai/server-utils";
import { AudioTranscriptStatus } from "@divinci-ai/models";

/**
 * Mock status endpoint for audio transcription jobs
 * This is used for testing purposes when the real document doesn't exist
 */
export const getMockAudioTranscriptStatus: RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");

    // Simulate a processing delay based on the audioId
    // This helps test different states of the transcription process
    const lastChar = audioId.slice(-1);
    let status: string;
    let progress: number;
    let message: string;
    let step: string;
    let detailedStep: string | undefined;

    // Use the last character of the ID to determine the status
    switch (lastChar) {
      case '0':
        status = AudioTranscriptStatus.Diarization;
        progress = 15;
        message = "Processing audio diarization";
        step = "diarization";
        detailedStep = "segmentation";
        break;
      case '1':
        status = AudioTranscriptStatus.Diarization;
        progress = 35;
        message = "Processing audio diarization";
        step = "diarization";
        detailedStep = "embeddings";
        break;
      case '2':
        status = AudioTranscriptStatus.Transcription;
        progress = 75;
        message = "Processing audio transcription";
        step = "transcription";
        detailedStep = "transcription";
        break;
      case '3':
        status = AudioTranscriptStatus.Transcription;
        progress = 90;
        message = "Processing audio transcription";
        step = "transcription";
        detailedStep = "transcription";
        break;
      case '4':
      case '5':
        status = AudioTranscriptStatus.Failed;
        progress = 0;
        message = "Transcription failed";
        step = "failed";
        break;
      default:
        status = AudioTranscriptStatus.Completed;
        progress = 100;
        message = "Transcription completed successfully";
        step = "completed";
    }

    // Prepare the response based on the process status
    const response: any = {
      id: audioId,
      status,
      message,
      progress,
      step,
    };

    // Add detailed step if available
    if (detailedStep) {
      response.detailedStep = detailedStep;
    }

    // Add additional information for completed status
    if (status === AudioTranscriptStatus.Completed) {
      response.samplesCount = 10;
      response.ignoredCount = 0;
      response.failedCount = 0;
    }

    // Add error information for failed status
    if (status === AudioTranscriptStatus.Failed) {
      response.error = {
        step: "transcription",
        message: "Mock error message for testing"
      };
    }

    res.statusCode = 200;
    res.json(response);
  } catch(e) {
    next(e);
  }
};
