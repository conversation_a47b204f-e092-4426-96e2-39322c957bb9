import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response } from "express";
import {
  NotificationSettingsModel,
  NotificationSettingsDefaultModel,
  INotificationSettingsDoc,
} from "@divinci-ai/server-models";
import { jsonBody, HTTP_ERRORS } from "@divinci-ai/server-utils";
import mongoose, { ObjectId } from "mongoose";

const notificationNotFoundMessage = "👀 Notification not found";

interface EmailObject {
  email: string,
  verified: boolean,
  whitelabelIds: string[],
  _id: string,
}

// Base interface for notification settings
interface NotificationSettingsBaseProps {
  emailEnabled: boolean,
  smsEnabled: boolean,
  emails: EmailObject[],
  phoneNumbers: string[],
  whitelabelId: string, // Reference to WhiteLabelModel
}

// Interface for notification settings with optional trigger description
interface NotificationSettingsRequestBody
  extends NotificationSettingsBaseProps {
  triggerDescription?: string,
}

// Function to validate the request body
const validateNotificationSettingsRequestBody = (
  body: any,
): body is NotificationSettingsRequestBody=>{
  if(typeof body.emailEnabled !== "boolean"){
    console.error("Invalid emailEnabled:", body.emailEnabled);
    return false;
  }

  if(typeof body.smsEnabled !== "boolean"){
    console.error("Invalid smsEnabled:", body.smsEnabled);
    return false;
  }

  if(!Array.isArray(body.emails)){
    console.error("Invalid emails array:", body.emails);
    return false;
  }

  for(const email of body.emails){
    if(typeof email.email !== "string"){
      console.error("Invalid email.email:", email.email);
      return false;
    }
    if(typeof email.verified !== "boolean"){
      console.error("Invalid email.verified:", email.verified);
      return false;
    }
    if(!Array.isArray(email.whitelabelIds)){
      console.error("Invalid email.whitelabelIds:", email.whitelabelIds);
      return false;
    }
    if(typeof email._id !== "string"){
      console.error("Invalid email._id:", email._id);
      return false;
    }
  }

  if(!Array.isArray(body.phoneNumbers)){
    console.error("Invalid phoneNumbers array:", body.phoneNumbers);
    return false;
  }

  // if (typeof body.whitelabelId !== 'string'){
  //   console.error('Invalid whitelabelId:', body.whitelabelId);
  //   return false;
  // }

  if(
    typeof body.triggerDescription !== "undefined" &&
    typeof body.triggerDescription !== "string"
  ){
    console.error("Invalid triggerDescription:", body.triggerDescription);
    return false;
  }

  return true;
};

export const createNotification: RequestHandler = async (req, res, next)=>{
  try {
    const notification = new NotificationSettingsModel(await jsonBody(req));
    await notification.save();
    res.statusCode = 201;
    res.json(notification);
  }catch(error){
    next(error);
  }
};

export const getNotifications: RequestHandler = async (req, res, next)=>{
  try {
    const notifications = await NotificationSettingsModel.find({});
    res.json(notifications);
  }catch(error){
    next(error);
  }
};

export const getNotificationById: RequestHandler = async (req, res, next)=>{
  try {
    const notification = await NotificationSettingsModel.findById(
      req.params.id,
    );
    if(!notification){
      return res.status(404).json({
        message: notificationNotFoundMessage,
      });
    }
    res.json(notification);
  }catch(error){
    next(error);
  }
};

export const updateNotificationById: RequestHandler = async (
  req,
  res,
  next,
)=>{
  try {
    const body: any = await jsonBody(req);
    const notification = await NotificationSettingsModel.findByIdAndUpdate(
      req.params.id,
      body,
      { new: true },
    );
    if(!notification){
      return res.status(404).json({ message: notificationNotFoundMessage });
    }
    res.json(notification);
  }catch(error){
    next(error);
  }
};

export const createNotificationSettings: RequestHandler = async (
  req,
  res,
  next,
)=>{
  try {
    // Parse the request body using jsonBody and assert its type
    const body = (await jsonBody(
      req,
    )) as unknown as NotificationSettingsRequestBody;

    // Manually validate the request body
    if(!validateNotificationSettingsRequestBody(body)){
      throw HTTP_ERRORS.BAD_FORM;
    }

    // Create a new NotificationSettingsModel instance with the provided data
    const newNotificationSettings = new NotificationSettingsModel(body);

    // Save the new notification settings to the database
    await newNotificationSettings.save();

    // Respond with the newly created notification settings
    res.json(newNotificationSettings);
  }catch(error){
    console.error("❌ Error in createNotificationSettings: ", error);
    next(error);
  }
};

export const updateNotificationSettings: RequestHandler = async (
  req,
  res,
  next,
)=>{
  try {
    const { whitelabelId, id } = req.params;
    console.log("🆙📢🎛️ updateNotificationSettings req.params: ", req.params);
    const settings = (await jsonBody(
      req,
    )) as unknown as NotificationSettingsRequestBody;

    // Log the NotificationSettingsModel to check if it's undefined
    console.log("📢🎛️ NotificationSettingsModel: ", NotificationSettingsModel);
    // Log the request parameters
    console.log("📢🎛️ Request Parameters:", { whitelabelId, id });
    // Log the request body
    console.log("📢🎛️ Request Body for updateNotificationSettings:", settings);

    // Use findOneAndUpdate with upsert option to either update or insert a new document
    const updatedSettings = await NotificationSettingsModel.findOneAndUpdate(
      { whitelabelId: whitelabelId, _id: id }, // Ensure this query is correct
      { $set: settings },
      { new: true, upsert: true, strict: false }, // Not recommended for general use
    );

    // Check if settings is not undefined and contains the necessary properties
    if(!validateNotificationSettingsRequestBody(settings)){
      console.error(
        "📢🙅🏻‍♂️ Invalid request body. Missing required properties in updateNotificationSettings. ",
      );
      throw HTTP_ERRORS.BAD_FORM;
    }

    // Log the result of the database query
    console.log("📢🎛️ Database Query Result:", updatedSettings);

    res.statusCode = 200;
    res.json(updatedSettings);
  }catch(error){
    console.error("❌📢🎛️ Error in updateNotificationSettings: ", error);
    next(error);
  }
};

export const updateNotificationDefaultSettings: RequestHandler = async (
  req,
  res,
  next,
)=>{
  try {
    const { id } = req.params;
    const body = (await jsonBody(
      req,
    )) as unknown as NotificationSettingsRequestBody;

    console.log("📞 Request body: ", body); // Add logging
    console.log("📞 Request params: ", req.params); // Add logging

    // Validate the request body
    if(!validateNotificationSettingsRequestBody(body)){
      return res
        .status(400)
        .json({ status: "error", message: "❌ Invalid request body format" });
    }

    const _id = new mongoose.Types.ObjectId(id);

    // Update the notification settings
    const updatedNotification =
      await NotificationSettingsDefaultModel.findByIdAndUpdate(
        _id,
        { $set: body },
        { new: true },
      );

    console.log(
      "📢 updateNotificationDefaultSettings::updatedNotification: ",
      updatedNotification,
    );

    if(!updatedNotification){
      return res
        .status(404)
        .json({
          status: "error",
          message: "❌ Notification settings not found. ",
        });
    }

    res.json({ status: "success", data: updatedNotification });
  }catch(error){
    console.error("❌ Error updating notification settings:", error);
    next(error);
  }
};

export const getNotificationSettings = async (req: Request, res: Response)=>{
  try {
    const whitelabelId = req.params.whitelabelId;
    // Use find to fetch all documents matching the whitelabelId
    // Directly use INotificationSettingsDoc interface for typing the result array
    const notificationSettings: (INotificationSettingsDoc & {
      _id: ObjectId,
    })[] = await NotificationSettingsModel.find({ whitelabelId });

    res.statusCode = 200;
    res.json(notificationSettings);
  }catch(error){
    console.error(
      "❌ An error occurred while retrieving or creating notification settings.",
    );
    throw HTTP_ERRORS.BAD_FORM;
  }
};

async function createEmptyDefaultNotification(whitelabelId: string){
  const notificationDefaultSettings = new NotificationSettingsDefaultModel({
    emailEnabled: true, // Default value
    smsEnabled: false, // Default value
    emails: [], // Default value
    phoneNumbers: [], // Default value
    whitelabelId,
  });
  await notificationDefaultSettings.save();
}

export const getNotificationDefaultSettings = async (
  req: Request,
  res: Response,
)=>{
  try {
    const whitelabelId = req.params.whitelabelId;
    const notificationDefaultSettings =
      await NotificationSettingsDefaultModel.findOne({ whitelabelId });

    if(!notificationDefaultSettings){
      // If not found, create new default notification settings with default values
      await createEmptyDefaultNotification(whitelabelId);
    }

    res.statusCode = 200;
    res.json(notificationDefaultSettings);
  }catch(error){
    console.error(
      "❌ An error occurred while retrieving or creating notification default settings.",
    );
    throw HTTP_ERRORS.BAD_FORM;
  }
};

export const deleteNotificationSettingById: RequestHandler = async (
  req,
  res,
  next,
)=>{
  try {
    // Track who delete things in the future!
    //  const userId = getUserId(req);

    // Use findOneAndDelete with a filter to match both the notification ID and the user ID
    // This is just an example; adjust the filter as necessary for your application's logic
    const result = await NotificationSettingsModel.findOneAndDelete({
      _id: req.params.id,
      // Add any additional conditions here, e.g., ownership
      // ownerUser: userId,
    });

    if(!result){
      throw HTTP_ERRORS.BAD_FORM;
    }

    res.json({
      message: "🟢🚮 Notification deleted successfully!",
    });
  }catch(error){
    next(error);
  }
};

export default getNotificationSettings;
