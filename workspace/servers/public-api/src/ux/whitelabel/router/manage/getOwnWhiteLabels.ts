import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { ReleaseStatus } from "@divinci-ai/models";
import {
  getUserId,
} from "@divinci-ai/server-globals";

import {
  ChatModel,
  WhiteLabelModel,
  WhiteLabelReleaseModel,
} from "@divinci-ai/server-models";

export const getOwnWhiteLabels: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);
    const whitelabelList = await WhiteLabelModel.find({
      ownerUser: user_id,
    }).exec();


    const whitelabelsWithMeta = await WhiteLabelModel.getWhitelabelMeta(whitelabelList);

    res.statusCode = 200;
    res.json(whitelabelsWithMeta);
  }catch(e) {
    next(e);
  }
};


function releaseAggregate(whitelabelList: Array<InstanceType<typeof WhiteLabelModel>>){

  const whitelabelIdList = whitelabelList.map((doc)=>(doc._id.toString()));
  console.log("Chat Collection:", ChatModel.collection.name);

  return WhiteLabelReleaseModel.aggregate([
    // 1. Match only the releases you care about
    {
      $match: {
        status: ReleaseStatus.AVAILABLE,
        whitelabel: { $in: whitelabelIdList },
      },
    },

    // 2. Join with ChatModel to get any chats that reference these releases
    {
      $lookup: {
        from: ChatModel.collection.name,            // The name of your ChatModel collection
        let: {
          releaseIdString: { $toString: "$_id" }, // Convert ObjectId => string
        },
        pipeline: [
          {
            // Only match chats whose releases array contains our releaseIdString
            $match: {
              $expr: {
                $in: ["$$releaseIdString", "$releases"],
              },
            },
          },
        ],
        as: "chats",
      },
    },

    // 3. Unwind the joined chats array (preserving empty arrays if no chats)
    {
      $unwind: {
        path: "$chats",
        preserveNullAndEmptyArrays: true,
      },
    },

    // 4. Group by the workspace
    {
      $group: {
        _id: "$whitelabel",

        // Keep track of workspace
        whitelabel: { $first: "$whitelabel" },

        // Count how many releases are in this workspace
        releaseCount: { $sum: 1 },

        // Gather all release IDs for this workspace
        releaseIds: { $addToSet: "$_id" },

        // Collect all chat IDs (using $addToSet to avoid duplicates)
        chatIds: { $addToSet: "$chats._id" },

        // Collect all unique owner users
        owners: { $addToSet: "$chats.ownerUser" },
      },
    },

    // 5. Final projection
    {
      $project: {
        whitelabel: 1,
        releaseCount: 1,
        releaseIds: 1,

        // Chat count is simply the size of the unique chat IDs set
        chatCount: { $size: "$chatIds" },

        // owners is already a unique set of owners
        userCount: { $size: "$owners" },
      },
    },
  ]);
}
