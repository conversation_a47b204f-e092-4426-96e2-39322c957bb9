import { HTTP_ERRORS, WebSocketHandlerCallback } from "@divinci-ai/server-utils";
import { getUserCookieFromRequest } from "../../util/user-cookie";

import { ChannelSubscriber, getRedis } from "@divinci-ai/server-globals";

const { redisClient } = getRedis();
const moneySubscriber = new ChannelSubscriber(redisClient);

export const watchMoneyUpdate: WebSocketHandlerCallback = async function(
  request, params, next
){
  try {
    const userId = getUserCookieFromRequest(request.httpRequest);
    if(!userId){
      throw HTTP_ERRORS.UNAUTHORIZED;
    }
    const websocket = await request.accept();

    const l = (updatedMoney: string)=>{
      websocket.send(updatedMoney);
    };

    const eventPath = `/user/money/${userId}`;
    moneySubscriber.on(eventPath, l);
    websocket.addListener("close", ()=>{
      moneySubscriber.off(eventPath, l);
    });

  }catch(e) {
    console.error("user money live:", e);
    next(e);
  }
};
