import { HTTP_ERRORS_WITH_CONTEXT, WebSocketHandlerCallback } from "@divinci-ai/server-utils";

import { WhiteLabelModel, WhiteLabelReleaseModel, ChatModel } from "@divinci-ai/server-models";

import { getUserCookieFromRequest } from "../../../util/user-cookie";

import { chatSubscriber } from "../../../data/redis";

export const watchThreadUpdate: WebSocketHandlerCallback = async function(
  request, params, next
){
  try {
    getUserCookieFromRequest(request.httpRequest);
    const { whitelabelId, chatId } = params;

    const whitelabel = await WhiteLabelModel.findById(whitelabelId);

    if(whitelabel === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("White Label Doesn't Exist");
    }

    const allReleases = await WhiteLabelReleaseModel.find({ whitelabel: whitelabelId })
    .select(["_id", "status"]);
    if(allReleases.length === 0){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("No Releases for this Whitelabel");
    }

    const releaseIds = allReleases.map((r)=>(r._id.toString()));

    const chat = await ChatModel.findOne({
      _id: chatId, releases: { $in: releaseIds }
    });

    if(chat === null){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Chat Thread Doesn't Exist");
    }
    const eventPath = `/transcript/${chat.transcriptId}`;

    const websocket = await request.accept();
    const l = ()=>{
      websocket.send(JSON.stringify("update"));
    };

    chatSubscriber.on(`/transcript/${chat.transcriptId}`, l);
    websocket.addListener("close", ()=>{
      chatSubscriber.off(eventPath, l);
    });
  }catch(e) {
    console.error("chat live:", e);
    next(e);
  }
};
