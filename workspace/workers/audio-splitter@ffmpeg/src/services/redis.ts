import { createClient } from 'redis';

let redisClient: ReturnType<typeof createClient> | null = null;

/**
 * Get a Redis client instance
 * @returns A Redis client instance
 */
export async function getRedisClient() {
  if (!redisClient) {
    const redisUrl = process.env.REDIS_URL || 'redis://redis:6379';
    console.log(`Connecting to Redis at ${redisUrl}`);
    
    redisClient = createClient({
      url: redisUrl
    });
    
    redisClient.on('error', (err) => {
      console.error('Redis Client Error', err);
    });
    
    await redisClient.connect();
  }
  
  return redisClient;
}

/**
 * Store audio processing progress in Redis
 * @param audioId The ID of the audio file
 * @param step The processing step
 * @param progress The progress percentage
 * @param ttl Time to live in seconds (default: 1 hour)
 */
export async function storeAudioProgress(
  audioId: string,
  step: string,
  progress: number,
  ttl: number = 3600
) {
  try {
    const redis = await getRedisClient();
    const key = `audio:progress:${audioId}`;
    
    const data = JSON.stringify({
      step,
      progress,
      updatedAt: new Date().toISOString()
    });
    
    await redis.set(key, data, { EX: ttl });
    console.log(`Stored progress for audio ${audioId}: ${step} ${progress}%`);
  } catch (error) {
    console.error(`Failed to store progress for audio ${audioId}:`, error);
  }
}
