import fs from 'fs';
import https from 'https';
import { Server } from 'node:http';
import express, { Express } from 'express';

/**
 * Checks if mTLS is enabled based on environment variables
 * 
 * @returns Boolean indicating if mTLS is enabled
 */
export function isMtlsEnabled(): boolean {
  return process.env.MTLS_ENABLED === 'true';
}

/**
 * Gets mTLS configuration from environment variables
 * 
 * @returns mTLS configuration object or null if not enabled
 */
export function getMtlsConfig(): {
  caCert: string;
  serverCert: string;
  serverKey: string;
} | null {
  if (!isMtlsEnabled()) {
    return null;
  }

  const caCert = process.env.MTLS_CA_CERT;
  const serverCert = process.env.MTLS_SERVER_CERT;
  const serverKey = process.env.MTLS_SERVER_KEY;

  if (!caCert || !serverCert || !serverKey) {
    throw new Error('mTLS is enabled but certificate paths are not properly configured');
  }

  return {
    caCert,
    serverCert,
    serverKey,
  };
}

/**
 * Creates an HTTPS server with mTLS if enabled, or an HTTP server if not
 * 
 * @param app Express application
 * @returns Server instance
 */
export function createServer(app: Express): Server {
  const mtlsConfig = getMtlsConfig();

  if (mtlsConfig) {
    console.log('✅ Creating HTTPS server with mTLS');
    
    // Read certificate files
    const ca = fs.readFileSync(mtlsConfig.caCert);
    const cert = fs.readFileSync(mtlsConfig.serverCert);
    const key = fs.readFileSync(mtlsConfig.serverKey);

    // Create HTTPS server with mTLS configuration
    return https.createServer({
      ca,
      cert,
      key,
      requestCert: true,
      rejectUnauthorized: true,
    }, app);
  }

  console.log('ℹ️ Creating HTTP server (mTLS disabled)');
  return new Server(app);
}

/**
 * Middleware to verify client certificates
 */
export function verifyClientCert(req: express.Request, res: express.Response, next: express.NextFunction) {
  // Skip verification if mTLS is not enabled
  if (!isMtlsEnabled()) {
    return next();
  }

  // Skip verification for health check endpoints
  if (req.path === '/health' || req.path === '/readiness') {
    return next();
  }

  const clientCert = (req as any).socket?.getPeerCertificate?.();
  
  if (!clientCert || Object.keys(clientCert).length === 0) {
    return res.status(401).json({ error: 'Client certificate required' });
  }

  // Additional certificate validation could be done here
  
  next();
}
