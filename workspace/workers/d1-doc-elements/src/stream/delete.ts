import { Context } from "hono";

import { ensureTableExists } from "./util";


export async function deleteChunk(c: Context<any>){
  console.log(`🚦 Handling request for DELETE /api/delete`);

  try {
    const requestBody = await c.req.json();
    const { id, whitelabelId } = requestBody;

    if(!id || !whitelabelId) {
      return c.json("Missing required fields: id and whitelabelId", 400);
    }

    const tableName = await ensureTableExists(c, whitelabelId);

    // Delete logic
    const deleteQuery = `DELETE FROM ${tableName} WHERE id = ?`;
    await c.env.DB.prepare(deleteQuery).bind(id).run();

    return c.json("🗑️ Record deleted", 200);
  } catch (error) {
    console.error("Error in deleteChunk:", error);
    return c.json({
      success: false,
      error: `Failed to delete chunk: ${error.message}`
    }, 400);
  }
}

export async function deleteChunkWithIds(c: Context<any>){
  console.log(`🚦 Handling request for DELETE /api/delete`);

  try {
    const requestBody = await c.req.json();

    if(!requestBody.ids || !requestBody.whitelabelId) {
      return c.json("Missing required fields: ids and whitelabelId", 400);
    }

    const { ids, whitelabelId } = requestBody;

    if(!Array.isArray(ids)) {
      return c.json("Expected Array List for ids", 400);
    }

    if(ids.length === 0) {
      return c.json("🗑️ No Items deleted", 200);
    }

    const tableName = await ensureTableExists(c, whitelabelId);

    if(typeof ids[0] !== "string") {
      return c.json("Expected String at index 0", 400);
    }

    let idList = `('${ids[0].replaceAll("'", "\\'")}'`;

    for(let i = 1; i < ids.length; i++) {
      if(typeof ids[i] !== "string") {
        return c.json(`Expected String at index ${i}`, 400);
      }
      idList += `,'${ids[i].replaceAll("'", "\\'")}'`;
    }

    idList += ")";

    const deleteQuery = `DELETE FROM ${tableName} WHERE id IN ${idList}`;
    await c.env.DB.prepare(deleteQuery).run();

    return c.json("🗑️ Records deleted", 200);
  } catch (error) {
    console.error("Error in deleteChunkWithIds:", error);
    return c.json({
      success: false,
      error: `Failed to delete chunks: ${error.message}`
    }, 400);
  }
}


export async function deleteChunks(c: Context<any>){
  console.log(`🚦 Handling request for DELETE /api/delete`);

  try {
    const requestBody = await c.req.json();
    const { fileId, length, whitelabelId } = requestBody;

    if(!fileId || !length || !whitelabelId) {
      return c.json("Missing required fields: fileId, length, and whitelabelId", 400);
    }

    const tableName = await ensureTableExists(c, whitelabelId);

    let idList = "(";

    for(let i = 0; i < length; i++) {
      if(i > 0) { idList += ","; }
      idList += `'${fileId}-${i}'`;
    }

    idList += ")";

    const deleteQuery = `DELETE FROM ${tableName} WHERE id IN ${idList}`;
    await c.env.DB.prepare(deleteQuery).run();

    return c.json("🗑️ Records deleted", 200);
  } catch (error) {
    console.error("Error in deleteChunks:", error);
    return c.json({
      success: false,
      error: `Failed to delete chunks: ${error.message}`
    }, 400);
  }
}