
import { Hono, Context } from 'hono';

export function getTableName(whitelabelId: string): string {
  return `d1_chunks_${whitelabelId.replace(/-/g, '_')}`;
}

export async function ensureTableExists(c: Context<any>, whitelabelId: string): Promise<string> {
  const tableName = getTableName(whitelabelId);

  try {
    // Add indexes for better query performance
    const statements = [
      `CREATE TABLE IF NOT EXISTS ${tableName} (
        id TEXT PRIMARY KEY,
        text TEXT NOT NULL,
        metadata TEXT
      )`,
      `CREATE INDEX IF NOT EXISTS idx_${tableName}_metadata ON ${tableName}((json_extract(metadata, '$.fileObjectKey')))`,
    ];
    
    for (const sql of statements) {
      try {
        await c.env.DB.prepare(sql).run();
      } catch (error) {
        console.error(`❌ Error executing SQL: ${sql}`, error);
        throw error;
      }
    }
    
    return tableName;
  } catch (error) {
    console.error('❌ Error creating table:', error);
    throw error;
  }
}

// Remove the setupD1Routes function as we're not using database-per-whitelabel anymore

export const unauthorized403Message = "🚫 403 - Unauthorized access";

export function validateRequest(c: Context<any>) {
  console.log("🆔 c.env.ENVIRONMENT: ", c.env.ENVIRONMENT);
  console.log("🆔 c.env.DB:", c.env.DB);
  console.log("🆔 c.req.raw.headers: ", c.req.raw.headers);

  // Additional validation for development environment
  if (extraDevCheck(c) === false) return;

  // Use a default value if the header is not present
  // const origin = c.req.raw.headers.get('Origin') ?? (c.env.ENVIRONMENT === "dev" ? wranglerLocalIp : '');
  // const ip = c.req.raw.headers.get('cf-connecting-ip') ?? (c.env.ENVIRONMENT === "dev" ? '2a09:bac1:76a0:1a98::26b:52' : '');
  // console.log("🆔 c.req.raw.headers.get('Origin'): ", origin);
  // console.log("🆔 c.req.raw.headers.get('cf-connecting-ip'): ", ip);

  // console.log("🆔 allowedDomains.includes(origin): ", allowedDomains.includes(origin));
  // console.log("🆔 allowedIPs.includes(ip): ", allowedIPs.includes(ip));

  return true;
  // return allowedDomains.includes(origin) && allowedIPs.includes(ip);
}

function extraDevCheck(c: Context<any>) {
  // Additional validation for development environment
  if (c.env.ENVIRONMENT === 'dev') {
    const devHeader = c.req.raw.headers.get('Cloudflare-Worker-X-Dev-Auth');

    console.log(`🎟️ c.env.CLOUDFLARE_WORKER_X_AUTH_DEV: `, c.env.CLOUDFLARE_WORKER_X_AUTH_DEV);
    console.log(`🎟️ dev header: `, devHeader);

    if (devHeader !== c.env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
      console.log(`🙅🏻‍♂️ devHeader !== c.env.CLOUDFLARE_WORKER_X_AUTH_DEV`);
      return false;
    }
  }
}
