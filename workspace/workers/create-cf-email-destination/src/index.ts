interface ApiResponse {
	success: boolean;
	result: { email: string; verified: boolean }[];
	errors?: any[]; // Optional errors property to handle API error
}
import 'dotenv/config';

// Replace 'YOUR_ACCOUNT_IDENTIFIER' with your actual account identifier.
// Replace 'YOUR_API_EMAIL' and 'YOUR_API_KEY' with your actual Cloudflare API credentials.

async function isEmailVerified(env: any, senderEmail: string) {
	console.log('☁️🟠 isEmailVerified  env.CLOUDFLARE_ACCOUNT_ID:', env.CLOUDFLARE_ACCOUNT_ID);
	console.log('☁️🟠 isEmailVerified  env.CLOUDFLARE_EMAIL:', env.CLOUDFLARE_EMAIL);
	console.log('☁️🟠 isEmailVerified env.CLOUDFLARE_API_KEY:', env.CLOUDFLARE_API_KEY);

	const url = `https://api.cloudflare.com/client/v4/accounts/${env.CLOUDFLARE_ACCOUNT_ID}/email/routing/addresses`;
	const options = {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			'X-Auth-Email': env.CLOUDFLARE_EMAIL,
			'X-Auth-Key': env.CLOUDFLARE_API_KEY,
		},
		keepalive: true,
	};

	try {
		console.log('☁️🟠 isEmailVerified url:', url);
		console.log('☁️🟠 isEmailVerified options:', options);
		const response = await fetch(url, options);
		console.log('☁️🟠 isEmailVerified response:', response);
		const data = (await response.json()) as ApiResponse;
		console.log('☁️🟠 isEmailVerified data:', data);
		if (data.success && Array.isArray(data.result)) {
			return data.result.some((address) => address.email === senderEmail && address.verified);
		} else {
			console.error('☁️🟠❌ Failed to fetch verified emails: ', data.errors);
			return false;
		}
	} catch (error) {
		console.error('Error fetching email verification status:', error);
		return false;
	}
}

async function createDestinationAddress(env: any, emailAddress: string) {
	console.log('☁️🟠 createDestinationAddress env.CLOUDFLARE_ACCOUNT_ID:', env.CLOUDFLARE_ACCOUNT_ID);
	console.log('☁️🟠 createDestinationAddress env.CLOUDFLARE_EMAIL:', env.CLOUDFLARE_EMAIL);
	console.log('☁️🟠 createDestinationAddress env.CLOUDFLARE_API_KEY:', env.CLOUDFLARE_API_KEY);
	const url = `https://api.cloudflare.com/client/v4/accounts/${env.CLOUDFLARE_ACCOUNT_ID}/email/routing/addresses`;
	const options = {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'X-Auth-Email': env.CLOUDFLARE_EMAIL,
			'X-Auth-Key': env.CLOUDFLARE_API_KEY,
		},
		body: JSON.stringify({ email: emailAddress }),
	};

	try {
		console.log('☁️🟠 createDestinationAddress url:', url);
		console.log('☁️🟠 createDestinationAddress options:', options);
		const response = await fetch(url, options);
		console.log('☁️🟠 createDestinationAddress response:', response);
		return response.json();
	} catch (error) {
		console.error('❌ Error creating destination address: ', error);
		return { success: false, error: '❌ Failed to create destination address' };
	}
}

async function createOrResendVerification(env: any, email: string) {
	console.log('☁️🟠 createOrResendVerification email: ', email);
	const verified = await isEmailVerified(env, email);
	console.log('☁️🟠 createOrResendVerification verified: ', verified);
	if (!verified) {
		await createDestinationAddress(env, email);
		// Return a Response object with the JSON body
		return new Response(
			JSON.stringify({
				success: true,
				message: '📧 Email verification needed.',
			}),
			{
				headers: {
					'Content-Type': 'application/json',
					'Access-Control-Allow-Origin': env.ALLOW_ORIGIN,
					'Access-Control-Allow-Methods': 'POST, OPTIONS',
				},
				status: 200, // OK status
			},
		);
	}
	// Return a Response object with the JSON body
	return new Response(
		JSON.stringify({
			success: true,
			message: '🟢 Email already verified.',
		}),
		{
			headers: {
				'Content-Type': 'application/json',
				'Access-Control-Allow-Origin': env.ALLOW_ORIGIN,
				'Access-Control-Allow-Methods': 'POST, OPTIONS',
			},
			status: 200, // OK status
		},
	);
}

// async function handleOptionsRequest(env: any) {
//   // Check for OPTIONS request (CORS preflight)
//   console.log(`🎟️🌎 OPTIONS env.ALLOW_ORIGIN: `, env.ALLOW_ORIGIN);

//   // Handle CORS preflight request
//   return new Response(null, {
//     headers: {
//       "Access-Control-Allow-Origin": env.ALLOW_ORIGIN,
//       "Access-Control-Allow-Methods": "POST, OPTIONS",
//       "Access-Control-Allow-Headers": "Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization",
//       "Access-Control-Request-Headers": "Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization",
//       // "Access-Control-Max-Age": "86400", // Optional: tells the browser to cache preflight request for 1 day
//     },
//   });
// }

export default {
	async fetch(request: Request, env: any) {
		function secretHeaderCheck() {
			// Additional validation for development environment
			if (env.ENVIRONMENT === 'dev') {
				const devHeader = request.headers.get('Cloudflare-Worker-X-Dev-Auth');

				console.log(`🎟️🌎 env.ENVIRONMENT: `, env.ENVIRONMENT);
				console.log(`🎟️🙋🏻‍♂️ request.headers: `, ...request.headers);
				console.log(`🎟️ env.CLOUDFLARE_WORKER_X_AUTH_DEV: `, env.CLOUDFLARE_WORKER_X_AUTH_DEV);
				console.log(`🎟️ dev header: `, devHeader);

				if (devHeader !== env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
					console.log(`🙅🏻‍♂️ devHeader !== c.env.CLOUDFLARE_WORKER_X_AUTH_DEV`);
					return false;
				}
				return true;
			}
		}
		// Check for OPTIONS request (CORS preflight)
		if (request.method === 'OPTIONS') {
			// await handleOptionsRequest(env);
			// Check for OPTIONS request (CORS preflight)
			console.log(`🎟️🌎 OPTIONS env.ALLOW_ORIGIN: `, env.ALLOW_ORIGIN);

			// Handle CORS preflight request
			return new Response(null, {
				headers: {
					'Access-Control-Allow-Origin': env.ALLOW_ORIGIN,
					'Access-Control-Allow-Methods': 'POST, OPTIONS',
					'Access-Control-Allow-Headers': 'Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization',
					'Access-Control-Request-Headers': 'Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization',
					// "Access-Control-Max-Age": "86400", // Optional: tells the browser to cache preflight request for 1 day
				},
			});
		}

		if (request.method === 'GET') {
			console.error('☁️🟠 create-cf-email-destination GET  env:', env);
			console.error('☁️🟠 create-cf-email-destination GET request:', request);
			return new Response('🙅🏻‍♂️ Method Not Allowed', { status: 405 });
		}

		if (request.method === 'POST') {
			console.log(`🎟️🌎 create-cf-email-destination POST request: `, request);
			console.log(`🎟️🌎 create-cf-email-destination POST env: `, env);
			if (secretHeaderCheck()) {
				const requestBody: any = await request.json();
				const email = requestBody.email;
				return createOrResendVerification(env, email);
			} else {
				return new Response('🛑 Unauthorized', { status: 401 });
			}
		}
	},
};
