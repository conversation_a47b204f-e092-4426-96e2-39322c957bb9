// ☁️🟠 Cloudflare Email Worker Docs:
// ☁️🟠 https://developers.cloudflare.com/email-routing/email-workers/send-email-workers/

import { EmailMessage } from "cloudflare:email"
import { createMimeMessage } from "mimetext";

// This function generates the HTML content for the email based on the fetch response
function generateEmailHtml(
  user: any, 
  message: any,
  whiteLabelId: string,
) {
  const { 
    name, 
    userPicture, 
    content, 
    timestamp, 
    _id,
  } = message;
  const {
    nickname,
  } = user;

  // Format the timestamp into a human-readable format
  const date = new Date(timestamp).toLocaleDateString("en-US", {
    year: 'numeric', 
    month: 'long', 
    day: 'numeric', 
    hour: '2-digit', 
    minute: '2-digit',
  });

  return `
    <!DOCTYPE html>
    <html>
    <head>
    <title>Service Inquiry</title>
    <style type="text/css">
      br {
        display: block;
        width: 100%;
        height: 33px;
        padding: 33px;
      }
      .container {
        width: 55%;
        padding: 33px 0;
        text-align: center;
        margin: 12px auto;
        border: 2px solid gainsboro;
        border-radius: 8px; /* Rounded corners for the card style */
      }
      
      .email-container {
        font-family: Arial, sans-serif;
        max-width: 600px;
        margin: auto;
        text-align: center; /* Center the content */
        padding-bottom: 20px; /* Padding at the bottom of the container */
      }
      .chat-bubble-link {
        text-decoration: none;
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .chat-bubble {
        background-color: white; /* Color of the chat bubble */
        border-radius: 20px; /* Rounded corners */
        display: inline-block; /* Necessary for width and center alignment */
        max-width: calc(100% - 40px); /* Max width with padding */
        margin: 10px 0; /* Top and bottom margin */
        text-align: left; /* Align text to the left within the bubble */
        min-width: 250px;
        position: relative;
        border: 1px solid #e8e8e8;
      }
      .chat-bubble:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 20px;
        border-width: 10px 20px 10px 0; /* Arrow size */
        border-style: solid;
        border-color: transparent #f0f0f0; /* Bubble color */
        display: block;
        width: 0;
        z-index: 0;
      }
      .chat-header {
        height: 35px;
        border-top-left-radius: 17px;
        border-top-right-radius: 17px;
      }
      .chat-footer {
        border-bottom-left-radius: 17px;
        border-bottom-right-radius: 17px;
        display: flex;
        justify-content: end;
      }
      .chat-header, .chat-footer {
        height: 35px;
        background-color: #E8E8E8; /* Color of the chat bubble */
      }
      .sender-info {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
      .sender-avatar {
        width: 50px;
        height: 50px;
        background-image: url("${userPicture}");
        background-size: cover;
        border-radius: 50%;
        margin-left: 5px;
      }
      .sender-name {
        text-align: right;
        font-weight: bold;
        margin-right: 10px;
        width: 74%;
        margin-top: 10px;
        color: #4c4c4c;
      }
      .timestamp {
        font-size: 0.8em;
        color: #555;
        padding: 13px 11px 0 0;
        text-align: right;
        width: 100%;
      }
      .chat-bubble-content {
        text-align: right;
        padding: 10px;
        font-size: 1em;
        color: #333; /* Darker text color for content */
      }
      .logo-container {
        text-align: center; /* Center the logo container */
        margin: 20px 0; /* Add some margin around the logo */
      }
      .logo {
        width: 100px; /* Width of the logo */
        height: 100px; /* Height of the logo, make sure it's a circle */
        background-image: url('https://divinci.app/images/divinci_logo.png');
        background-size: cover; /* Cover the entire area of the container */
        border-radius: 50%; /* Make it round */
        display: inline-block; /* Needed to apply text-align center */
        margin-bottom: 20px; /* Space before the message bubble */
        border: 5px solid rgb(0, 0, 93);
      }
      .button {
        background-color: #007bff; /* Blue background */
        color: white; /* White text */
        padding: 10px 20px; /* Padding inside the button */
        text-decoration: none; /* No underline on the text */
        border-radius: 5px; /* Rounded corners */
        display: inline-block; /* Allow width and margin */
        margin: 20px 0; /* Space above and below the button */
        font-weight: bold; /* Make text bold */
      }
      .button a {
        color: white;
      }
      .call-to-action {
        width: calc(100% - 40px); /* Subtracting the horizontal paddings */
        margin: 0 auto 20px auto;
        text-align: center; /* Center the button */
      }
    </style>
    </head>
    <body>
      <div class="container">
      <div class="email-container">
        <div class="logo-container">
          <div class="logo"></div>
        </div>
        <div class="header">
          <h2> Divinci AI <h2>
          <h3> ✋🙂 ${nickname} has raised their hand! </h3>
        </div>

        <a href="http://localhost:8080/white-label/${whiteLabelId}#message-${_id}" class="chat-bubble-link">
          <div class="chat-bubble">
            <div class="chat-header">
              <div class="sender-info">
                <div class="timestamp">
                  ${date}
                </div>
              </div>
            </div>
            <div class="chat-bubble-content">
              ${content}
            </div>
            <div class="chat-footer">
              <div class="sender-name">
                ${nickname}
              </div>
              <div class="sender-avatar"></div>
            </div>
          </div>
        </a>

        <br/>

        <table class="call-to-action" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
          <tr>
            <td style="text-align: center;">
              <a href="http://localhost:8080/white-label/${whiteLabelId}#message-${_id}" class="button" style="color: white">
                Respond to mike3
              </a>
            </td>
          </tr>
        </table>
        </div>
      </div>
    </body>
    </html>
  `;
}


export default {
  async fetch(request: Request, env: any) {
    function secretHeaderCheck(){
      // Additional validation for development environment
      if (env.ENVIRONMENT === 'dev') {
        const devHeader = request.headers.get('Cloudflare-Worker-X-Dev-Auth');
    
        console.log(`🎟️🌎 env.ENVIRONMENT: `, env.ENVIRONMENT);
        console.log(`🎟️🙋🏻‍♂️ request.headers: `, ...request.headers);
        console.log(`🎟️ env.CLOUDFLARE_WORKER_X_AUTH_DEV: `, env.CLOUDFLARE_WORKER_X_AUTH_DEV);
        console.log(`🎟️ dev header: `, devHeader);
        
        if (devHeader !== env.CLOUDFLARE_WORKER_X_AUTH_DEV) {
          console.log(`🙅🏻‍♂️ devHeader !== c.env.CLOUDFLARE_WORKER_X_AUTH_DEV`);
          return false;
        }
        return true;
      }
    }
    // Check for OPTIONS request (CORS preflight)
    if (request.method === "OPTIONS") {
      console.log(`🎟️🌎 OPTIONS env.ALLOW_ORIGIN: `, env.ALLOW_ORIGIN);

      // Handle CORS preflight request
      return new Response(null, {
        headers: {
          "Access-Control-Allow-Origin": env.ALLOW_ORIGIN, // Adjust this to match your front-end origin for better security
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization",
          "Access-Control-Request-Headers": "Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization",
          "Access-Control-Max-Age": "86400", // Optional: tells the browser to cache preflight request for 1 day
        },
      });
    }
    // Check for POST request
    if (request.method === "GET") {
      return new Response("🙅🏻‍♂️ Method Not Allowed", { status: 405 });
    }

    async function sendEmailWithRetry(
      emailMessage: EmailMessage, 
      env: any, 
      retries = 3,
    ) {
      try {
        await env.SEB.send(emailMessage);
      } catch (error) {
        console.error("❌ Failed to send email:", error);
        if (retries > 0) {
          console.log(`🔂 Retrying... attempts left: ${retries}`);
          await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds before retrying
          return sendEmailWithRetry(emailMessage, env, retries - 1);
        } else {
          throw error; // Rethrow error after all retries are exhausted
        }
      }
    }

    // Validate a specific header, e.g., Cloudflare-Worker-X-Dev-Auth
    if (request.method === "POST") {
      console.log('☁️🟠 EMAIL WORKER POST'); 
      if (secretHeaderCheck()) {
        console.log('☁️🟠 EMAIL WORKER POST'); 

        const requestBody: any = await request.json(); // Parse the request body
        const {
          emailEnabled, emails,
        } = requestBody.notificationDefaultSettings;

        // await createOrResendVerification(emails[0]);

        const { nickname } = requestBody.user;

        const emailHtmlContent = generateEmailHtml(
          requestBody.user,
          requestBody.whitelabelId, 
          requestBody.message,
        ); // Generate the HTML content for the email

        if (!emailEnabled) {
          return new Response(`🙅🏻‍♂️ emailEnabled: ${emailEnabled} `, { status: 500 });
        }
        if (emails.length === 0) {
          return new Response(`🙅🏻‍♂️ No default notifcation email found: ${emails} `, { status: 500 });
        }
    
        // Ensure the email service binding is correctly configured
        if (!env.SEB) {
          console.error("🛠️ Email service binding is not configured.");
          return new Response("🛠️ Email service is not configured", { status: 500 });
        }
    
        // Create the email message
        const msg = createMimeMessage();
        msg.setSender({ name: "Divinci AI", addr: "<EMAIL>" });
        msg.setRecipient(emails[0]);
        msg.setSubject(`✋🙂 ${nickname} has raised their hand! - Divinci AI `);
        msg.addMessage({
          contentType: 'text/html',
          data: emailHtmlContent,
        });
    
        // Construct the EmailMessage object
        const message = new EmailMessage(
          "<EMAIL>",
          emails[0],
          msg.asRaw(),
        );
    
        try {
          await sendEmailWithRetry(message, env);
          return new Response(JSON.stringify("🟢 Email sent successfully!"), {
            headers: {
              "Access-Control-Allow-Origin": env.ALLOW_ORIGIN,
              "Access-Control-Allow-Methods": "POST, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization",
            },
          });
        } catch (e) {
          console.error("❌ Failed to send email:", e);
          return new Response("❌ Failed to send email", { status: 500 });
        }
      } else {
        // Return a 401 Unauthorized response if the security header is missing
        return new Response("🙅🏻‍♂️ Unauthorized: Missing security header. ", { status: 401 });
      }
    }
  },
};