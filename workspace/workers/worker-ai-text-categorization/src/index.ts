// workspace/workers/worker-ai-text-categorization/src/index.ts
interface RequestBody {
	text: string;
}

export interface Env {
	AI: any;
}

const CATEGORIES = ['diagram/chart', 'text/chat/question', 'image', 'audio', 'video', 'unknown'];

export default {
	async fetch(request: Request, env: Env): Promise<Response> {
		try {
			const { text } = (await request.json()) as RequestBody;
			const categoriesList = CATEGORIES.map((category) => `"${category}"`).join(', ');
			const promptMessage = `Hello! Can you help detect the intent from the user's question and select which category it matches out of these possible categories: ${categoriesList}. Please provide how confident you are of the categorization as a float. Please only respond with the category and confidence in this JSON format: {"category": "{category}", "confidence": {confidence}}. Here is the text: "${text}"`;

			const response = await env.AI.run(
				// "@hf/nousresearch/hermes-2-pro-mistral-7b",
				'@hf/thebloke/mistral-7b-instruct-v0.1-awq',
				{
					messages: [{ role: 'user', content: promptMessage }],
					response_format: { type: 'json_object' },
				},
			);

			console.log('☁️🟠 CF AI response:', response);

			if (!response) {
				throw new Error('❌ Invalid AI response structure');
			}

			return new Response(JSON.stringify(response), {
				headers: { 'Content-Type': 'application/json' },
			});
		} catch (e) {
			const error = e as Error;
			console.error('❌ Error:', error.message);
			return new Response(`❌ Error: ${error.message}`, { status: 500 });
		}
	},
} satisfies ExportedHandler<Env>;
