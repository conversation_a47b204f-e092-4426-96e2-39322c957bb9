/**
 * Fixed storage client implementation for handling file storage operations
 * with support for both R2 and MinIO S3 interfaces.
 */

import { Env } from "../types";

/**
 * Interface for a unified storage client that works with both R2 and MinIO
 */
export interface StorageClient {
  get(key: string): Promise<R2ObjectBody | null>,
  put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object>,
  delete(key: string): Promise<void>,
  list(options?: R2ListOptions): Promise<R2Objects>,
}

/**
 * Creates an appropriate storage client based on the environment.
 * Uses MinIO client for local/dev environments and R2 client for production.
 */
export function createFixedStorageClient(env: Env): StorageClient {
  const isLocalEnvironment = env.ENVIRONMENT === "local" || env.ENVIRONMENT === "development";

  if (isLocalEnvironment) {
    console.log(`🔄 [FIXED-STORAGE] Using MinIO S3 client for local environment: ${env.ENVIRONMENT}`);
    console.log(`🔄 [FIXED-STORAGE] Storage URL: ${env.R2_BUCKET_URL}`);
    return new FixedMinioStorageClient(env);
  } else {
    console.log(`🔄 [FIXED-STORAGE] Using Cloudflare R2 client for environment: ${env.ENVIRONMENT}`);
    console.log(`🔄 [FIXED-STORAGE] Storage URL: ${env.R2_BUCKET_URL}`);
    return new FixedR2StorageClient(env);
  }
}

/**
 * R2 storage client implementation
 */
class FixedR2StorageClient implements StorageClient {
  constructor(private env: Env) {}

  async get(key: string): Promise<R2ObjectBody | null> {
    return this.env.R2.get(key);
  }

  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    return this.env.R2.put(key, value, options);
  }

  async delete(key: string): Promise<void> {
    await this.env.R2.delete(key);
  }

  async list(options?: R2ListOptions): Promise<R2Objects> {
    return this.env.R2.list(options);
  }
}

/**
 * MinIO S3 storage client implementation using direct fetch calls
 */
class FixedMinioStorageClient implements StorageClient {
  private baseUrl: string;
  private buckets: string[] = ["rag-origin-files-local", "rag-files-local"];
  private accessKey: string;
  private secretKey: string;

  constructor(private env: Env) {
    // Use the R2_BUCKET_URL from environment if available, or DNS name
    this.baseUrl = this.env.R2_BUCKET_URL || "http://minio.divinci.local:9000";
    this.accessKey = this.env.R2_ACCESS_KEY_ID || "minioadmin";
    this.secretKey = this.env.R2_SECRET_ACCESS_KEY || "minioadmin";

    console.log(`🔄 [FIXED-STORAGE] Using MinIO endpoint: ${this.baseUrl}`);
  }

  /**
   * Gets a file from any of the configured buckets
   * @param key The object key to get
   * @returns The file content as a Response if found, null otherwise
   */
  async get(key: string): Promise<R2ObjectBody | null> {
    console.log(`🔄 [FIXED-STORAGE] Getting object: ${key}`);

    // Extract the whitelabelId from the key if it exists
    let whitelabelId = '';
    if (key.includes('/')) {
      whitelabelId = key.split('/')[0];
    }

    // Paths to try
    const pathsToTry = [
      key, // Original key
    ];

    // If the key has a timestamp prefix, also try just the filename
    if (key.includes('T') && key.includes('Z_')) {
      const parts = key.split('Z_');
      if (parts.length > 1) {
        const filename = parts[1];
        pathsToTry.push(filename);
      }
    }

    // If the key has a whitelabelId prefix, also try just the timestamp_filename part
    if (key.includes('/')) {
      const timestampFilename = key.split('/').pop() || '';
      pathsToTry.push(timestampFilename);
    }

    // Try each bucket
    for (const bucket of this.buckets) {
      // Try each path in the bucket
      for (const path of pathsToTry) {
        try {
          console.log(`🔄 [FIXED-STORAGE] Trying bucket: ${bucket}, path: ${path}`);

          // URLs to try
          const urlsToTry = [
            `${this.baseUrl}/${bucket}/${path}`, // Direct path
          ];

          // If we have a whitelabelId, also try with the whitelabelId folder
          if (whitelabelId && !path.includes('/')) {
            urlsToTry.push(`${this.baseUrl}/${bucket}/${whitelabelId}/${path}`);
          }

          // Try each URL
          for (const url of urlsToTry) {
            console.log(`🔄 [FIXED-STORAGE] Trying URL: ${url}`);

            // Try to get the object
            const response = await fetch(url, {
              headers: {
                'Authorization': `Basic ${btoa(`${this.accessKey}:${this.secretKey}`)}`
              }
            });

            if (response.ok) {
              console.log(`✅ [FIXED-STORAGE] File found at URL: ${url}`);

              // Get the content type and other metadata
              const contentType = response.headers.get('Content-Type') || 'application/octet-stream';
              const contentLength = parseInt(response.headers.get('Content-Length') || '0', 10);
              const etag = response.headers.get('ETag') || '';
              const lastModified = response.headers.get('Last-Modified') || new Date().toUTCString();

              // Create a blob from the content
              const blob = new Blob([], { type: contentType });

              // Create a new R2ObjectBody from the response
              const r2Object = {
                body: response.body,
                bodyUsed: false,
                headers: response.headers,
                ok: true,
                redirected: false,
                status: 200,
                statusText: 'OK',
                type: 'basic',
                url: response.url,
                clone: function() { return this; },
                arrayBuffer: async function() { return new ArrayBuffer(0); },
                blob: async function() { return blob; },
                formData: async function() { return new FormData(); },
                json: async function() { return {}; },
                text: async function() { return ''; },
                size: contentLength,
                key: key,
                httpMetadata: {
                  contentType: contentType
                },
                customMetadata: {}
              } as unknown as R2ObjectBody;

              return r2Object;
            }
          }
        } catch (error: any) {
          console.log(`⚠️ [FIXED-STORAGE] Error checking path ${path} in bucket ${bucket}: ${error.message}`);
        }
      }
    }

    console.log(`❌ [FIXED-STORAGE] File not found in any bucket: ${key}`);
    return null;
  }

  /**
   * Stub implementation for put
   */
  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    console.log(`🔄 [FIXED-STORAGE] Putting object: ${key}`);

    // Create a dummy R2Object to return
    return {
      key,
      version: "",
      size: 0,
      etag: "",
      httpEtag: "",
      uploaded: new Date(),
      httpMetadata: {
        contentType: options?.httpMetadata?.contentType || 'application/octet-stream'
      },
      customMetadata: options?.customMetadata || {},
    } as R2Object;
  }

  /**
   * Stub implementation for delete
   */
  async delete(key: string): Promise<void> {
    console.log(`🔄 [FIXED-STORAGE] Deleting object: ${key}`);
    // No-op
  }

  /**
   * Stub implementation for list
   */
  async list(options?: R2ListOptions): Promise<R2Objects> {
    console.log(`🔄 [FIXED-STORAGE] Listing objects`);

    // Return an empty list
    return {
      objects: [],
      truncated: false,
      delimitedPrefixes: []
    } as R2Objects;
  }
}
