/**
 * A simple storage client implementation that uses direct fetch calls
 * to access files in MinIO buckets.
 */

import { Env } from "../types";

/**
 * Interface for a simplified storage client
 */
export interface SimpleStorageClient {
  get(key: string): Promise<Response | null>;
  exists(key: string): Promise<boolean>;
}

/**
 * Creates a simple storage client for accessing files in MinIO buckets
 */
export function createSimpleStorageClient(env: Env): SimpleStorageClient {
  return new MinioSimpleStorageClient(env);
}

/**
 * MinIO simple storage client implementation using direct fetch calls
 */
class MinioSimpleStorageClient implements SimpleStorageClient {
  private baseUrl: string;
  private buckets: string[] = ["rag-origin-files-local", "rag-files-local"];
  private accessKey: string;
  private secretKey: string;

  constructor(private env: Env) {
    // Use the R2_BUCKET_URL from environment if available, or DNS name
    this.baseUrl = this.env.R2_BUCKET_URL || "http://minio.divinci.local:9000";
    this.accessKey = this.env.R2_ACCESS_KEY_ID || "minioadmin";
    this.secretKey = this.env.R2_SECRET_ACCESS_KEY || "minioadmin";

    console.log(`🔄 [SIMPLE-STORAGE] Using MinIO endpoint: ${this.baseUrl}`);
  }

  /**
   * Gets a file from any of the configured buckets
   * @param key The object key to get
   * @returns The file content as a Response if found, null otherwise
   */
  async get(key: string): Promise<Response | null> {
    console.log(`🔄 [SIMPLE-STORAGE] Getting object: ${key}`);

    // Try each bucket
    for (const bucket of this.buckets) {
      try {
        console.log(`🔄 [SIMPLE-STORAGE] Trying bucket: ${bucket}`);
        
        // Create the URL for the file
        const url = `${this.baseUrl}/${bucket}/${key}`;
        
        // Try to get the object
        const response = await fetch(url, {
          headers: {
            'Authorization': `Basic ${btoa(`${this.accessKey}:${this.secretKey}`)}`
          }
        });
        
        if (response.ok) {
          console.log(`✅ [SIMPLE-STORAGE] File found in bucket: ${bucket}`);
          return response;
        }
      } catch (error: any) {
        console.log(`⚠️ [SIMPLE-STORAGE] Error checking bucket ${bucket}: ${error.message}`);
      }
    }
    
    console.log(`❌ [SIMPLE-STORAGE] File not found in any bucket: ${key}`);
    return null;
  }

  /**
   * Checks if a file exists in any of the configured buckets
   * @param key The object key to check
   * @returns True if the file exists, false otherwise
   */
  async exists(key: string): Promise<boolean> {
    console.log(`🔄 [SIMPLE-STORAGE] Checking if object exists: ${key}`);

    // Try each bucket
    for (const bucket of this.buckets) {
      try {
        console.log(`🔄 [SIMPLE-STORAGE] Trying bucket: ${bucket}`);
        
        // Create the URL for the file
        const url = `${this.baseUrl}/${bucket}/${key}`;
        
        // Try to get the object with a HEAD request
        const response = await fetch(url, {
          method: 'HEAD',
          headers: {
            'Authorization': `Basic ${btoa(`${this.accessKey}:${this.secretKey}`)}`
          }
        });
        
        if (response.ok) {
          console.log(`✅ [SIMPLE-STORAGE] File exists in bucket: ${bucket}`);
          return true;
        }
      } catch (error: any) {
        console.log(`⚠️ [SIMPLE-STORAGE] Error checking bucket ${bucket}: ${error.message}`);
      }
    }
    
    console.log(`❌ [SIMPLE-STORAGE] File does not exist in any bucket: ${key}`);
    return false;
  }
}
