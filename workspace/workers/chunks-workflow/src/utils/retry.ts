/**
 * Options for the retry function
 */
export interface RetryOptions {
  /** Maximum number of attempts (including the initial attempt) */
  maxAttempts: number;
  /** Delay in milliseconds between retries */
  delayMs: number;
  /** Factor to multiply delay by after each retry (for exponential backoff) */
  backoffFactor: number;
  /** Optional predicate to determine if an error is retryable */
  isRetryable?: (error: Error) => boolean;
}

/**
 * Default retry options
 */
export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  delayMs: 1000,
  backoffFactor: 2,
  isRetryable: () => true
};

/**
 * Retry a function with exponential backoff
 * 
 * @param fn Function to retry
 * @param args Arguments to pass to the function
 * @param options Retry options
 * @returns Promise that resolves with the function's result or rejects with the last error
 */
export async function retry<T>(
  fn: (...args: any[]) => Promise<T>,
  args: any[] = [],
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const retryOptions: RetryOptions = {
    ...DEFAULT_RETRY_OPTIONS,
    ...options
  };

  let lastError: Error;
  let currentDelay = retryOptions.delayMs;

  for (let attempt = 1; attempt <= retryOptions.maxAttempts; attempt++) {
    try {
      return await fn(...args);
    } catch (error) {
      lastError = error as Error;

      // Check if error is retryable
      if (retryOptions.isRetryable && !retryOptions.isRetryable(lastError)) {
        throw lastError;
      }

      // If this was the last attempt, throw the error
      if (attempt === retryOptions.maxAttempts) {
        throw lastError;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, currentDelay));

      // Increase delay for next retry (exponential backoff)
      currentDelay *= retryOptions.backoffFactor;
    }
  }

  // This should never happen, but TypeScript needs it
  throw new Error('Unexpected error in retry logic');
}

/**
 * Determines if an error is a transient error that should be retried
 * 
 * @param error Error to check
 * @returns True if the error is retryable, false otherwise
 */
export function isTransientError(error: Error): boolean {
  // Network errors
  if (
    error.message.includes('network') ||
    error.message.includes('connection') ||
    error.message.includes('timeout') ||
    error.message.includes('ECONNRESET') ||
    error.message.includes('ETIMEDOUT')
  ) {
    return true;
  }

  // Check for HTTP status codes that indicate transient errors
  if (error instanceof Response || (error as any).status) {
    const status = (error as any).status;
    // 429 (Too Many Requests), 5xx (Server Errors)
    return status === 429 || (status >= 500 && status < 600);
  }

  // Session expired errors
  if (
    error.message.includes('session expired') ||
    error.message.includes('Session not found')
  ) {
    return true;
  }

  return false;
}

/**
 * Creates a retryable version of a function
 * 
 * @param fn Function to make retryable
 * @param options Retry options
 * @returns A new function that will retry the original function
 */
export function createRetryableFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: Partial<RetryOptions> = {}
): T {
  return ((...args: Parameters<T>) => {
    return retry(fn, args, options);
  }) as T;
}
