/**
 * Simplified storage client that uses DNS name for MinIO access
 */

import { Env } from "../types";
import { generateAuthorizationHeader } from "./aws-sig-v4";

export interface StorageClient {
  get(key: string): Promise<R2ObjectBody | null>,
  put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object>,
  delete(key: string): Promise<void>,
  list(options?: R2ListOptions): Promise<R2Objects>
}

export function createStorageClient(env: Env): StorageClient {
  const isLocalEnvironment = env.ENVIRONMENT === "local" || env.ENVIRONMENT === "development";

  if (isLocalEnvironment) {
    console.log(`🔄 [STORAGE] Using MinIO client for local env: ${env.ENVIRONMENT}`);
    return new MinioStorageClient(env);
  } else {
    console.log(`🔄 [STORAGE] Using R2 client for env: ${env.ENVIRONMENT}`);
    return new R2StorageClient(env);
  }
}

class R2StorageClient implements StorageClient {
  constructor(private env: Env) {}

  async get(key: string): Promise<R2ObjectBody | null> {
    return this.env.R2.get(key);
  }

  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    return this.env.R2.put(key, value, options);
  }

  async delete(key: string): Promise<void> {
    await this.env.R2.delete(key);
  }

  async list(options?: R2ListOptions): Promise<R2Objects> {
    return this.env.R2.list(options);
  }
}

class MinioStorageClient implements StorageClient {
  private baseUrl: string;
  private bucketName: string = "rag-origin-files-local";
  private accessKey: string;
  private secretKey: string;
  private maxRetries: number = 5;
  private retryDelayMs: number = 1000;

  constructor(private env: Env) {
    // Use the R2_BUCKET_URL from environment if available, or DNS name
    this.baseUrl = this.env.R2_BUCKET_URL || "http://minio.divinci.local:9000";

    console.log(`🔄 [STORAGE] Using MinIO endpoint: ${this.baseUrl}`);

    this.accessKey = this.env.R2_ACCESS_KEY_ID || "minioadmin";
    this.secretKey = this.env.R2_SECRET_ACCESS_KEY || "minioadmin";

    // Check bucket on startup
    this.checkOrCreateBucket()
      .then(exists => console.log(`🔄 [STORAGE] Bucket check: ${exists ? "exists" : "created"}`))
      .catch(err => console.warn(`⚠️ [STORAGE] Bucket check failed: ${err.message}`));
  }

  private async checkOrCreateBucket(): Promise<boolean> {
    try {
      // Check if bucket exists
      const headUrl = new URL(`${this.baseUrl}/${this.bucketName}`);
      const headHeaders = await generateAuthorizationHeader(
        "HEAD",
        headUrl,
        this.accessKey,
        this.secretKey,
        null,
        ""
      );

      const response = await fetch(headUrl.toString(), {
        method: 'HEAD',
        headers: headHeaders,
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        return true; // Bucket exists
      }

      if (response.status === 404) {
        // Try to create bucket
        const putUrl = new URL(`${this.baseUrl}/${this.bucketName}`);
        const putHeaders = await generateAuthorizationHeader(
          "PUT",
          putUrl,
          this.accessKey,
          this.secretKey,
          null,
          ""
        );

        const createResponse = await fetch(putUrl.toString(), {
          method: 'PUT',
          headers: putHeaders,
          signal: AbortSignal.timeout(10000)
        });

        if (createResponse.ok || createResponse.status === 409) {
          return true; // Created or already exists
        }
      }

      return false;
    } catch (error) {
      console.warn(`⚠️ [STORAGE] Bucket check/create error: ${error.message}`);
      return false;
    }
  }

  private async withRetry<T>(
    operation: string,
    fn: () => Promise<T>,
    retries: number = this.maxRetries
  ): Promise<T> {
    let lastError = null;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔄 [STORAGE] Attempt ${attempt}/${retries}: ${operation}`);
        const result = await fn();
        console.log(`✅ [STORAGE] Operation successful: ${operation}`);
        return result;
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ [STORAGE] Attempt ${attempt} failed: ${error.message}`);

        if (attempt < retries) {
          const delay = this.retryDelayMs * Math.pow(2, attempt - 1);
          console.log(`🔄 [STORAGE] Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error(`All ${retries} attempts failed`);
  }

  async get(key: string): Promise<R2ObjectBody | null> {
    try {
      console.log(`🔄 [STORAGE] Getting object ${key} from ${this.baseUrl}`);

      // Check if file exists
      const headUrl = new URL(`${this.baseUrl}/${this.bucketName}/${key}`);
      const headHeaders = await generateAuthorizationHeader(
        "HEAD",
        headUrl,
        this.accessKey,
        this.secretKey,
        null,
        ""
      );

      const headResponse = await this.withRetry(
        `Head check for ${key}`,
        async () => {
          const response = await fetch(headUrl.toString(), {
            method: 'HEAD',
            headers: headHeaders,
            signal: AbortSignal.timeout(5000)
          });

          if (response.status === 404) {
            return null;
          }

          if (!response.ok) {
            throw new Error(`HTTP error: ${response.status}`);
          }

          return response;
        }
      );

      if (!headResponse) {
        return null; // File not found
      }

      // Get metadata
      const contentType = headResponse.headers.get('Content-Type') || 'application/octet-stream';
      const contentLength = parseInt(headResponse.headers.get('Content-Length') || '0', 10);
      const etag = headResponse.headers.get('ETag') || '';

      // Get file content
      const getUrl = new URL(`${this.baseUrl}/${this.bucketName}/${key}`);
      const getHeaders = await generateAuthorizationHeader(
        "GET",
        getUrl,
        this.accessKey,
        this.secretKey,
        null,
        ""
      );

      const getResponse = await this.withRetry(
        `GET ${key}`,
        async () => {
          const response = await fetch(getUrl.toString(), {
            method: 'GET',
            headers: getHeaders,
            signal: AbortSignal.timeout(10000)
          });

          if (!response.ok) {
            throw new Error(`HTTP error: ${response.status}`);
          }

          return response;
        }
      );

      // Create response object
      const arrayBuffer = await getResponse.arrayBuffer();
      const blob = new Blob([arrayBuffer], { type: contentType });

      // Create R2ObjectBody-like object
      return {
        body: new ReadableStream({
          start(controller) {
            controller.enqueue(new Uint8Array(arrayBuffer));
            controller.close();
          }
        }),
        bodyUsed: false,
        arrayBuffer: async () => arrayBuffer,
        json: async () => JSON.parse(await blob.text()),
        text: async () => await blob.text(),
        blob: async () => blob,
        size: arrayBuffer.byteLength,
        etag: etag,
        httpMetadata: {
          contentType,
          contentLanguage: headResponse.headers.get('Content-Language'),
          contentDisposition: headResponse.headers.get('Content-Disposition'),
          contentEncoding: headResponse.headers.get('Content-Encoding'),
          cacheControl: headResponse.headers.get('Cache-Control'),
          cacheExpiry: undefined
        },
        customMetadata: {
          fileName: key.split('/').pop() || key
        },
        key: key,
        version: "",
        httpEtag: etag,
        uploaded: new Date(headResponse.headers.get('Last-Modified') || new Date().toISOString()),
        storageClass: "standard",
        writeHttpMetadata: () => ({}),
        checksums: {
          md5: undefined,
          sha1: undefined,
          sha256: undefined,
          sha384: undefined,
          sha512: undefined,
          toJSON: () => ({})
        }
      } as R2ObjectBody;
    } catch (error) {
      console.error(`❌ [STORAGE] Error getting object: ${error.message}`);
      return null;
    }
  }

  async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> {
    try {
      // Ensure bucket exists
      await this.checkOrCreateBucket();

      // Convert value to ArrayBuffer
      let data: ArrayBuffer;
      let contentType = options?.httpMetadata?.contentType || 'application/octet-stream';

      if (value instanceof Blob) {
        data = await value.arrayBuffer();
        if (value.type && contentType === 'application/octet-stream') {
          contentType = value.type;
        }
      } else if (value instanceof ReadableStream) {
        const reader = value.getReader();
        const chunks: Uint8Array[] = [];

        while (true) {
          const { done, value: chunk } = await reader.read();
          if (done) break;
          if (chunk) chunks.push(chunk);
        }

        if (chunks.length > 0) {
          const totalLength = chunks.reduce((len, chunk) => len + chunk.length, 0);
          const result = new Uint8Array(totalLength);
          let offset = 0;
          for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
          }
          data = result.buffer;
        } else {
          data = new ArrayBuffer(0);
        }
      } else if (value instanceof ArrayBuffer) {
        data = value;
      } else if (ArrayBuffer.isView(value)) {
        data = value.buffer;
      } else if (typeof value === 'string') {
        const encoder = new TextEncoder();
        data = encoder.encode(value).buffer;
        if (contentType === 'application/octet-stream') {
          contentType = 'text/plain';
        }
      } else if (value === null) {
        data = new ArrayBuffer(0);
      } else {
        throw new Error(`Unsupported value type: ${typeof value}`);
      }

      const contentLength = data.byteLength;
      console.log(`🔄 [STORAGE] Uploading: ${key} to ${this.baseUrl}, size: ${contentLength}, type: ${contentType}`);

      // Create URL and headers
      const putUrl = new URL(`${this.baseUrl}/${this.bucketName}/${key}`);
      const authHeaders = await generateAuthorizationHeader(
        "PUT",
        putUrl,
        this.accessKey,
        this.secretKey,
        data,
        contentType
      );

      // Add required headers
      const headers = {
        ...authHeaders,
        "Content-Type": contentType,
        "Content-Length": String(contentLength),
        "x-amz-acl": "public-read"
      };

      // Add metadata headers
      if (options?.customMetadata) {
        for (const [k, v] of Object.entries(options.customMetadata)) {
          headers[`x-amz-meta-${k}`] = String(v);
        }
      }

      if (options?.httpMetadata) {
        const meta = options.httpMetadata;
        if (meta.contentLanguage) headers["Content-Language"] = meta.contentLanguage;
        if (meta.contentDisposition) headers["Content-Disposition"] = meta.contentDisposition;
        if (meta.contentEncoding) headers["Content-Encoding"] = meta.contentEncoding;
        if (meta.cacheControl) headers["Cache-Control"] = meta.cacheControl;
      }

      // Upload with retry
      const response = await this.withRetry(
        `Upload ${key}`,
        async () => {
          const response = await fetch(putUrl.toString(), {
            method: 'PUT',
            headers,
            body: new Uint8Array(data),
            signal: AbortSignal.timeout(15000)
          });

          if (!response.ok) {
            const errText = await response.text().catch(() => `Status: ${response.status}`);
            throw new Error(`S3 PUT failed: ${errText}`);
          }

          return response;
        }
      );

      // Return R2Object-like response
      const etag = response.headers.get('ETag') || Date.now().toString();

      return {
        key,
        version: "",
        size: contentLength,
        etag,
        httpEtag: etag,
        uploaded: new Date(),
        httpMetadata: {
          contentType
        },
        customMetadata: options?.customMetadata || {},
        checksums: {
          md5: undefined,
          sha1: undefined,
          sha256: undefined,
          sha384: undefined,
          sha512: undefined
        }
      } as R2Object;
    } catch (error) {
      console.error(`❌ [STORAGE] Error putting object: ${error.message}`);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      // Check if file exists
      const headUrl = new URL(`${this.baseUrl}/${this.bucketName}/${key}`);
      const headHeaders = await generateAuthorizationHeader(
        "HEAD",
        headUrl,
        this.accessKey,
        this.secretKey,
        null,
        ""
      );

      const headResponse = await fetch(headUrl.toString(), {
        method: 'HEAD',
        headers: headHeaders,
        signal: AbortSignal.timeout(5000)
      });

      if (headResponse.status === 404) {
        return; // Nothing to delete
      }

      // Delete file
      const deleteUrl = new URL(`${this.baseUrl}/${this.bucketName}/${key}`);
      const deleteHeaders = await generateAuthorizationHeader(
        "DELETE",
        deleteUrl,
        this.accessKey,
        this.secretKey,
        null,
        ""
      );

      await this.withRetry(
        `Delete ${key}`,
        async () => {
          const response = await fetch(deleteUrl.toString(), {
            method: 'DELETE',
            headers: deleteHeaders,
            signal: AbortSignal.timeout(5000)
          });

          if (!response.ok && response.status !== 404) {
            throw new Error(`Delete failed: ${response.status}`);
          }

          return response;
        }
      );
    } catch (error) {
      console.warn(`⚠️ [STORAGE] Error deleting file: ${error.message}`);
      // Don't throw - deletes are not critical
    }
  }

  async list(options?: R2ListOptions): Promise<R2Objects> {
    try {
      // Create URL with query parameters
      const listUrl = new URL(`${this.baseUrl}/${this.bucketName}`);
      listUrl.searchParams.append('list-type', '2');

      if (options?.prefix) listUrl.searchParams.append('prefix', options.prefix);
      if (options?.delimiter) listUrl.searchParams.append('delimiter', options.delimiter);
      if (options?.cursor) listUrl.searchParams.append('continuation-token', options.cursor);
      if (options?.limit) listUrl.searchParams.append('max-keys', String(options.limit));

      // Generate headers
      const listHeaders = await generateAuthorizationHeader(
        "GET",
        listUrl,
        this.accessKey,
        this.secretKey,
        null,
        ""
      );

      // Make request with retry
      const response = await this.withRetry(
        `List objects`,
        async () => {
          const response = await fetch(listUrl.toString(), {
            method: 'GET',
            headers: listHeaders,
            signal: AbortSignal.timeout(10000)
          });

          if (response.status === 404) {
            return null; // Bucket doesn't exist
          }

          if (!response.ok) {
            throw new Error(`List failed: ${response.status}`);
          }

          return response;
        }
      );

      // Handle bucket not found
      if (!response) {
        return {
          objects: [],
          truncated: false,
          delimitedPrefixes: []
        } as R2Objects;
      }

      // Parse XML response
      const responseText = await response.text();
      const objects: R2Object[] = [];
      const delimitedPrefixes: string[] = [];

      // Extract common prefixes (folders)
      const prefixRegex = /<CommonPrefix><Prefix>([^<]+)<\/Prefix><\/CommonPrefix>/g;
      let prefixMatch;
      while ((prefixMatch = prefixRegex.exec(responseText)) !== null) {
        delimitedPrefixes.push(prefixMatch[1]);
      }

      // Extract objects
      const keyRegex = /<Contents><Key>([^<]+)<\/Key>.*?<Size>([^<]+)<\/Size>.*?<LastModified>([^<]+)<\/LastModified>.*?<ETag>([^<]+)<\/ETag>/g;
      let keyMatch;
      while ((keyMatch = keyRegex.exec(responseText)) !== null) {
        const key = keyMatch[1];
        const size = parseInt(keyMatch[2] || '0', 10);
        const lastModified = new Date(keyMatch[3]);
        const etag = keyMatch[4].replace(/&quot;/g, '').replace(/"/g, '');

        objects.push({
          key,
          version: '',
          size,
          etag,
          httpEtag: etag,
          uploaded: lastModified,
          httpMetadata: {
            contentType: 'application/octet-stream'
          },
          customMetadata: {},
          checksums: {
            md5: undefined,
            sha1: undefined,
            sha256: undefined,
            sha384: undefined,
            sha512: undefined
          }
        } as R2Object);
      }

      // Check if results are truncated
      const truncatedMatch = /<IsTruncated>([^<]+)<\/IsTruncated>/i.exec(responseText);
      const truncated = truncatedMatch && truncatedMatch[1].toLowerCase() === 'true';

      return {
        objects,
        truncated,
        delimitedPrefixes
      } as R2Objects;
    } catch (error) {
      console.error(`❌ [STORAGE] Error listing objects: ${error.message}`);
      return {
        objects: [],
        truncated: false,
        delimitedPrefixes: []
      } as R2Objects;
    }
  }
}