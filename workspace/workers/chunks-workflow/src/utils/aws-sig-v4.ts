/**
 * AWS Signature Version 4 implementation for MinIO
 * Based on AWS documentation
 */

// Helper function to convert string to Uint8Array
function stringToUint8Array(str: string): Uint8Array{
  const encoder = new TextEncoder();
  return encoder.encode(str);
}

// Helper function to convert Uint8Array to hex string
function uint8ArrayToHex(arr: Uint8Array): string{
  return Array.from(arr)
    .map(b=>b.toString(16).padStart(2, "0"))
    .join("");
}

// Helper function to sign a message with a key
async function sign(key: Uint8Array, msg: string): Promise<Uint8Array>{
  const encoder = new TextEncoder();
  const data = encoder.encode(msg);

  const cryptoKey = await crypto.subtle.importKey(
    "raw",
    key,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign"]
  );

  const signature = await crypto.subtle.sign(
    { name: "HM<PERSON>", hash: "SHA-256" },
    cryptoKey,
    data
  );

  return new Uint8Array(signature);
}

// Helper function to get the signature key
async function getSignatureKey(
  key: string,
  dateStamp: string,
  regionName: string,
  serviceName: string
): Promise<Uint8Array>{
  const kDate = await sign(stringToUint8Array(`AWS4${key}`), dateStamp);
  const kRegion = await sign(kDate, regionName);
  const kService = await sign(kRegion, serviceName);
  const kSigning = await sign(kService, "aws4_request");
  return kSigning;
}

// Generate AWS Signature Version 4 authorization header
export async function generateAuthorizationHeader(
  method: string,
  url: URL,
  accessKey: string,
  secretKey: string,
  payload: ArrayBuffer | null = null,
  contentType: string = "",
  region: string = "us-east-1",
  service: string = "s3"
): Promise<Record<string, string>>{
  // Create timestamp for headers and credential string
  const now = new Date();
  const amzDate = now.toISOString().replace(/[:-]|\.\d{3}/g, "");
  const dateStamp = amzDate.substring(0, 8);

  // console.log(`🔍 [AWS-SIG-V4] Generating auth headers for ${method} ${url.toString()}`);
  // console.log(`🔍 [AWS-SIG-V4] Using credentials: accessKey=${accessKey}, secretKey=${secretKey ? "****" : "undefined"}`);
  // console.log(`🔍 [AWS-SIG-V4] Date: ${amzDate}, DateStamp: ${dateStamp}`);

  // Create canonical URI
  const canonicalUri = url.pathname;
  // console.log(`🔍 [AWS-SIG-V4] Canonical URI: ${canonicalUri}`);

  // Create canonical querystring
  const canonicalQuerystring = url.search.substring(1); // Remove the leading '?'
  // console.log(`🔍 [AWS-SIG-V4] Canonical Querystring: ${canonicalQuerystring || "(empty)"}`);

  // Create payload hash
  let payloadHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"; // Empty payload hash
  if(payload) {
    const hashBuffer = await crypto.subtle.digest("SHA-256", payload);
    payloadHash = uint8ArrayToHex(new Uint8Array(hashBuffer));
    // console.log(`🔍 [AWS-SIG-V4] Payload hash: ${payloadHash} (from provided payload)`);
  } else {
    // console.log(`🔍 [AWS-SIG-V4] Payload hash: ${payloadHash} (empty payload)`);
  }

  // Create canonical headers
  const canonicalHeaders =
    `host:${url.host}\n` +
    `x-amz-content-sha256:${payloadHash}\n` +
    `x-amz-date:${amzDate}\n`;

  // Add content-type if provided
  const headersToSign = ["host", "x-amz-content-sha256", "x-amz-date"];
  let fullCanonicalHeaders = canonicalHeaders;
  if(contentType) {
    fullCanonicalHeaders = `content-type:${contentType}\n${canonicalHeaders}`;
    headersToSign.unshift("content-type");
    // console.log(`🔍 [AWS-SIG-V4] Added Content-Type: ${contentType}`);
  }

  // console.log(`🔍 [AWS-SIG-V4] Canonical Headers:\n${fullCanonicalHeaders}`);

  // Create signed headers
  const signedHeaders = headersToSign.join(";");
  // console.log(`🔍 [AWS-SIG-V4] Signed Headers: ${signedHeaders}`);

  // Create canonical request
  const canonicalRequest =
    `${method}\n${canonicalUri}\n${canonicalQuerystring}\n${fullCanonicalHeaders}\n${signedHeaders}\n${payloadHash}`;

  // console.log(`🔍 [AWS-SIG-V4] Canonical Request:\n${canonicalRequest}`);

  // Create string to sign
  const algorithm = "AWS4-HMAC-SHA256";
  const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;

  const canonicalRequestHash = uint8ArrayToHex(
    new Uint8Array(await crypto.subtle.digest("SHA-256", stringToUint8Array(canonicalRequest)))
  );

  const stringToSign =
    `${algorithm}\n${amzDate}\n${credentialScope}\n${canonicalRequestHash}`;

  // console.log(`🔍 [AWS-SIG-V4] String to Sign:\n${stringToSign}`);

  // Calculate signature
  const signingKey = await getSignatureKey(secretKey, dateStamp, region, service);
  const signature = uint8ArrayToHex(await sign(signingKey, stringToSign));

  // console.log(`🔍 [AWS-SIG-V4] Signature: ${signature}`);

  // Create authorization header
  const authorizationHeader =
    `${algorithm} Credential=${accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

  // console.log(`🔍 [AWS-SIG-V4] Authorization Header: ${authorizationHeader}`);

  // Return headers
  const headers: Record<string, string> = {
    "Authorization": authorizationHeader,
    "X-Amz-Date": amzDate,
    "X-Amz-Content-SHA256": payloadHash
  };

  if(contentType) {
    headers["Content-Type"] = contentType;
  }

  // console.log(`🔍 [AWS-SIG-V4] Final Headers:`, headers);
  return headers;
}
