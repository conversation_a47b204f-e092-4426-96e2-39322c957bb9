import { WorkflowStep } from "cloudflare:workers";
import { StepContext, WorkflowFile, InitialSetup, WorkflowMetadata } from "./types";

/**
 * Normalizes processor config and initializes metadata
 */
export async function initializeWorkflow(
  step: WorkflowStep,
  context: StepContext,
  file: WorkflowFile
): Promise<InitialSetup>{
  return await step.do("initializeWorkflow", async ()=>{
    console.log("Workflow processor config:", {
      fileId: file.fileId,
      processorConfig: file.processorConfig,
    });

    // Normalize processor config
    const normalizedConfig = normalizeProcessorConfig(
      file.processor,
      file.processorConfig
    );

    // Initialize metadata
    const metadata = initializeMetadata(file, normalizedConfig);

    // Extract all necessary file information
    // Always use the original objectKey for consistency
    const objectKey = file.objectKey;

    // Store both original and normalized fileId for consistency
    const fileInfo = {
      fileId: file.fileId,
      originalFileId: file.originalFileId || file.fileId, // Keep the original ID for reference
      processor: file.processor,
      objectKey: objectKey, // Use the original objectKey
      fileName: file.fileName,
      title: file.title,
      description: file.description,
      target: file.target,
      bucket: file.bucket,
      hasFormData: !!file.formData
    };

    // Log the file IDs to help with debugging
    console.log(`🔍 [INITIALIZE] File IDs: fileId=${fileInfo.fileId}, originalFileId=${fileInfo.originalFileId}`);

    // Ensure fileId is in MongoDB ObjectId format (24 hex chars)
    const isValidObjectId = typeof fileInfo.fileId === 'string' && /^[0-9a-fA-F]{24}$/.test(fileInfo.fileId);
    if (!isValidObjectId) {
      console.warn(`⚠️ [INITIALIZE] Invalid ObjectId format detected: ${fileInfo.fileId}`);

      // Extract a valid ObjectId if possible
      if (typeof fileInfo.fileId === 'string' && fileInfo.fileId.match(/[0-9a-fA-F]{24}/)) {
        const match = fileInfo.fileId.match(/([0-9a-fA-F]{24})/);
        if (match && match[1]) {
          fileInfo.fileId = match[1];
          console.log(`✅ [INITIALIZE] Extracted valid ObjectId: ${fileInfo.fileId}`);
        }
      }
    }

    return {
      normalizedConfig,
      metadata,
      fileInfo
    };
  });
}

/**
 * Normalizes processor config based on processor type
 */
function normalizeProcessorConfig(processor: string, config: any): any{
  if(processor === "unstructured") {
    return {
      skipDeJunk: config?.skipDeJunk ?? true,
      chunkingStrategy: config?.chunkingStrategy || "by_title",
      maxCharacters: config?.maxCharacters || 1024,
      splitPdfPage: config?.splitPdfPage ?? true,
      splitPdfConcurrencyLevel: config?.splitPdfConcurrencyLevel || 5,
      splitPdfAllowFailed: config?.splitPdfAllowFailed ?? true,
      minTokens: config?.minTokens,
      maxTokens: config?.maxTokens
    };
  } else if(processor === "openparse") {
    // Normalize OpenParse config to match the @divinci-ai/models interface
    return {
      // Standard OpenParseConfig properties
      semantic: config?.semantic_chunking ?? config?.openparse?.semantic ?? false,
      embeddingsProvider: config?.embeddings_provider || config?.openparse?.embeddings_provider || "ollama",
      useTokens: config?.openparse?.useTokens ?? true,
      minTokens: config?.openparse?.minTokens || config?.minTokens || 256,
      maxTokens: config?.openparse?.maxTokens || config?.maxTokens || 1024,
      chunkOverlap: config?.openparse?.chunkOverlap || 200,
      relevanceThreshold: config?.relevanceThreshold || 0.3,
      // Additional properties needed for processing
      skipDeJunk: config?.skipDeJunk ?? true
    };
  }
  return config;
}

/**
 * Initializes workflow metadata
 */
function initializeMetadata(file: WorkflowFile, normalizedConfig: any): WorkflowMetadata{
  const now = new Date().toISOString();

  return {
    workflowId: `wf-${file.target}-${file.fileId}`,
    fileId: file.fileId,
    target: file.target,
    processor: file.processor,
    processorConfig: normalizedConfig,
    steps: {
      initializeWorkflow: {
        startTime: now,
        endTime: now
      },
      deJunk: {}
    },
    status: "processing"
  };
}
