import { WorkflowStep } from "cloudflare:workers";
import { StepContext, InitialSetup, ChunkData } from "./types";
import { getRelevanceThreshold } from "../../utils";
import { countTokens, createVectorId } from "../../utils";

/**
 * Validates and filters chunks
 */
export async function validateAndFilterChunks(
  step: WorkflowStep,
  context: StepContext,
  initialSetup: InitialSetup,
  documentChunks: any[]
): Promise<ChunkData[]>{
  // Process chunks in smaller batches to avoid exceeding the 1MB limit
  const BATCH_SIZE = 20; // Smaller batch size to avoid exceeding the 1MB limit
  const validatedChunks: ChunkData[] = [];

  console.log(`🔄 Processing ${documentChunks.length} chunks in batches of ${BATCH_SIZE}`);

  // Process chunks in batches
  for (let batchStart = 0; batchStart < documentChunks.length; batchStart += BATCH_SIZE) {
    const batchEnd = Math.min(batchStart + BATCH_SIZE, documentChunks.length);
    const currentBatch = documentChunks.slice(batchStart, batchEnd);

    console.log(`🔄 Processing batch ${Math.floor(batchStart/BATCH_SIZE) + 1}/${Math.ceil(documentChunks.length/BATCH_SIZE)} (${currentBatch.length} chunks)`);

    // Process each chunk in the current batch
    for (let i = 0; i < currentBatch.length; i++) {
      const index = batchStart + i;
      const chunk = currentBatch[i];

      // Handle both string chunks and object chunks with text property
      if(typeof chunk === "string" && chunk.trim().length > 0) {
        // Handle string chunks
        validatedChunks.push({
          id: createVectorId(initialSetup.fileInfo.fileId, index),
          text: chunk.trim(),
          metadata: {
            whiteLabelId: initialSetup.metadata.target,
            originalName: initialSetup.fileInfo.fileName,
            vectorId: createVectorId(initialSetup.fileInfo.fileId, index),
            fileObjectKey: initialSetup.fileInfo.objectKey,
            tokenCount: countTokens(chunk)
          }
        });
      } else if(chunk && typeof chunk === "object" && typeof chunk.text === "string" && chunk.text.trim().length > 0) {
        // Handle object chunks with text property
        // Use existing metadata if available and merge with required fields
        const existingMetadata = chunk.metadata || {};

        validatedChunks.push({
          id: createVectorId(initialSetup.fileInfo.fileId, index),
          text: chunk.text.trim(),
          metadata: {
            whiteLabelId: initialSetup.metadata.target || existingMetadata.whiteLabelId,
            originalName: initialSetup.fileInfo.fileName,
            vectorId: createVectorId(initialSetup.fileInfo.fileId, index),
            fileObjectKey: initialSetup.fileInfo.objectKey,
            tokenCount: existingMetadata.tokenCount || countTokens(chunk.text),
            ...existingMetadata // Include any other metadata fields
          }
        });
      }
    }

    console.log(`✅ Processed batch ${Math.floor(batchStart/BATCH_SIZE) + 1}/${Math.ceil(documentChunks.length/BATCH_SIZE)}, total validated chunks: ${validatedChunks.length}`);
  }

  // Validate that we have chunks
  if(validatedChunks.length === 0) {
    throw new Error("No valid chunks were generated during document processing");
  }

  console.log("✅ Chunks processed:", {
    original: documentChunks.length,
    valid: validatedChunks.length,
    sample: validatedChunks[0].text.substring(0, 100)
  });

  // Step 5: DeJunk Filtering
  const skipDeJunk = initialSetup.normalizedConfig?.skipDeJunk;
  console.log("🚮⚙️ DeJunk configuration:", { skipDeJunk });

  let filteredChunks: ChunkData[];
  if(skipDeJunk) {
    console.log("🔄 Skipping DeJunk filtering");
    filteredChunks = validatedChunks;

    initialSetup.metadata.steps.deJunk.skipped = true;
  } else {
    filteredChunks = await step.do("filterChunks", async ()=>{
      initialSetup.metadata.steps.deJunk.chunksBeforeDeJunk = validatedChunks.length;

      console.log("🔄 Applying relevance filtering...");
      const relevanceThreshold = getRelevanceThreshold(initialSetup.normalizedConfig);

      const processedChunks = await filterRelevantChunks(context, validatedChunks, relevanceThreshold);

      initialSetup.metadata.steps.deJunk.chunksAfterDeJunk = processedChunks.length;
      return processedChunks;
    });
  }

  return filteredChunks;
}

/**
 * Filters chunks based on relevance
 */
async function filterRelevantChunks(
  context: StepContext,
  chunks: ChunkData[],
  relevanceThreshold: number
): Promise<ChunkData[]>{
  // Process chunks in smaller batches to avoid exceeding the 1MB limit
  const BATCH_SIZE = 20; // Smaller batch size to avoid exceeding the 1MB limit
  const filteredChunks: ChunkData[] = [];

  console.log(`🔄 Filtering ${chunks.length} chunks in batches of ${BATCH_SIZE}`);

  // Process chunks in batches
  for (let batchStart = 0; batchStart < chunks.length; batchStart += BATCH_SIZE) {
    const batchEnd = Math.min(batchStart + BATCH_SIZE, chunks.length);
    const currentBatch = chunks.slice(batchStart, batchEnd);

    console.log(`🔄 Filtering batch ${Math.floor(batchStart/BATCH_SIZE) + 1}/${Math.ceil(chunks.length/BATCH_SIZE)} (${currentBatch.length} chunks)`);

    // For now, we'll just add all chunks to the filtered list
    // In the future, we can implement relevance filtering
    filteredChunks.push(...currentBatch);

    console.log(`✅ Filtered batch ${Math.floor(batchStart/BATCH_SIZE) + 1}/${Math.ceil(chunks.length/BATCH_SIZE)}, total filtered chunks: ${filteredChunks.length}`);
  }

  return filteredChunks;
}
