import { WorkflowStep } from "cloudflare:workers";
import { StepContext, ChunkData } from "./types";
import { getTableName } from "../../utils";

/**
 * Stores chunks in D1
 */
export async function storeChunksInD1(
  step: WorkflowStep,
  context: StepContext,
  chunks: ChunkData[],
  whitelabelId: string
): Promise<void>{
  await step.do("storeChunksInD1", async ()=>{
    console.log(`📝 Storing ${chunks.length} chunks in D1`);

    // Pass the chunks and whitelabelId to storeChunksInD1
    await storeChunks(context, chunks, whitelabelId);
  });
}

/**
 * Stores chunks in D1 database using the D1 worker API
 */
async function storeChunks(
  context: StepContext,
  chunks: ChunkData[],
  whitelabelId: string
): Promise<void>{
  const batchSize = 20;
  const tableName = getTableName(whitelabelId);
  console.log(`📊 Storing ${chunks.length} chunks in batches of ${batchSize}`);
  console.log(`🔍 Using table: ${tableName}`);

  // Get the D1 worker URL from environment
  // In local development, use direct connection to the D1 worker
  // Use the IP address 172.18.0.x which is the Docker network IP range
  const d1WorkerUrl = (context.env as any).CLOUDFLARE_D1_API_URL || "http://**********:8787";
  const upsertEndpoint = `${d1WorkerUrl}/api/upsert`;

  console.log(`🔍 Using D1 worker URL: ${d1WorkerUrl}`);
  console.log(`🔍 Using D1 upsert endpoint: ${upsertEndpoint}`);

  // First, ensure the table exists
  try {
    console.log(`📝 Ensuring table ${tableName} exists`);
    const createTableResponse = await fetch(upsertEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Cloudflare-Worker-X-Dev-Auth": (context.env as any).CLOUDFLARE_WORKER_X_AUTH_DEV || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvZSBEb2UiLCJpYXQiOjE1MTYyMzkwMjJ9.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
      },
      body: JSON.stringify({
        table: tableName,
        schema: {
          id: "TEXT PRIMARY KEY",
          text: "TEXT NOT NULL",
          metadata: "TEXT NOT NULL"
        }
      })
    });

    if(!createTableResponse.ok) {
      const errorText = await createTableResponse.text();
      console.error(`❌ Error creating table: ${errorText}`);
    } else {
      console.log(`✅ Table ${tableName} created or already exists`);
    }
  }catch(error) {
    console.error(`❌ Error ensuring table exists:`, error);
  }

  for(let i = 0; i < chunks.length; i += batchSize) {
    const batch = chunks.slice(i, i + batchSize);

    if(batch.length === 0) {
      continue;
    }

    console.log(`📝 Attempting to store ${batch.length} records in batch ${i / batchSize + 1}`);

    let attempts = 0;
    const maxAttempts = 3;
    let success = false;

    while(attempts < maxAttempts && !success) {
      try {
        // Prepare the chunks for the D1 worker
        const validChunks = batch.filter(chunk=>chunk && chunk.id && chunk.text);

        // If no valid chunks, skip
        if(validChunks.length === 0) {
          console.log(`⚠️ No valid chunks in batch ${i / batchSize + 1}, skipping`);
          success = true;
          continue;
        }

        // Format chunks for the D1 worker
        const formattedChunks = validChunks.map(chunk=>({
          id: chunk.id,
          text: chunk.text,
          metadata: chunk.metadata || {}
        }));

        // Log the request details
        console.log(`🔍 Preparing D1 upsert request:`, {
          endpoint: upsertEndpoint,
          chunkCount: formattedChunks.length,
          firstChunkPreview: {
            id: formattedChunks[0].id,
            textLength: formattedChunks[0].text.length,
            metadataKeys: Object.keys(formattedChunks[0].metadata || {})
          }
        });

        // Prepare headers
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
          "Cloudflare-Worker-X-Dev-Auth": (context.env as any).CLOUDFLARE_WORKER_X_AUTH_DEV || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvZSBEb2UiLCJpYXQiOjE1MTYyMzkwMjJ9.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
        };

        // Add x-worker-local-dev header for local development
        if(context.env.ENVIRONMENT === 'local' || context.env.ENVIRONMENT === 'development') {
          headers["x-worker-local-dev"] = "true";
          console.log(`🔑 Added x-worker-local-dev header for D1 worker request`);
        }

        // Make the request to the D1 worker
        const response = await fetch(upsertEndpoint, {
          method: "POST",
          headers,
          body: JSON.stringify({
            table: tableName,
            chunks: formattedChunks
          })
        });

        // Check if the request was successful
        if(!response.ok) {
          const errorText = await response.text();
          throw new Error(`D1 worker returned error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const result = await response.json() as { success: boolean };

        console.log(`✅ Successfully stored batch ${i / batchSize + 1}`, {
          success: result.success
        });

        success = true;
      }catch(error) {
        attempts++;
        console.error(`❌ Error storing batch ${i / batchSize + 1} (attempt ${attempts}/${maxAttempts}):`, error);

        if(attempts >= maxAttempts) {
          throw error;
        }

        // Wait before retrying
        await new Promise(resolve=>setTimeout(resolve, 1000 * attempts));
      }
    }
  }
}
