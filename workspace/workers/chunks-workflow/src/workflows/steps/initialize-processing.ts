import { WorkflowStep } from "cloudflare:workers";
import { StepContext, R2ValidationResult, InitialSetup } from "./types";

/**
 * Initializes processing session
 *
 * IMPORTANT: This function has been refactored to always use the original object key
 * without trying to find similar files or use special handling for LOCAL MODE.
 * This ensures consistent behavior between local and production environments.
 */
export async function initializeProcessing(
  step: WorkflowStep,
  context: StepContext,
  r2Result: R2ValidationResult,
  initialSetup: InitialSetup,
  processorType: string,
  processor: any
): Promise<any>{
  return await step.do("initializeProcessing", async ()=>{
    // Construct the file URL using the original object key
    let fileUrl: string;

    // Encode the object key for URL usage
    const encodedKey = r2Result.objectKey.split("/").map(segment => encodeURIComponent(segment)).join("/");

    // IMPORTANT: In BARE METAL MODE, we should use the same URL construction logic
    // for both local and production environments. The only difference should be
    // the base URL, which is provided by the environment configuration.
    //
    // The R2_BUCKET_URL environment variable should be set appropriately for each
    // environment, pointing to either the local MinIO server or the production R2 bucket.
    fileUrl = `${context.env.R2_BUCKET_URL}/${encodedKey}`;
    console.log(`🔄 Constructed file URL: ${fileUrl}`);

    console.log(`🔄 R2 Bucket URL from env: ${context.env.R2_BUCKET_URL}`);
    console.log(`🔄 Initializing processing for file: ${fileUrl}`, {
      processor: processorType,
      config: initialSetup.normalizedConfig
    });

    // Initialize the processing session with the file URL
    return await processor.initializeSession(
      fileUrl,
      initialSetup.normalizedConfig
    );
  });
}
