import { WorkflowStep } from "cloudflare:workers";
import { StepContext, WorkflowMetadata } from "./types";

/**
 * Upserts workflow metadata
 */
export async function upsertWorkflowMetadata(
  step: WorkflowStep,
  context: StepContext,
  metadata: WorkflowMetadata,
  error?: Error | any
): Promise<void>{
  await step.do("upsertWorkflowMetadata", async ()=>{
    try {
      const { workflowId, fileId, processor, processorConfig, steps, status } = metadata;

      // Convert error to string to avoid D1_TYPE_ERROR
      let errorString = null;
      if(error) {
        if(typeof error === "string") {
          errorString = error;
        } else if(error instanceof Error) {
          errorString = error.message || String(error);
        } else {
          try {
            errorString = JSON.stringify(error);
          }catch(e) {
            errorString = String(error);
          }
        }
      }

      // Check if we're in local development mode
      const isLocalDevelopment = context.env.ENVIRONMENT === "local" || context.env.ENVIRONMENT === "development";

      // First, check if the table exists and has the expected columns
      try {
        // Try to get the table schema
        const tableInfo = await context.env.DB.prepare(`PRAGMA table_info(workflow_metadata)`).all();
        const columns = tableInfo.results.map((col: any)=>col.name);

        // In local development mode, create the table if it doesn't exist or has missing columns
        if(isLocalDevelopment) {
          // Check if we need to create or alter the table
          if(tableInfo.results.length === 0) {
            // Create the workflow_metadata table

            // Create the table with a minimal schema
            await context.env.DB.prepare(`
              CREATE TABLE IF NOT EXISTS workflow_metadata (
                workflow_id TEXT PRIMARY KEY,
                file_id TEXT,
                metadata TEXT NOT NULL,
                status TEXT,
                error TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
              )
            `).run();

            // Insert with minimal schema
            await context.env.DB.prepare(`
              INSERT OR REPLACE INTO workflow_metadata (
                workflow_id, file_id, metadata, status, error, created_at
              ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `).bind(
              workflowId,
              fileId,
              JSON.stringify(metadata),
              status,
              errorString
            ).run();

            // Workflow metadata stored with minimal schema
            return;
          }

          // If the table exists but is missing columns, use only the available columns
          // Using available columns in workflow_metadata table

          // Build a dynamic query based on available columns
          const availableColumns = [
            columns.includes("workflow_id") ? "workflow_id" : null,
            columns.includes("file_id") ? "file_id" : null,
            columns.includes("metadata") ? "metadata" : null,
            columns.includes("status") ? "status" : null,
            columns.includes("error") ? "error" : null,
          ].filter(Boolean);

          // If metadata column is not available, we need to add it
          if (!columns.includes("metadata")) {
            console.log("⚠️ Metadata column not found, adding it to the table");
            try {
              await context.env.DB.prepare(`
                ALTER TABLE workflow_metadata ADD COLUMN metadata TEXT NOT NULL DEFAULT '{}'
              `).run();
              // Add metadata to available columns after adding it to the table
              availableColumns.push("metadata");
            } catch (error) {
              console.error("❌ Error adding metadata column:", error);
            }
          }

          if(availableColumns.length > 0) {
            const placeholders = availableColumns.map(()=>"?").join(", ");
            const query = `
              INSERT OR REPLACE INTO workflow_metadata (
                ${availableColumns.join(", ")}, created_at
              ) VALUES (${placeholders}, CURRENT_TIMESTAMP)
            `;

            // Prepare values based on available columns
            const values = [];
            if(columns.includes("workflow_id") || availableColumns.includes("workflow_id")) values.push(workflowId);
            if(columns.includes("file_id") || availableColumns.includes("file_id")) values.push(fileId);
            if(columns.includes("metadata") || availableColumns.includes("metadata")) values.push(JSON.stringify(metadata));
            if(columns.includes("status") || availableColumns.includes("status")) values.push(status);
            if(columns.includes("error") || availableColumns.includes("error")) values.push(errorString);

            await context.env.DB.prepare(query).bind(...values).run();
            // Workflow metadata stored with available columns
            return;
          }

          // Could not store workflow metadata - no usable columns found
          return;
        }

        // For production, check if required columns exist
        const hasTargetColumn = columns.includes("target");
        const hasProcessorColumn = columns.includes("processor");
        const hasProcessorConfigColumn = columns.includes("processor_config");
        const hasStepsColumn = columns.includes("steps");
        const hasStatusColumn = columns.includes("status");

        if(!hasTargetColumn || !hasProcessorColumn || !hasProcessorConfigColumn || !hasStepsColumn || !hasStatusColumn) {
          // Some columns not found in workflow_metadata table, using simplified schema

          // Use a simplified schema with only the available columns
          const availableColumns = [
            "workflow_id",
            "file_id",
            hasProcessorColumn ? "processor" : null,
            hasProcessorConfigColumn ? "processor_config" : null,
            hasStepsColumn ? "steps" : null,
            hasStatusColumn ? "status" : null,
            "error",
            "created_at"
          ].filter(Boolean);

          const placeholders = availableColumns.map(()=>"?").join(", ");
          const query = `
            INSERT OR REPLACE INTO workflow_metadata (
              ${availableColumns.join(", ")}
            ) VALUES (${placeholders})
          `;

          // Prepare values based on available columns
          const values = [workflowId, fileId];
          if(hasProcessorColumn) values.push(processor);
          if(hasProcessorConfigColumn) values.push(JSON.stringify(processorConfig));
          if(hasStepsColumn) values.push(JSON.stringify(steps));
          if(hasStatusColumn) values.push(status);
          values.push(errorString, "CURRENT_TIMESTAMP");

          await context.env.DB.prepare(query).bind(...values).run();
          return;
        }
      }catch(schemaError) {
        if(isLocalDevelopment) {
          // Error checking workflow_metadata table schema - skipping workflow metadata storage
          return;
        } else {
          console.error("❌ Error checking table schema:", schemaError);
          // Continue with the standard query in production, it will fail if the schema is incompatible
        }
      }

      // If we get here, try the standard query with all columns
      try {
        await context.env.DB.prepare(`
          INSERT OR REPLACE INTO workflow_metadata (
            workflow_id, file_id, metadata, target, processor, processor_config, steps, status, error, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `).bind(
          workflowId,
          fileId,
          JSON.stringify(metadata), // Always include metadata to satisfy NOT NULL constraint
          metadata.target || null, // Use null if target is not defined
          processor,
          JSON.stringify(processorConfig),
          JSON.stringify(steps),
          status,
          errorString
        ).run();
      } catch (error) {
        // If the error is about missing columns, try without the target column
        if (error.toString().includes("no column named target")) {
          console.log("⚠️ Target column not found, using simplified query");
          await context.env.DB.prepare(`
            INSERT OR REPLACE INTO workflow_metadata (
              workflow_id, file_id, metadata, processor, processor_config, steps, status, error, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
          `).bind(
            workflowId,
            fileId,
            JSON.stringify(metadata), // Always include metadata to satisfy NOT NULL constraint
            processor,
            JSON.stringify(processorConfig),
            JSON.stringify(steps),
            status,
            errorString
          ).run();
        } else {
          // Re-throw other errors
          throw error;
        }
      }

      // Workflow metadata upserted successfully
    }catch(error) {
      console.error("❌ Error upserting workflow metadata:", error);
      // Log but don't throw - this shouldn't fail the whole workflow
    }
  });
}
