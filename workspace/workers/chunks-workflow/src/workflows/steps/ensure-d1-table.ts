import { WorkflowStep } from "cloudflare:workers";
import { StepContext } from "./types";
import { getTableName } from "../../utils";

/**
 * Ensures D1 tables exist
 */
export async function ensureD1Table(
  step: WorkflowStep,
  context: StepContext,
  whitelabelId: string
): Promise<void>{
  await step.do("ensureD1Table", async ()=>{
    console.log("🪲 Debug - Cloudflare credentials:", {
      accountId: context.env.CLOUDFLARE_ACCOUNT_ID,
      apiTokenLength: context.env.CLOUDFLARE_API_TOKEN?.length,
    });

    await Promise.all([
      ensureWorkflowMetadataTable(context),
      ensureChunksTable(whitelabelId, context.env.DB)
    ]);
  });
}

/**
 * Ensures workflow metadata table exists
 */
async function ensureWorkflowMetadataTable(context: StepContext): Promise<void>{
  try {
    await context.env.DB.prepare(`
      CREATE TABLE IF NOT EXISTS workflow_metadata (
        workflow_id TEXT PRIMARY KEY,
        file_id TEXT,
        target TEXT,
        processor TEXT,
        processor_config TEXT,
        steps TEXT,
        status TEXT,
        error TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    // Create index on file_id
    await context.env.DB.prepare(`
      CREATE INDEX IF NOT EXISTS idx_workflow_metadata_file_id
        ON workflow_metadata(file_id)
    `).run();

    console.log("✅ Workflow metadata table and indexes ensured");
  }catch(error) {
    console.error("❌ Error ensuring workflow metadata table:", error);
    throw error;
  }
}

/**
 * Ensures chunks table exists
 */
export async function ensureChunksTable(whitelabelId: string, db: D1Database): Promise<void>{
  const tableName = getTableName(whitelabelId);

  try {
    await db.prepare(`
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id TEXT PRIMARY KEY,
        text TEXT,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    return;
  }catch(error) {
    console.error(`❌ Error ensuring chunks table for ${whitelabelId}:`, error);
    throw error;
  }
}
