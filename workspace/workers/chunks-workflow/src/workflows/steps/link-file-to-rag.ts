import { WorkflowStep } from "cloudflare:workers";
import { StepContext } from "./types";

/**
 * Links file to RAG with robust error handling
 */
export async function linkFileToRag(
  step: WorkflowStep,
  context: StepContext,
  ragId: string,
  whitelabelId: string,
  fileId: string,
  auth0Token: string
): Promise<void>{
  await step.do("linkFileToRag", async ()=>{
    console.log(`📝 Linking file ${fileId} to custom RAG ${ragId}`);

    // First, add to pending (using updatePendingAtomic)
    await addFileToPending(context, ragId, whitelabelId, fileId, auth0Token);

    // Then, move to success (using updateSuccessAtomic)
    await moveFileToSuccess(context, ragId, whitelabelId, fileId, auth0Token);

    console.log(`✅ Successfully linked file ${fileId} to RAG ${ragId}`);
  });
}

/**
 * Adds file to pending with robust error handling
 */
async function addFileToPending(
  context: StepContext,
  ragId: string,
  whitelabelId: string,
  fileId: string,
  auth0Token: string
): Promise<void>{
  const maxAttempts = 3;
  const baseDelay = 2000; // 2 seconds

  for(let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 Adding file to pending (attempt ${attempt}/${maxAttempts})`);

      const pendingResponse = await fetch(
        `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/pending-file`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${auth0Token}`
          },
          body: JSON.stringify({ fileId })
        }
      );

      if(pendingResponse.ok) {
        console.log(`✅ Successfully added file ${fileId} to pending`);
        return;
      }

      let error: any;
      try {
        error = await pendingResponse.json();
      }catch(e) {
        error = await pendingResponse.text();
      }

      console.error(`❌ Failed to add file to pending (attempt ${attempt}/${maxAttempts}):`, error);

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
        console.warn(`⚠️ All attempts to add file to pending failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
      }
    }catch(error) {
      console.error(`❌ Error adding file to pending (attempt ${attempt}/${maxAttempts}):`, error);

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
        console.warn(`⚠️ All attempts to add file to pending failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
      }
    }
  }
}

/**
 * Moves file to success with robust error handling
 */
async function moveFileToSuccess(
  context: StepContext,
  ragId: string,
  whitelabelId: string,
  fileId: string,
  auth0Token: string
): Promise<void>{
  const maxAttempts = 3;
  const baseDelay = 2000; // 2 seconds

  for(let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 Moving file to success (attempt ${attempt}/${maxAttempts})`);

      const successResponse = await fetch(
        `${context.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/success-file`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${auth0Token}`
          },
          body: JSON.stringify({ fileId })
        }
      );

      if(successResponse.ok) {
        console.log(`✅ Successfully moved file ${fileId} to success`);
        return;
      }

      let error: any;
      try {
        error = await successResponse.json();
      }catch(e) {
        error = await successResponse.text();
      }

      console.error(`❌ Failed to move file to success (attempt ${attempt}/${maxAttempts}):`, error);

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
        console.warn(`⚠️ All attempts to move file to success failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
      }
    }catch(error) {
      console.error(`❌ Error moving file to success (attempt ${attempt}/${maxAttempts}):`, error);

      if(attempt < maxAttempts) {
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 500;
        const delay = baseDelay + jitter;
        console.log(`🔄 Retrying in ${Math.round(delay/1000)} seconds...`);
        await new Promise(resolve=>setTimeout(resolve, delay));
      } else {
        console.warn(`⚠️ All attempts to move file to success failed, continuing anyway`);
        return; // Continue with the workflow even if this step fails
      }
    }
  }
}
