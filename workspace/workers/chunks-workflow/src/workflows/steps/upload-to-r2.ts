import { WorkflowStep } from "cloudflare:workers";
import { StepContext, R2ValidationResult, WorkflowFile } from "./types";

/**
 * Handles file upload if needed
 *
 * IMPORTANT: This function has been refactored to always upload files directly
 * without trying to find similar files or use special handling for LOCAL MODE.
 * This ensures consistent behavior between local and production environments.
 */
export async function uploadToR2(
  step: WorkflowStep,
  context: StepContext,
  r2Result: R2ValidationResult,
  files: WorkflowFile[]
): Promise<{ uploaded: boolean, size?: number, fromR2?: boolean, skipped?: boolean, error?: any }>{
  // Skip upload if file already exists
  if(r2Result.hasExistingFile) {
    console.log(`🔄 [UPLOAD] File already exists, skipping upload: ${r2Result.objectKey}`);
    return { uploaded: false, skipped: true };
  }

  return await step.do("uploadToR2", {
    retries: {
      limit: 3,
      delay: "5 seconds",
      backoff: "exponential"
    },
    timeout: "10 minutes"
  }, async ()=>{
    console.log("🔍 Debug - uploadToR2 files array:", {
      filesLength: files.length,
      file0: files[0] ? {
        file0: files[0]
      } : "undefined"
    });

    // If we have form data, use it to upload the file
    const file = files[0]?.formData?.get("file") as File;
    if(!file) {
      console.log("🔄 No file found in FormData, skipping upload step");
      // Return a dummy result to indicate that we're skipping the upload
      return { uploaded: false, skipped: true };
    }

    try {
      // Get the file content as ArrayBuffer
      const fileBuffer = await file.arrayBuffer();

      // Add some logging
      console.log(`📤 Uploading file to storage: ${r2Result.objectKey}, size: ${fileBuffer.byteLength} bytes, type: ${file.type}`);

      // Always use the original object key for consistency
      const uploadKey = r2Result.objectKey;

      await context.storageClient.put(uploadKey, fileBuffer, {
        httpMetadata: {
          contentType: file.type || "application/octet-stream"
        },
        customMetadata: {
          fileName: file.name,
          originalObjectKey: r2Result.objectKey,
          uploadedAt: new Date().toISOString()
        }
      });

      console.log(`✅ Successfully uploaded file to storage: ${uploadKey}`);

      return { uploaded: true, size: fileBuffer.byteLength };
    }catch(error) {
      console.error(`❌ Error uploading file to storage: ${error}`);
      throw error;
    }
  });
}
