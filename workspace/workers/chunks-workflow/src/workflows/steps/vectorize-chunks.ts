import { WorkflowStep } from "cloudflare:workers";
import { StepContext, VectorizeConfig, ChunkData, InitialSetup } from "./types";

/**
 * Vectorizes chunks
 */
export async function vectorizeChunks(
  step: WorkflowStep,
  context: StepContext,
  filteredChunks: ChunkData[],
  vectorizeConfig: VectorizeConfig,
  initialSetup: InitialSetup
): Promise<void>{
  await step.do("vectorizeChunks", async ()=>{

    // Only vectorize if we have a ragId
    if(!vectorizeConfig.ragId) {
      console.log("⚠️ No ragId provided, skipping vectorization");
      return;
    }

    const vectorIndex = `vector-index-${vectorizeConfig.ragId}`;

    // Ensure vector index exists first
    console.log(`🔄 Ensuring vector index exists: ${vectorIndex}`);
    try {
      // Use the Cloudflare API directly
      // Import the VectorizeAPI class from utils
      const { VectorizeAPI } = await import("../../utils/vectorize-api");

      // Create a new instance of the VectorizeAPI class
      const vectorizeApi = new VectorizeAPI({
        accountId: context.env.CLOUDFLARE_ACCOUNT_ID,
        apiToken: context.env.CLOUDFLARE_API_TOKEN,
        ragName: "default"
      });

      // Find or create the index
      await vectorizeApi.findOrCreateIndex(
        vectorIndex,
        `📊 Vectors for chunks \n\n vectorIndex: ${vectorIndex}`
      );

      console.log(`✅ Successfully found or created vector index: ${vectorIndex}`);

      // Process chunks in batches to avoid hitting API limits
      const batchSize = 10;

      // Make sure the fileId is valid
    let fileId = initialSetup.fileInfo.fileId;
    // Check if the fileId is in a valid MongoDB ObjectId format
    const isValidObjectId = typeof fileId === 'string' && /^[0-9a-fA-F]{24}$/.test(fileId);
    if (!isValidObjectId) {
      console.warn(`⚠️ [VECTORIZE] Invalid ObjectId format detected: ${fileId}`);
      
      // If it contains special characters like '/', attempt to extract a valid ID portion
      if (typeof fileId === 'string' && fileId.includes('/')) {
        const parts = fileId.split('/');
        // Check each part for a valid ObjectId
        for (const part of parts) {
          if (/^[0-9a-fA-F]{24}$/.test(part)) {
            initialSetup.fileInfo.fileId = part;
            console.log(`✅ [VECTORIZE] Extracted valid ObjectId from path: ${part}`);
            break;
          }
        }
      }
    }
    
    // Process chunks in batches
      for(let i = 0; i < filteredChunks.length; i += batchSize) {
        const batch = filteredChunks.slice(i, i + batchSize);

        console.log("🔄 Processing batch for embeddings", {
          batchIndex: i / batchSize + 1,
          batchSize: batch.length,
          fileId: initialSetup.fileInfo.fileId,
          vectorIndex
        });

        // Create embeddings for each chunk
        const vectorsWithEmbeddings = [];

        for(const chunk of batch) {
          try {
            // Generate embedding for the chunk
            let embeddingResult;

            try {
              // Use Cloudflare Workers AI for embeddings
              embeddingResult = await context.env.AI.run(
                "@cf/baai/bge-base-en-v1.5",
                { text: chunk.text }
              );
            }catch(embeddingError) {
              console.error("❌ Error generating embedding with Workers AI:", embeddingError);

              // Fallback to a simpler approach - just use a random vector
              // This is just for testing and should be replaced with a proper fallback
              embeddingResult = Array(384).fill(0).map(()=>Math.random() * 2 - 1);

              console.warn("⚠️ Using fallback random embedding for chunk:", {
                chunkId: chunk.id,
                reason: embeddingError.message
              });
            }

            // Validate embedding result
            if(!embeddingResult || (Array.isArray(embeddingResult) && embeddingResult.length === 0)) {
              console.error("❌ Invalid embedding result:", embeddingResult);
              continue;
            }

            // Extract the actual embedding values
            const embedding = Array.isArray(embeddingResult)
              ? embeddingResult
              : embeddingResult.data && Array.isArray(embeddingResult.data)
                ? embeddingResult.data
                : embeddingResult.embedding && Array.isArray(embeddingResult.embedding)
                  ? embeddingResult.embedding
                  : null;

            if(!embedding) {
              console.error("❌ Could not extract embedding from result:", {
                embeddingResult,
                type: typeof embeddingResult
              });
              continue;
            }

            // Add the vector with embedding to the batch
            vectorsWithEmbeddings.push({
              id: chunk.id,
              values: embedding,
              metadata: {
                text: chunk.text,
                whiteLabelId: vectorizeConfig.whitelabelId,
                originalName: initialSetup.fileInfo.fileName,
                vectorId: chunk.id,
                fileObjectKey: initialSetup.fileInfo.objectKey,
                tokenCount: chunk.metadata?.tokenCount || 0,
                tags: chunk.metadata?.tags || [] // Add empty tags array to satisfy API requirements
              }
            });

            // Log the embedding result to debug
            console.log("Embedding result for chunk:", {
              chunkId: chunk.id,
              embeddingType: typeof embeddingResult,
              embeddingValue: embeddingResult,
              extractedEmbedding: embedding ? `${embedding.length} values` : null
            });
          }catch(error) {
            console.error("❌ Error processing chunk for embedding:", {
              chunkId: chunk.id,
              error
            });
          }
        }

        // Skip if no vectors with embeddings
        if(vectorsWithEmbeddings.length === 0) {
          console.warn("⚠️ No vectors with embeddings in batch, skipping");
          continue;
        }

        try {
          // Log the first vector for debugging
          if(vectorsWithEmbeddings.length > 0) {
            console.log("🔍 First vector to upsert:", {
              id: vectorsWithEmbeddings[0].id,
              valuesLength: vectorsWithEmbeddings[0].values.length,
              metadataKeys: vectorsWithEmbeddings[0].metadata ? Object.keys(vectorsWithEmbeddings[0].metadata) : []
            });
          }

          // Upsert vectors to Vectorize
          await vectorizeApi.upsert(vectorIndex, vectorsWithEmbeddings);

          console.log("✅ Successfully processed and stored batch", {
            batchIndex: i / batchSize + 1,
            totalBatches: Math.ceil(filteredChunks.length / batchSize)
          });
        }catch(error) {
          console.error("❌ Error upserting vectors to Vectorize:", error);
          throw error;
        }
      }
    }catch(error) {
      console.error("❌ Error finding or creating Vectorize index:", error);
      throw error;
    }

    // The vectorization is already handled in the code above.
    // No need to duplicate the process.
  });
}
