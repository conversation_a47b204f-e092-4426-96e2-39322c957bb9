import { WorkflowStep } from "cloudflare:workers";
import { StepContext, R2ValidationResult, InitialSetup } from "./types";

/**
 * Processes document in batches
 */
export async function processBatch(
  step: WorkflowStep,
  context: StepContext,
  r2Result: R2ValidationResult,
  initialSetup: InitialSetup,
  processor: any,
  batchNumber: number
): Promise<any[]>{
  return await step.do(`processBatch_${batchNumber}`, {
    retries: {
      limit: 3,
      delay: "5 seconds",
      backoff: "exponential"
    },
    timeout: "30 minutes",
  }, async ()=>{
    console.log(`📄 Processing batch ${batchNumber} for file: ${r2Result.objectKey}`);

    try {
      const result = await processor.processBatch(
        batchNumber,
        initialSetup.normalizedConfig
      );

      // Add validation for empty or invalid results
      if(!result) {
        console.log(`🏁 No chunks in batch ${batchNumber} - assuming end of processing`);
        return null;
      }

      console.log(`✅ Successfully processed batch ${batchNumber} with ${result.length} chunks`);
      return result;
    }catch(error) {
      console.error(`❌ Error processing batch ${batchNumber}:`, error);
      if(error.message.includes("Session expired")) {
        console.log("🔄 Session expired, reinitializing...");

        // IMPORTANT: In BARE METAL MODE, we should not have any special handling for LOCAL MODE.
        // The environment configuration should handle the differences between environments.

        // Use the same URL construction logic as in the initial session creation
        // Encode the object key for URL usage
        const encodedKey = r2Result.objectKey.split("/").map(segment => encodeURIComponent(segment)).join("/");

        // Construct the file URL using the original object key
        const fileUrl = `${context.env.R2_BUCKET_URL}/${encodedKey}`;
        console.log(`🔄 Reinitializing with URL: ${fileUrl}`);

        // Reinitialize the session
        await processor.initializeSession(
          fileUrl,
          initialSetup.normalizedConfig
        );

        // Retry the batch processing
        return await processor.processBatch(
          batchNumber,
          initialSetup.normalizedConfig
        );
      }

      // For other errors, rethrow
      throw error;
    }
  });
}
