// Import Cloudflare Workers types
import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from "cloudflare:workers";
import { NonRetryableError } from "cloudflare:workflows";

import { findOrCreateVectorizeIndex } from "../utils/vector-index";
import { WorkflowMetadata } from "src/types/workflow";
import {
  createVectorId, countTokens, getTableName,
  getRelevanceThreshold,
 } from "../utils";
import { ensureChunksTable } from "../utils/d1-api";
import {
  Env, ChunksVectorizedEvent, FileRecordPayload,
  FileRecord, R2ValidationResult, RagVectorTextChunksStatus,
  FileRecordResponse, ProcessedChunk,
 } from "../types";
import { VectorizeAPI } from "../utils/vectorize-api";
import { UnstructuredProcessor } from "../processors/unstructured";
import { OpenParseProcessor } from "../processors/openparse";
import { FILTER_BATCH_SIZE, EMBEDDING_BATCH_SIZE } from "../constants";
import { createStorageClient, StorageClient } from "../utils/storage-client";


export class ChunksVectorizedWorkflow extends WorkflowEntrypoint<Env, ChunksVectorizedEvent> {
  private fileRecord: FileRecordResponse["data"] | null = null;
  private storageClient: StorageClient;

  constructor(ctx: ExecutionContext, protected readonly env: Env){
    super(ctx, env);
    // Initialize the storage client
    this.storageClient = createStorageClient(env);

    // Assign the storage client to the environment for use in other components
    this.env.storageClient = this.storageClient;
  }

  private async storeChunksInD1(
    chunks: ProcessedChunk[],
    whitelabelId: string
  ): Promise<void>{
    if(!chunks || chunks.length === 0) {
      throw new Error("❌ No chunks provided for storage");
    }

    const batchSize = 20;
    const tableName = getTableName(whitelabelId);
    console.log(`📊 Storing ${chunks.length} chunks in batches of ${batchSize}`);
    console.log(`🔍 Using table: ${tableName}`);

    for(let i = 0; i < chunks.length; i += batchSize) {
      const batch = chunks.slice(i, i + batchSize);
      if(!batch.length) {
        console.warn(`⚠️ Empty batch encountered at index ${i}`);
        continue;
      }

      console.log(`📝 Attempting to store ${batch.length} records in batch ${i / batchSize + 1}`);

      let attempts = 0;
      const maxAttempts = 3;

      while(attempts < maxAttempts) {
        try {
          // Prepare the SQL statement with placeholders
          const placeholders = batch.map(()=>"(?, ?, ?)").join(",");
          const sql = `
            INSERT OR REPLACE INTO ${tableName} (id, text, metadata)
            VALUES ${placeholders}
          `;

          // Prepare the values array for binding
          const values = [];
          for(const chunk of batch) {
            // Validate each chunk"s required fields
            if(!chunk.id || typeof chunk.text !== "string" || !chunk.metadata) {
              console.error("❌ Invalid chunk detected:", {
                id: !!chunk.id,
                textType: typeof chunk.text,
                hasMetadata: !!chunk.metadata
              });
              throw new Error("Chunk text must be a string");
            }

            // Add values in the correct order
            values.push(
              chunk.id,                          // id
              chunk.text.trim() || "",           // text (ensure not empty)
              JSON.stringify(chunk.metadata)     // metadata
            );
          }

          // Validate the final values array
          const expectedLength = batch.length * 3; // 3 values per chunk
          if(values.length !== expectedLength) {
            console.error("❌ Invalid values array:", {
              expected: expectedLength,
              actual: values.length,
              batchSize: batch.length
            });
            throw new Error(`Invalid values array length: expected ${expectedLength}, got ${values.length}`);
          }

          // Additional validation for null/undefined
          const invalidValues = values.map((val, idx)=>({
            index: idx,
            valid: val !== null && val !== undefined
          })).filter(v=>!v.valid);

          if(invalidValues.length > 0) {
            console.error("❌ Null or undefined values detected at indices:", invalidValues);
            throw new Error("Cannot insert null or undefined values into D1");
          }

          // Log the SQL and values for debugging - TRIM THIS DOWN
          console.log("🔍 Preparing SQL:", {
            sql: sql.substring(0, 100) + "...", // Truncate SQL
            valueCount: values.length,
            firstChunkPreview: {
              id: values[0],
              textLength: values[1]?.length || 0,
              metadataLength: values[2]?.length || 0
            }
          });

          const stmt = this.env.DB.prepare(sql);
          if(!stmt) {
            throw new Error("Failed to prepare SQL statement");
          }

          // Execute the batch insert
          const result = await stmt.bind(...values).run();

          console.log(`✅ Successfully stored batch ${i / batchSize + 1}`, {
            success: result.success,
            meta: result.meta
          });
          break; // Success, exit retry loop

        }catch(error) {
          attempts++;
          console.error(`❌ Attempt ${attempts} failed:`, error);
          if(attempts === maxAttempts) {
            throw error;
          }
          console.warn(`⚠️ Retry ${attempts}/${maxAttempts} for batch ${i / batchSize + 1}`);
          await new Promise(resolve=>setTimeout(resolve, 1000 * attempts));
        }
      }
    }
  }

  private async ensureWorkflowMetadataTable(): Promise<void>{
    try {
      await this.env.DB.prepare(`
        CREATE TABLE IF NOT EXISTS workflow_metadata (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workflow_id TEXT NOT NULL,
          file_id TEXT NOT NULL,
          metadata TEXT NOT NULL,
          error TEXT,
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(workflow_id, file_id)
        )
      `).run();

      // Create indexes if they don"t exist
      await this.env.DB.prepare(`
        CREATE INDEX IF NOT EXISTS idx_workflow_metadata_workflow_id
        ON workflow_metadata(workflow_id)
      `).run();

      await this.env.DB.prepare(`
        CREATE INDEX IF NOT EXISTS idx_workflow_metadata_file_id
        ON workflow_metadata(file_id)
      `).run();

      console.log("✅ Workflow metadata table and indexes ensured");
    }catch(error) {
      console.error("❌ Error ensuring workflow metadata table:", error);
      throw error;
    }
  }

  async run(event: WorkflowEvent<ChunksVectorizedEvent>, step: WorkflowStep): Promise<void>{
    const { files, vectorizeConfig, instanceId } = event.payload;

    // Step 0: Normalize processor config and initialize metadata
    const initialSetup = await step.do("initializeWorkflow", async ()=>{
      const file = files[0]; // Extract the file from the event payload

      // Add debug logging for processor config
      console.log("Workflow processor config:", {
        fileId: file.fileId,
        processorConfig: file.processorConfig,
      });

      // Validate and normalize processor config
      const normalizeProcessorConfig = (processor: string, config: any)=>{
        if(processor === "unstructured") {
          return {
            skipDeJunk: config?.skipDeJunk ?? true,
            chunkingStrategy: config?.chunkingStrategy || "by_title",
            maxCharacters: config?.maxCharacters || 1024,
            splitPdfPage: config?.splitPdfPage ?? true,
            splitPdfConcurrencyLevel: config?.splitPdfConcurrencyLevel || 5,
            splitPdfAllowFailed: config?.splitPdfAllowFailed ?? true,
            minTokens: config?.minTokens,
            maxTokens: config?.maxTokens
          };
        } else if(processor === "openparse") {
          // Normalize OpenParse config to match the @divinci-ai/models interface
          // Create a config object with the OpenParseConfig properties
          // and add any additional properties needed for processing
          const openParseConfig = {
            // Standard OpenParseConfig properties
            semantic: config?.semantic_chunking ?? config?.openparse?.semantic ?? false,
            embeddingsProvider: config?.embeddings_provider || config?.openparse?.embeddings_provider || "ollama",
            useTokens: config?.openparse?.useTokens ?? true,
            minTokens: config?.openparse?.minTokens || config?.minTokens || 256,
            maxTokens: config?.openparse?.maxTokens || config?.maxTokens || 1024,
            chunkOverlap: config?.openparse?.chunkOverlap || 200,
            relevanceThreshold: config?.relevanceThreshold || 0.3,
            // Additional properties needed for processing
            skipDeJunk: config?.skipDeJunk ?? true
          };
          return openParseConfig;
        }
        return config;
      };

      const normalizedConfig = normalizeProcessorConfig(
        file.processor,
        file.processorConfig
      );

      // Initialize metadata
      const metadata = this.initializeMetadata({
        ...file,
        processorConfig: normalizedConfig
      });

      // Extract all necessary file information
      const fileInfo = {
        fileId: file.fileId,
        processor: file.processor,
        objectKey: file.objectKey,
        fileName: file.fileName,
        title: file.title,
        description: file.description,
        target: file.target,
        bucket: file.bucket, // Add the bucket property
        // Don't include formData directly as it's not serializable
        hasFormData: !!file.formData
      };

      return {
        normalizedConfig,
        metadata,
        fileInfo
      };
    });

    // Step 1: Ensure D1 table
    await step.do("ensureD1Table", async ()=>{
      console.log("🪲 Debug - Cloudflare credentials:", {
        accountId: this.env.CLOUDFLARE_ACCOUNT_ID,
        apiTokenLength: this.env.CLOUDFLARE_API_TOKEN?.length,
      });

      await Promise.all([
        this.ensureWorkflowMetadataTable(),
        ensureChunksTable(vectorizeConfig.whitelabelId, this.env.DB)
      ]);
    });

    // Step 1: Validate and get R2 file metadata only
    const r2Result: R2ValidationResult = await step.do("validateAndGetR2File", async ()=>{
      const { objectKey, fileName } = initialSetup.fileInfo;
      // Access formData directly from the event payload if needed
      // Add more logging to debug the formData issue
      console.log("🔍 Debug - files array:", {
        filesLength: files.length,
        file0: files[0] ? {
          fileId: files[0].fileId,
          hasFormData: !!files[0].formData,
          formDataType: files[0].formData ? typeof files[0].formData : "undefined"
        } : "undefined"
      });

      const formData = files[0]?.formData;
      const existingFile = await this.storageClient.get(objectKey);

      // In offline mode, we need to handle the case where the file doesn't exist in MinIO
      // and formData is not available
      if(!existingFile) {
        // In offline mode, if we don't have formData, we need to check if the file exists in R2
        // This is a special case for offline mode where we're processing a file that was uploaded
        // in a previous run but not yet stored in MinIO
        console.log("🔄 [OFFLINE MODE] File not found in MinIO, checking if it exists in R2...");

        // We'll continue with the workflow and let the uploadToR2 step handle the error if needed
        // This allows us to process files that were uploaded in a previous run
      }

      // If we're in offline mode and the file doesn't exist, log that we'll upload it
      if(!existingFile && formData) {
        console.log("🔄 [OFFLINE MODE] File not found in MinIO, will upload it in the next step");
      }

      console.log("1️⃣ step('validateAndGetR2File')::objectKey:", objectKey);
      console.log("1️⃣ step('validateAndGetR2File')::existingFile metadata:", {
        key: existingFile?.key,
        size: existingFile?.size,
        contentType: existingFile?.httpMetadata?.contentType,
        customMetadata: existingFile?.customMetadata
      });

      // Even if the file doesn't exist in MinIO, we still need to return a valid result
      // so that the workflow can continue and upload the file in the next step
      return {
        objectKey,
        fileName,
        hasExistingFile: !!existingFile,
        size: existingFile?.size || 0,
        mimeType: existingFile?.httpMetadata?.contentType || "application/octet-stream",
        customMetadata: existingFile?.customMetadata || {}
      };
    });

    // Step 2: Handle file upload if needed (as a separate step)
    if(!r2Result.hasExistingFile) {
      await step.do("uploadToR2", {
        retries: {
          limit: 3,
          delay: "5 seconds",
          backoff: "exponential"
        },
        timeout: "10 minutes"
      }, async ()=>{
        // Add more logging to debug the formData issue
        console.log("🔍 Debug - uploadToR2 files array:", {
          filesLength: files.length,
          file0: files[0] ? {
            // fileId: files[0].fileId,
            // hasFormData: !!files[0].formData,
            // formDataType: files[0].formData ? typeof files[0].formData : "undefined"
            file0: files[0]
          } : "undefined"
        });

        // In offline mode, we might not have formData available
        // This is a special case where we're processing a file that was uploaded
        // in a previous run but not yet stored in MinIO
        if(!files[0]?.formData) {
          console.log("🔄 [OFFLINE MODE] No formData available for upload, attempting to fetch from R2");

          try {
            // Try to get the file from R2
            const r2File = await this.env.R2.get(r2Result.objectKey);

            if(!r2File) {
              console.log("🔄 [OFFLINE MODE] File not found in R2 either, skipping upload step");
              return { uploaded: false, skipped: true };
            }

            console.log(`🔄 [OFFLINE MODE] Found file in R2, uploading to MinIO: ${r2Result.objectKey}`);

            // Get the file content
            const fileBuffer = await r2File.arrayBuffer();

            // Upload to MinIO using the storage client
            await this.storageClient.put(r2Result.objectKey, fileBuffer, {
              httpMetadata: {
                contentType: r2File.httpMetadata?.contentType || "application/octet-stream"
              },
              customMetadata: r2File.customMetadata || {}
            });

            console.log(`🔄 [OFFLINE MODE] Successfully uploaded file from R2 to MinIO: ${r2Result.objectKey}`);
            return { uploaded: true, size: fileBuffer.byteLength, fromR2: true };
          }catch(r2Error) {
            console.error(`❌ [OFFLINE MODE] Error fetching from R2: ${r2Error}`);
            return { uploaded: false, skipped: true, error: r2Error };
          }
        }

        const file = files[0].formData.get("file") as File;
        if(!file) {
          console.log("🔄 [OFFLINE MODE] No file found in FormData, skipping upload step");
          // Return a dummy result to indicate that we're skipping the upload
          return { uploaded: false, skipped: true };
        }

        try {
          // Get file buffer once to avoid multiple reads
          const fileBuffer = await file.arrayBuffer();

          // Add some logging
          console.log(`📤 Uploading file to R2: ${r2Result.objectKey}, size: ${fileBuffer.byteLength} bytes`);

          await this.storageClient.put(r2Result.objectKey, fileBuffer, {
            httpMetadata: {
              contentType: file.type || "application/octet-stream",
            },
            customMetadata: {
              fileName: r2Result.fileName,
              uploadedAt: new Date().toISOString(),
              size: fileBuffer.byteLength.toString()
            }
          });

          console.log(`✅ Successfully uploaded file to R2: ${r2Result.objectKey}`);
          return { uploaded: true, size: fileBuffer.byteLength };

        }catch(error) {
          console.error(`❌ R2 upload error for ${r2Result.objectKey}:`, error);

          // Check if it"s a connection error
          if(error.message.includes("connection") || error.message.includes("closed")) {
            // Make this error retryable
            throw new Error(`R2 connection error: ${error.message}`);
          }

          // For other errors, make them non-retryable
          throw new NonRetryableError(`R2 upload failed: ${error.message}`);
        }
      });
    }

    // Verify R2 upload result
    if(!r2Result.objectKey) {
      throw new Error("❌ R2 upload failed: No object key returned");
    }

    // Step 3: Create or get file record
    const fileRecord = await step.do("ensureFileRecord", async (): Promise<FileRecord>=>{
      console.log("🔍 Checking for existing file record:", {
        whitelabelId: vectorizeConfig.whitelabelId,
        objectKey: r2Result.objectKey
      });

      const existingRecord = await this.getFileRecord(
        vectorizeConfig.whitelabelId,
        r2Result.objectKey,
        vectorizeConfig.auth0Token
      );

      console.log("📝 Existing record check result:", {
        found: !!existingRecord,
        recordDetails: existingRecord ? {
          _id: existingRecord._id,
          objectKey: existingRecord.objectKey,
          status: existingRecord.status
        } : "none"
      });

      if(existingRecord) {
        const record = {
          status: "success",
          data: {
            _id: existingRecord._id,
            target: existingRecord.target || "",
            title: existingRecord.title || "",
            chunkingTool: existingRecord.chunkingTool || "",
            originalFilename: existingRecord.originalName,
            uploadTimestamp: existingRecord.uploadTimestamp || Date.now(),
            updateTimestamp: existingRecord.updateTimestamp || Date.now(),
            rawFileKey: existingRecord.objectKey,
            fileKey: existingRecord.objectKey,
            status: existingRecord.status || "success",
            vectorIndexProcessing: existingRecord.vectorIndexProcessing || [],
            chunks: existingRecord.chunks || [],
            __v: existingRecord.__v || 0
          }
        } as FileRecord;

        console.log("📄 Returning existing file record:", {
          _id: record.data._id,
          objectKey: record.data.fileKey,
          status: record.status
        });

        return record;
      }

      const payload: FileRecordPayload = {
        bucket: "default",
        objectKey: r2Result.objectKey,
        originalName: r2Result.fileName,
        chunkingTool: initialSetup.fileInfo.processor,
        title: initialSetup.fileInfo.title || r2Result.fileName,
        description: initialSetup.fileInfo.description
      };

      console.log("📤 Creating new file record with payload:", payload);

      const fileRecord = await this.createFileRecord(
        vectorizeConfig.whitelabelId,
        payload,
        vectorizeConfig.auth0Token
      );

      console.log("✅ File record creation response:", {
        success: !!fileRecord?.data?._id,
        recordDetails: fileRecord?.data ? {
          _id: fileRecord.data._id,
          objectKey: fileRecord.data.objectKey,
          status: fileRecord.status
        } : "failed"
      });

      if(!fileRecord?.data?._id) {
        console.error("❌ File record creation failed:", {
          objectKey: r2Result.objectKey,
          response: fileRecord
        });
        throw new NonRetryableError("File record creation failed: No file ID returned");
      }

      // Update fileInfo with the file ID from the created record
      initialSetup.fileInfo.fileId = fileRecord.data._id;

      console.log("✅ File record created and assigned:", {
        fileId: initialSetup.fileInfo.fileId,
        objectKey: r2Result.objectKey,
        status: "file to chunks",
        fullRecord: fileRecord
      });

      await this.upsertWorkflowMetadata(
        instanceId,
        fileRecord.data._id,
        initialSetup.metadata,
      );

      return fileRecord;
    });

    // Add debug logging before processing
    console.log("🔄 Starting document processing with:", {
      fileRecord: {
        id: fileRecord?.data?._id,
        fileKey: fileRecord?.data?.fileKey, // Changed from objectKey to fileKey
        status: fileRecord?.status
      },
      processor: initialSetup.fileInfo.processor,
      config: initialSetup.normalizedConfig,
    });

    // Step 4: Process document in batches
    const documentChunks = [];
    const batchSize = 50;

    // Initialize processor once outside the loop
    const processorType = initialSetup.fileInfo.processor?.toLowerCase() || "unstructured";
    console.log(`🔄 Initializing processor: ${processorType}`, {
      config: initialSetup.normalizedConfig,
      fileName: initialSetup.fileInfo.fileName
    });

    // Check if we're running in a local development environment
    const isLocalDevelopment = this.env.ENVIRONMENT === "local" || this.env.ENVIRONMENT === "development";

    console.log(`🔄 Environment detected: ${this.env.ENVIRONMENT}, using local mode: ${isLocalDevelopment}`);

    const processor = processorType === "openparse"
      ? new OpenParseProcessor(
          this.env.OPENPARSE_API_KEY,
          this.env.OPENPARSE_API_URL,
          this.env,
          {
            useStreaming: true,
            isLocalDevelopment: isLocalDevelopment
          }
        )
      : new UnstructuredProcessor(
          this.env.UNSTRUCTURED_WORKER_URL,
          { useStreaming: true }
        );

    try {
      // Initialize processing session
      let session = await step.do("initializeProcessing", async ()=>{
        // R2_BUCKET_URL now includes the protocol in wrangler.toml
        // For local development, we need to use the bucket name in the URL
        // The file is stored in the rag-origin-files-local bucket
        let fileUrl: string;

        // Extract the filename from the object key
        const pathParts = r2Result.objectKey.split("/");
        const filename = pathParts[pathParts.length - 1];

        // Extract the original filename without the timestamp
        const filenameWithoutTimestamp = filename.replace(/^\d+-/, "");

        if(isLocalDevelopment) {
          // In local development, use the bucket name in the URL and the normalized filename
          // IMPORTANT: For MinIO, we need to use the full object key with folder structure
          // We've updated the storage client to preserve folder structure
          // For OpenParse, we need to use a URL that the OpenParse service can access
          // Use the Docker service name that other containers can access
          // IMPORTANT: We need to properly encode the URL to handle special characters
          // But we need to make sure slashes are preserved as actual slashes, not %2F
          // This is because MinIO treats %2F differently than actual folder separators
          // IMPORTANT: We also need to remove any timestamp prefix from the filename
          // Extract the object key parts
          const keyParts = r2Result.objectKey.split("/");
          // Get the filename (last part)
          let filename = keyParts[keyParts.length - 1];
          // Remove timestamp prefix if it exists
          filename = filename.replace(/^\d+-/, "");
          // Replace the filename in the key parts
          keyParts[keyParts.length - 1] = filename;
          // Reconstruct the key without timestamp
          const keyWithoutTimestamp = keyParts.join("/");
          // Encode each segment properly
          const encodedKey = keyWithoutTimestamp.split("/").map(segment=>encodeURIComponent(segment)).join("/");

          // First, try to check if the file exists in MinIO with the current path
          console.log(`🔄 [LOCAL MODE] Checking if file exists in MinIO with current path: ${keyWithoutTimestamp}`);
          const fileExistsWithCurrentPath = await this.storageClient.get(keyWithoutTimestamp);

          if(fileExistsWithCurrentPath) {
            // If the file exists with the current path, use it
            fileUrl = `http://local-minio:9000/rag-origin-files-local/${encodedKey}`;
            console.log(`🔄 [LOCAL MODE] File exists in MinIO with current path, using: ${fileUrl}`);
          } else {
            // If the file doesn't exist with the current path, try to list all files in the bucket
            // and find one with the same filename but potentially different folder structure
            console.log(`🔄 [LOCAL MODE] File not found with current path, listing all files in bucket...`);
            const allFiles = await this.storageClient.list();

            // Look for files with the same filename
            const matchingFile = allFiles.objects?.find(obj=>{
              const objParts = obj.key.split("/");
              const objFilename = objParts[objParts.length - 1];
              // Compare with the filename (with or without timestamp)
              return objFilename === filename || objFilename.replace(/^\d+-/, "") === filename.replace(/^\d+-/, "");
            });

            if(matchingFile) {
              // If we found a matching file, use its key
              console.log(`🔄 [LOCAL MODE] Found matching file in MinIO: ${matchingFile.key}`);
              // Extract the parts of the matching file key
              const matchingKeyParts = matchingFile.key.split("/");
              // Get the filename (last part)
              let matchingFilename = matchingKeyParts[matchingKeyParts.length - 1];
              // Remove timestamp prefix if it exists
              matchingFilename = matchingFilename.replace(/^\d+-/, "");
              // Replace the filename in the key parts
              matchingKeyParts[matchingKeyParts.length - 1] = matchingFilename;
              // Reconstruct the key without timestamp
              const matchingKeyWithoutTimestamp = matchingKeyParts.join("/");
              // Encode each segment properly
              const matchingEncodedKey = matchingKeyWithoutTimestamp.split("/").map(segment=>encodeURIComponent(segment)).join("/");
              fileUrl = `http://local-minio:9000/rag-origin-files-local/${matchingEncodedKey}`;
              console.log(`🔄 [LOCAL MODE] Using matching file URL: ${fileUrl}`);
            } else {
              // If we couldn't find a matching file, use the original URL and log a warning
              fileUrl = `http://local-minio:9000/rag-origin-files-local/${encodedKey}`;
              console.warn(`⚠️ [LOCAL MODE] Could not find matching file in MinIO, using original URL: ${fileUrl}`);
            }
          }
          console.log(`🔄 [LOCAL MODE] Constructed file URL for OpenParse with bucket name: ${fileUrl}`);
          console.log(`🔄 [LOCAL MODE] Original object key: ${r2Result.objectKey}`);
          console.log(`🔄 [LOCAL MODE] Normalized filename: ${filenameWithoutTimestamp}`);

          // For local development, verify the file exists in MinIO before proceeding
          try {
            // Use the storage client to check if the file exists
            const fileExists = await this.storageClient.get(filenameWithoutTimestamp);
            if(!fileExists) {
              console.log(`🔄 [LOCAL MODE] File not found in MinIO with normalized name, trying to copy it...`);

              // Try to get the file with the original key
              const originalFile = await this.storageClient.get(r2Result.objectKey);

              if(originalFile && originalFile.body) {
                // Copy the file with the normalized name
                await this.storageClient.put(filenameWithoutTimestamp, originalFile.body);
                console.log(`🔄 [LOCAL MODE] Successfully copied file to normalized name: ${filenameWithoutTimestamp}`);
              } else {
                console.error(`❌ [LOCAL MODE] Original file not found in MinIO: ${r2Result.objectKey}`);

                // Try to list all objects in the bucket to find a similar file
                console.log(`🔄 [LOCAL MODE] Trying to find a similar file in MinIO...`);
                const objects = await this.storageClient.list();

                // Look for files with the same base name (without timestamp)
                const similarObject = objects.objects?.find(obj=>{
                  const objName = obj.key.split("/").pop() || "";
                  return objName.includes(filenameWithoutTimestamp) ||
                         objName.includes(filename.replace(/^\d+-/, ""));
                });

                if(similarObject) {
                  console.log(`🔄 [LOCAL MODE] Found similar file in MinIO: ${similarObject.key}`);
                  const similarFile = await this.storageClient.get(similarObject.key);

                  if(similarFile && similarFile.body) {
                    // Copy the file with the normalized name
                    await this.storageClient.put(filenameWithoutTimestamp, similarFile.body);
                    console.log(`🔄 [LOCAL MODE] Successfully copied similar file to normalized name: ${filenameWithoutTimestamp}`);
                  }
                } else {
                  console.error(`❌ [LOCAL MODE] No similar files found in MinIO`);
                }
              }
            } else {
              console.log(`🔄 [LOCAL MODE] File exists in MinIO with normalized name: ${filenameWithoutTimestamp}`);
            }
          }catch(error) {
            console.error(`❌ [LOCAL MODE] Error checking file existence:`, error);
          }
        } else {
          // In production, use the R2 URL directly
          fileUrl = `${this.env.R2_BUCKET_URL}/${r2Result.objectKey}`;
          console.log(`🔄 Constructed file URL for OpenParse: ${fileUrl}`);
        }

        console.log(`🔄 R2 Bucket URL from env: ${this.env.R2_BUCKET_URL}`);
        console.log(`🔄 Initializing processing for file: ${fileUrl}`, {
          processor: processorType,
          config: initialSetup.normalizedConfig  // Use normalized config from initialSetup
        });

        // In local development mode, the file should already be in MinIO
        // because this.storageClient.put() would have uploaded it there
        if(isLocalDevelopment) {
          console.log(`🔄 [LOCAL MODE] Using MinIO for storage in local development mode`);
          // The storage client abstraction handles the details
        }

        return await processor.initializeSession(
          initialSetup.normalizedConfig,  // Use normalized config from initialSetup
          fileUrl
        );
      });

      // Process batches
      let batchNumber = 0;
      const hasMoreChunks = true;
      while(hasMoreChunks) {
        const batch = await step.do(`processDocumentBatch_${batchNumber}`, {
          retries: {
            limit: 3,
            delay: "5 seconds",
            backoff: "exponential"
          },
          timeout: "30 minutes",
        }, async ()=>{
          console.log(`📄 Processing batch ${batchNumber} for file: ${r2Result.objectKey}`); // Use r2Result.objectKey

          try {
            const result = await processor.processBatch(
              session.sessionId,
              batchNumber,
              batchSize
            );

            // Add validation for empty or invalid results
            if(!result) {
              console.log(`🏁 No chunks in batch ${batchNumber} - assuming end of processing`);
              return null;
            }

            console.log(`✅ Successfully processed batch ${batchNumber} with ${result.length} chunks`);
            return result;
          }catch(error) {
            console.error(`❌ Error processing batch ${batchNumber}:`, error);
            if(error.message.includes("Session expired")) {
              console.log("🔄 Session expired, reinitializing...");

              // Use the same URL construction logic as in the initial session creation
              let fileUrl: string;

              // Extract the filename from the object key
              const pathParts = r2Result.objectKey.split("/");
              const filename = pathParts[pathParts.length - 1];

              // Extract the original filename without the timestamp
              const filenameWithoutTimestamp = filename.replace(/^\d+-/, "");

              if(isLocalDevelopment) {
                // IMPORTANT: For MinIO, we need to use the full object key with folder structure
                // We've updated the storage client to preserve folder structure
                // For OpenParse, we need to use a URL that the OpenParse service can access
                // Use the Docker service name that other containers can access
                // IMPORTANT: We need to properly encode the URL to handle special characters
                // But we need to make sure slashes are preserved as actual slashes, not %2F
                // This is because MinIO treats %2F differently than actual folder separators
                // IMPORTANT: We also need to remove any timestamp prefix from the filename
                // Extract the object key parts
                const keyParts = r2Result.objectKey.split("/");
                // Get the filename (last part)
                let filename = keyParts[keyParts.length - 1];
                // Remove timestamp prefix if it exists
                filename = filename.replace(/^\d+-/, "");
                // Replace the filename in the key parts
                keyParts[keyParts.length - 1] = filename;
                // Reconstruct the key without timestamp
                const keyWithoutTimestamp = keyParts.join("/");
                // Encode each segment properly
                const encodedKey = keyWithoutTimestamp.split("/").map(segment=>encodeURIComponent(segment)).join("/");

                // First, try to check if the file exists in MinIO with the current path
                console.log(`🔄 [LOCAL MODE] Checking if file exists in MinIO with current path: ${keyWithoutTimestamp}`);
                const fileExistsWithCurrentPath = await this.storageClient.get(keyWithoutTimestamp);

                if(fileExistsWithCurrentPath) {
                  // If the file exists with the current path, use it
                  fileUrl = `http://local-minio:9000/rag-origin-files-local/${encodedKey}`;
                  console.log(`🔄 [LOCAL MODE] File exists in MinIO with current path, using: ${fileUrl}`);
                } else {
                  // If the file doesn't exist with the current path, try to list all files in the bucket
                  // and find one with the same filename but potentially different folder structure
                  console.log(`🔄 [LOCAL MODE] File not found with current path, listing all files in bucket...`);
                  const allFiles = await this.storageClient.list();

                  // Look for files with the same filename
                  const matchingFile = allFiles.objects?.find(obj=>{
                    const objParts = obj.key.split("/");
                    const objFilename = objParts[objParts.length - 1];
                    // Compare with the filename (with or without timestamp)
                    return objFilename === filename || objFilename.replace(/^\d+-/, "") === filename.replace(/^\d+-/, "");
                  });

                  if(matchingFile) {
                    // If we found a matching file, use its key
                    console.log(`🔄 [LOCAL MODE] Found matching file in MinIO: ${matchingFile.key}`);
                    // Extract the parts of the matching file key
                    const matchingKeyParts = matchingFile.key.split("/");
                    // Get the filename (last part)
                    let matchingFilename = matchingKeyParts[matchingKeyParts.length - 1];
                    // Remove timestamp prefix if it exists
                    matchingFilename = matchingFilename.replace(/^\d+-/, "");
                    // Replace the filename in the key parts
                    matchingKeyParts[matchingKeyParts.length - 1] = matchingFilename;
                    // Reconstruct the key without timestamp
                    const matchingKeyWithoutTimestamp = matchingKeyParts.join("/");
                    // Encode each segment properly
                    const matchingEncodedKey = matchingKeyWithoutTimestamp.split("/").map(segment=>encodeURIComponent(segment)).join("/");
                    fileUrl = `http://local-minio:9000/rag-origin-files-local/${matchingEncodedKey}`;
                    console.log(`🔄 [LOCAL MODE] Using matching file URL: ${fileUrl}`);
                  } else {
                    // If we couldn't find a matching file, use the original URL and log a warning
                    fileUrl = `http://local-minio:9000/rag-origin-files-local/${encodedKey}`;
                    console.warn(`⚠️ [LOCAL MODE] Could not find matching file in MinIO, using original URL: ${fileUrl}`);
                  }
                }
                console.log(`🔄 [LOCAL MODE] Reinitializing with URL: ${fileUrl}`);

                // For local development, verify the file exists in MinIO before proceeding
                try {
                  // Use the storage client to check if the file exists
                  const fileExists = await this.storageClient.get(filenameWithoutTimestamp);
                  if(!fileExists) {
                    console.log(`🔄 [LOCAL MODE] File not found in MinIO with normalized name, trying to copy it...`);

                    // Try to get the file with the original key
                    const originalFile = await this.storageClient.get(r2Result.objectKey);

                    if(originalFile && originalFile.body) {
                      // Copy the file with the normalized name
                      await this.storageClient.put(filenameWithoutTimestamp, originalFile.body);
                      console.log(`🔄 [LOCAL MODE] Successfully copied file to normalized name: ${filenameWithoutTimestamp}`);
                    } else {
                      console.error(`❌ [LOCAL MODE] Original file not found in MinIO: ${r2Result.objectKey}`);

                      // Try to list all objects in the bucket to find a similar file
                      console.log(`🔄 [LOCAL MODE] Trying to find a similar file in MinIO...`);
                      const objects = await this.storageClient.list();

                      // Look for files with the same base name (without timestamp)
                      const similarObject = objects.objects?.find(obj=>{
                        const objName = obj.key.split("/").pop() || "";
                        return objName.includes(filenameWithoutTimestamp) ||
                               objName.includes(filename.replace(/^\d+-/, ""));
                      });

                      if(similarObject) {
                        console.log(`🔄 [LOCAL MODE] Found similar file in MinIO: ${similarObject.key}`);
                        const similarFile = await this.storageClient.get(similarObject.key);

                        if(similarFile && similarFile.body) {
                          // Copy the file with the normalized name
                          await this.storageClient.put(filenameWithoutTimestamp, similarFile.body);
                          console.log(`🔄 [LOCAL MODE] Successfully copied similar file to normalized name: ${filenameWithoutTimestamp}`);
                        }
                      } else {
                        console.error(`❌ [LOCAL MODE] No similar files found in MinIO`);
                      }
                    }
                  } else {
                    console.log(`🔄 [LOCAL MODE] File exists in MinIO with normalized name: ${filenameWithoutTimestamp}`);
                  }
                }catch(error) {
                  console.error(`❌ [LOCAL MODE] Error checking file existence:`, error);
                }
              } else {
                // In production, use the R2 URL directly
                fileUrl = `${this.env.R2_BUCKET_URL}/${r2Result.objectKey}`;
              }

              session = await processor.initializeSession(
                initialSetup.normalizedConfig,
                fileUrl,
              );
              return processor.processBatch(
                session.sessionId,
                batchNumber,
                batchSize,
              );
            }
            throw error;
          }
        });

        if(!batch) {
          console.log(`🏁 No more chunks to process after batch ${batchNumber}`);
          break;
        }

        documentChunks.push(...batch);
        console.log(`📊 Chunks accumulated: ${documentChunks.length} (added ${batch.length})`);

        batchNumber++;
      }
    } finally {
      // Ensure we clean up resources
      console.log(`📊 pre-processor.dispose documentChunks chunks preview: ${documentChunks[0]}`);
      await processor.dispose();
      console.log(`📊 post-processor.dispose documentChunks chunks preview: ${documentChunks[0]}`);
    }

    // Add final count logging
    console.log(`📊 Total chunks received: ${documentChunks.length}`);

    // Simple validation and transformation
    const validatedChunks = [];

    for(let index = 0; index < documentChunks.length; index++) {
      const chunk = documentChunks[index];

      // Handle both string chunks and object chunks with text property
      if(typeof chunk === "string" && chunk.trim().length > 0) {
        // Handle string chunks
        validatedChunks.push({
          id: createVectorId(initialSetup.fileInfo.fileId, index),
          text: chunk.trim(),
          metadata: {
            whiteLabelId: vectorizeConfig.whitelabelId,
            originalName: initialSetup.fileInfo.fileName,
            vectorId: createVectorId(initialSetup.fileInfo.fileId, index),
            fileObjectKey: initialSetup.fileInfo.objectKey,
            tokenCount: countTokens(chunk)
          }
        });
      } else if(chunk && typeof chunk === "object" && typeof chunk.text === "string" && chunk.text.trim().length > 0) {
        // Handle object chunks with text property
        // Use existing metadata if available and merge with required fields
        const existingMetadata = chunk.metadata || {};

        validatedChunks.push({
          id: createVectorId(initialSetup.fileInfo.fileId, index),
          text: chunk.text.trim(),
          metadata: {
            whiteLabelId: vectorizeConfig.whitelabelId || existingMetadata.whiteLabelId,
            originalName: initialSetup.fileInfo.fileName,
            vectorId: createVectorId(initialSetup.fileInfo.fileId, index),
            fileObjectKey: initialSetup.fileInfo.objectKey,
            tokenCount: existingMetadata.tokenCount || countTokens(chunk.text),
            ...existingMetadata // Include any other metadata fields
          }
        });
      }
    }

    if(validatedChunks.length === 0) {
      throw new NonRetryableError("No valid chunks were generated during document processing");
    }

    console.log("✅ Chunks processed:", {
      original: documentChunks.length,
      valid: validatedChunks.length,
      sample: validatedChunks[0].text.substring(0, 100)
    });

    // Step 5: DeJunk Filtering
    const skipDeJunk = initialSetup.normalizedConfig?.skipDeJunk;
    console.log("🚮⚙️ DeJunk configuration:", { skipDeJunk });

    let filteredChunks: any[];
    if(skipDeJunk) {
      console.log("🔄 Skipping DeJunk filtering");
      filteredChunks = validatedChunks;

      initialSetup.metadata.steps.deJunk.skipped = true;
      initialSetup.metadata.steps.deJunk.chunksBeforeDeJunk = validatedChunks.length;
      initialSetup.metadata.steps.deJunk.chunksAfterDeJunk = validatedChunks.length;
      initialSetup.metadata.steps.deJunk.duration = 0;
    } else {
      const deJunkStart = Date.now();
      filteredChunks = await step.do("filterChunks", async ()=>{
        initialSetup.metadata.steps.deJunk.chunksBeforeDeJunk = validatedChunks.length;

        console.log("🔄 Applying relevance filtering...");
        const relevanceThreshold = getRelevanceThreshold(initialSetup.normalizedConfig);

        const processedChunks = await this.filterRelevantChunks(validatedChunks, relevanceThreshold);

        if(!processedChunks?.length) {
          throw new NonRetryableError("No chunks remained after filtering");
        }

        initialSetup.metadata.steps.deJunk.chunksAfterDeJunk = processedChunks.length;
        initialSetup.metadata.steps.deJunk.duration = Date.now() - deJunkStart;

        return processedChunks;
      });
    }

    // Step: Store chunks in D1
    await step.do("storeChunksInD1", async ()=>{
      console.log(`📝 Storing ${filteredChunks.length} chunks in D1`);

      // Pass the chunks and whitelabelId to storeChunksInD1
      await this.storeChunksInD1(
        filteredChunks,
        vectorizeConfig.whitelabelId
      );
    });

    // Step 6: Generate Embeddings and Store Vectors
    const vectorStep = await step.do("findOrCreateVectorIndex", async ()=>{
      const vectorizationStart = Date.now();
      const vectorIndex = `vector-index-${vectorizeConfig.ragId}`;

      // Ensure vector index exists first
      console.log(`🔄 Ensuring vector index exists: ${vectorIndex}`);
      await findOrCreateVectorizeIndex(
        vectorIndex,
        `🔢 Vectors for chunks \n\n vectorIndex: ${vectorIndex}`,
        this.env.CLOUDFLARE_ACCOUNT_ID,
        this.env.CLOUDFLARE_API_TOKEN,
        this.env.CLOUDFLARE_API_URL || "https://api.cloudflare.com/client/v4"
      );

      return { vectorIndex, vectorizationStart };
    });

    await step.do("generateAndStoreEmbeddings", async ()=>{
      const vectorizeApi = new VectorizeAPI({
        accountId: this.env.CLOUDFLARE_ACCOUNT_ID,
        apiToken: this.env.CLOUDFLARE_API_TOKEN,
        ragName: "default"
      });

      // Process chunks in smaller batches
      const batchSize = 25;
      for(let i = 0; i < filteredChunks.length; i += batchSize) {
        const batch = filteredChunks.slice(i, batchSize);

        console.log("🔄 Processing batch for embeddings", {
          batchIndex: i / batchSize + 1,
          batchSize: batch.length,
          fileId: initialSetup.fileInfo.fileId,
          vectorIndex: vectorStep.vectorIndex
        });

        // Validate chunk data before processing
        const validBatch = batch.map((chunk, index)=>({
          id: createVectorId(initialSetup.fileInfo.fileId, index + (i * batchSize)),
          text: chunk.text,
          metadata: {
            ...chunk.metadata,
            whiteLabelId: vectorizeConfig.whitelabelId,
            originalName: initialSetup.fileInfo.fileName,
            vectorId: createVectorId(initialSetup.fileInfo.fileId, index + (i * batchSize)),
            fileObjectKey: initialSetup.fileInfo.objectKey,
            tokenCount: chunk.metadata?.tokenCount || countTokens(chunk.text)
          }
        })).filter(chunk=>{
          const isValid = chunk.id && chunk.text && chunk.metadata;
          if(!isValid) {
            console.warn("⚠️ Invalid chunk detected:", {
              hasId: !!chunk.id,
              hasText: !!chunk.text,
              hasMetadata: !!chunk.metadata
            });
          }
          return isValid;
        });

        if(validBatch.length === 0) {
          console.warn("⚠️ No valid chunks in batch, skipping");
          continue;
        }

        try {
          const vectorsWithEmbeddings = await Promise.all(
            validBatch.map(async (chunk)=>{
              // Define possible embedding result types
              interface EmbeddingResult {
                values?: number[],
                embedding?: number[],
                shape?: [number, number],
                data?: number[][],
              }

              const embeddingResult = await this.env.AI.run("@cf/baai/bge-base-en-v1.5", {
                text: chunk.text
              });

              // Log the embedding result to debug
              console.log("Embedding result for chunk:", {
                chunkId: chunk.id,
                embeddingType: typeof embeddingResult,
                embeddingValue: embeddingResult,
                isArray: Array.isArray(embeddingResult)
              });

              // Handle the new format with shape and data properties
              let embedding: number[];
              if(Array.isArray(embeddingResult)) {
                embedding = embeddingResult;
              } else if((embeddingResult as EmbeddingResult).values) {
                embedding = (embeddingResult as EmbeddingResult).values;
              } else if((embeddingResult as EmbeddingResult).embedding) {
                embedding = (embeddingResult as EmbeddingResult).embedding;
              } else if((embeddingResult as EmbeddingResult).data?.[0]) {
                // Extract the first row from the data array
                embedding = (embeddingResult as EmbeddingResult).data[0];
              } else {
                throw new Error(`Invalid embedding format received: ${JSON.stringify(embeddingResult)}`);
              }

              if(!Array.isArray(embedding)) {
                throw new Error(`Failed to extract embedding array from result: ${JSON.stringify(embeddingResult)}`);
              }

              return {
                id: chunk.id,
                values: embedding,
                metadata: {
                  whiteLabelId: vectorizeConfig.whitelabelId,
                  originalName: initialSetup.fileInfo.fileName,
                  vectorId: chunk.id,
                  fileObjectKey: initialSetup.fileInfo.objectKey,
                  tokenCount: chunk.metadata.tokenCount || countTokens(chunk.text),
                  tags: chunk.metadata.tags,
                }
              };
            })
          );

          await vectorizeApi.upsert(vectorStep.vectorIndex, vectorsWithEmbeddings);

          console.log("✅ Successfully processed and stored batch", {
            batchIndex: i / batchSize + 1,
            totalBatches: Math.ceil(filteredChunks.length / batchSize)
          });
        }catch(error) {
          console.error("❌ Failed to process batch", {
            error: error.message,
            stack: error.stack,
            batchIndex: i / batchSize + 1,
          });
          throw error;
        }
      }

      return {
        status: "vectorization_complete",
        totalChunks: filteredChunks.length,
        fileId: initialSetup.fileInfo.fileId,
      };
    });

    // Store final metadata
    initialSetup.metadata.endTime = Date.now();
    initialSetup.metadata.totalDuration = initialSetup.metadata.endTime - initialSetup.metadata.startTime;

    await this.env.DB.prepare(`
      INSERT INTO workflow_metadata (
        workflow_id,
        file_id,
        metadata,
        created_at
      ) VALUES (?, ?, ?, datetime("now"))
      ON CONFLICT(workflow_id, file_id)
      DO UPDATE SET
        metadata = excluded.metadata,
        created_at = datetime("now")
    `).bind(
      instanceId,
      initialSetup.fileInfo.fileId,
      JSON.stringify(initialSetup.metadata),
    ).run();

    // Ensure filteredChunks is not empty and properly formatted
    if(!filteredChunks?.length) {
      console.error("❌ No chunks to process");
      throw new NonRetryableError("No chunks to process");
    }

    await this.updateFileStatus(
      vectorizeConfig.whitelabelId,
      initialSetup.fileInfo.fileId,
      RagVectorTextChunksStatus.EDITING,
      vectorizeConfig.auth0Token,
    );

    // Add chunks
    const CHUNK_BATCH_SIZE = 50; // Adjust based on your payload size limits
    console.log(`🔄 Adding chunks in batches of ${CHUNK_BATCH_SIZE}`);

    // Process chunks in smaller batches to avoid "request entity too large" errors
    for(let i = 0; i < filteredChunks.length; i += CHUNK_BATCH_SIZE) {
      const chunkBatch = filteredChunks.slice(i, i + CHUNK_BATCH_SIZE);
      console.log(`🔄 Adding chunks batch ${Math.floor(i/CHUNK_BATCH_SIZE) + 1}/${Math.ceil(filteredChunks.length/CHUNK_BATCH_SIZE)} (${chunkBatch.length} chunks)`);

      const addChunksResponse = await fetch(
        `${this.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/${initialSetup.fileInfo.fileId}/chunks/bulk`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${vectorizeConfig.auth0Token}`,
          },
          body: JSON.stringify({
            chunks: chunkBatch.map(chunk=>({
              text: chunk.text,
              tags: chunk.metadata?.tags || [],
            }))
          })
        }
      );

      if(!addChunksResponse.ok) {
        const error = await addChunksResponse.json();
        console.error("❌ Failed to add chunks batch:", error, {
          batchIndex: Math.floor(i/CHUNK_BATCH_SIZE) + 1,
          batchSize: chunkBatch.length,
        });

        // Update status to FAILED
        await this.updateFileStatus(
          vectorizeConfig.whitelabelId,
          initialSetup.fileInfo.fileId,
          RagVectorTextChunksStatus.FAILED_TO_CHUNK,
          vectorizeConfig.auth0Token,
        );

        throw new Error(`Failed to add chunks batch: ${JSON.stringify(error)}`);
      }

      console.log(`✅ Successfully added chunks batch ${Math.floor(i/CHUNK_BATCH_SIZE) + 1}/${Math.ceil(filteredChunks.length/CHUNK_BATCH_SIZE)}`);
    }

    // Only update to COMPLETED after successful chunk addition
    await this.updateFileStatus(
      vectorizeConfig.whitelabelId,
      initialSetup.fileInfo.fileId,
      RagVectorTextChunksStatus.COMPLETED,
      vectorizeConfig.auth0Token
    );

    // After successful vector storage and status update
    if(vectorizeConfig.ragId) {
      await this.addFileToCustomRag(
        vectorizeConfig.whitelabelId,
        vectorizeConfig.ragId,
        initialSetup.fileInfo.fileId,
        vectorizeConfig.auth0Token
      );
    }

    // Update workflow metadata
    await this.upsertWorkflowMetadata(
      instanceId,
      initialSetup.fileInfo.fileId,
      initialSetup.metadata,
    );

    // Don"t return anything (void)
  }

  private async filterRelevantChunks(
    chunks: Array<{ text: string, metadata?: any }>,
    relevanceThreshold: number
  ): Promise<Array<{ text: string, metadata?: any }>>{
    if(!chunks.length) {
      return [];
    }

    const batchResults = [];

    // Process chunks in batches of FILTER_BATCH_SIZE
    for(let i = 0; i < chunks.length; i += FILTER_BATCH_SIZE) {
      const batch = chunks.slice(i, i + FILTER_BATCH_SIZE);

      try {
        const sentiments = await Promise.all(
          batch.map(chunk=>this.env.AI.run("@cf/huggingface/distilbert-sst-2-int8", {
              text: chunk.text
            })
          )
        );

        // Filter chunks based on positive sentiment score
        const filteredBatch = batch.filter((_, index)=>{
          const sentiment = sentiments[index];
          // Check if sentiment has the expected structure
          if(sentiment && Array.isArray(sentiment) && sentiment.length === 2) {
            const positiveScore = sentiment[1];
            return positiveScore >= relevanceThreshold;
          }
          // If sentiment structure is unexpected, keep the chunk by default
          return true;
        });

        batchResults.push(...filteredBatch);
      }catch(error) {
        console.error("Error during sentiment analysis:", error);
        // In case of error, keep all chunks from this batch
        batchResults.push(...batch);
      }
    }

    return batchResults;
  }

  private async updateFileStatus(
    whitelabelId: string,
    fileId: string,
    status: RagVectorTextChunksStatus,
    auth0Token: string
  ): Promise<void>{
    console.log(`🔄 Updating file status: ${fileId} -> ${status}`);

    if(!fileId) {
      console.error("❌ Cannot update file status: Invalid file ID (empty)");
      return;
    }

    try {
      const response = await fetch(
        `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/${fileId}/status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${auth0Token}`,
          },
          body: JSON.stringify({ status })
        }
      );

      if(!response.ok) {
        const errorData = await response.json();
        console.error(`❌ Failed to update file status:`, errorData);
        throw new Error(`❌ Failed to update file status: ${JSON.stringify(errorData)}`);
      }

      console.log(`✅ File status updated successfully: ${fileId} -> ${status}`);
    }catch(error) {
      console.error(`❌ Error updating file status:`, error);
      throw error;
    }
  }

  private async addFileToCustomRag(
    whitelabelId: string,
    ragId: string,
    fileId: string,
    auth0Token: string
  ): Promise<void>{
    console.log(`📝 Linking file ${fileId} to custom RAG ${ragId}`);

    // First, add to pending (using updatePendingAtomic)
    const pendingResponse = await fetch(
      `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/pending-file`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${auth0Token}`,
        },
        body: JSON.stringify({ fileId })
      }
    );

    if(!pendingResponse.ok) {
      const error = await pendingResponse.json();
      console.error("Failed to add file to pending:", error);
      throw new Error(`Failed to add file to pending: ${JSON.stringify(error)}`);
    }

    // After vectors are stored, move from pending to success
    const successResponse = await fetch(
      `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/success-file`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${auth0Token}`,
        },
        body: JSON.stringify({ fileId })
      }
    );

    if(!successResponse.ok) {
      const error = await successResponse.json();
      console.error("Failed to move file to success:", error);
      throw new Error(`Failed to move file to success: ${JSON.stringify(error)}`);
    }

    console.log(`✅ Successfully linked file ${fileId} to RAG ${ragId}`);
  }

  /**
   * Generate a presigned URL for a file in MinIO
   * @param bucketName The name of the bucket
   * @param objectKey The key of the object
   * @param expirySeconds The number of seconds until the URL expires
   * @returns A presigned URL for the object
   */
  private async generatePresignedUrl(bucketName: string, objectKey: string, expirySeconds: number = 3600): Promise<string | null>{
    try {
      // For local development, we'll use the MinIO client to generate a presigned URL
      // This URL will be valid for the specified number of seconds
      // The URL will allow anyone to GET the object without authentication

      // First, check if the file exists
      const fileExists = await this.storageClient.get(objectKey);
      if(!fileExists) {
        console.log(`🔄 [OFFLINE MODE] File not found in MinIO: ${objectKey}`);
        return null;
      }

      // Use the MinIO client to generate a presigned URL
      // Since we don't have direct access to the MinIO client, we'll construct a URL
      // that will work with the MinIO server
      const presignedUrl = `${this.env.R2_BUCKET_URL}/${bucketName}/${encodeURIComponent(objectKey)}`;
      console.log(`🔄 [OFFLINE MODE] Generated presigned URL: ${presignedUrl}`);
      return presignedUrl;
    }catch(error) {
      console.error(`❌ [OFFLINE MODE] Error generating presigned URL:`, error);
      return null;
    }
  }

  private async upsertWorkflowMetadata(
    workflowId: string,
    fileId: string,
    metadata: WorkflowMetadata,
    error?: string
  ): Promise<void>{
    try {
      // First try to delete any existing record
      await this.env.DB.prepare(`
        DELETE FROM workflow_metadata
        WHERE workflow_id = ? AND file_id = ?
      `).bind(workflowId, fileId).run();

      // Then insert the new record
      await this.env.DB.prepare(`
        INSERT INTO workflow_metadata (
          workflow_id,
          file_id,
          metadata,
          error,
          created_at
        ) VALUES (?, ?, ?, ?, datetime("now"))
      `).bind(
        workflowId,
        fileId,
        JSON.stringify(metadata),
        error || null
      ).run();

      console.log(`✅ Workflow metadata upserted for workflow ${workflowId}, file ${fileId}`);
    }catch(error) {
      console.error("❌ Error upserting workflow metadata:", error);
      // Log but don"t throw - this shouldn"t fail the whole workflow
      console.warn("Continuing workflow despite metadata upsert failure");
    }
  }

  // Helper function to make API requests
  private async makeRequest(url: string, method: string, body?: any, token?: string){
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    if(token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Clone the body if it"s a Response object
    const bodyToSend = body instanceof Response ?
      JSON.stringify(await body.clone().json()) :
      body ? JSON.stringify(body) : undefined;

    const response = await fetch(url, {
      method,
      headers,
      body: bodyToSend
    });

    // Log the request details
    console.log(`📡 API Request [${method}] ${url}:`, {
      headers,
      body: bodyToSend ? JSON.parse(bodyToSend) : undefined
    });

    if(!response.ok) {
      const errorData = await response.json().catch(()=>null);
      console.error("❌ Request failed:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      throw new Error(`Request failed: ${response.status} ${response.statusText}`);
    }

    return response;
  }

  // Helper method to get file record
  private async getFileRecord(whitelabelId: string, objectKey: string, auth0Token: string){
    try {
      const url = `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/get-record?objectKey=${objectKey}`;
      console.log("🔍 Attempting to get file record from:", url);

      const response = await this.makeRequest(url, "GET", undefined, auth0Token);

      console.log("📡 Get file record response status:", response.status);

      if(!response.ok) {
        console.warn("❌ Failed to get file record. Status:", response.status);
        const errorText = await response.text();
        console.warn("Error response:", errorText);
        return null;
      }

      const record = await response.json() as FileRecordResponse;
      console.log("✅ Got file record:", record);

      // Store the record in the class
      this.fileRecord = record?.data || null;

      return this.fileRecord;
    }catch(error) {
      console.error("💥 Error in getFileRecord:", error);
      return null;
    }
  }

  private async createFileRecord(
    whitelabelId: string,
    fileData: {
      bucket: string,
      objectKey: string,
      originalName: string,
      chunkingTool: string,
      title?: string,
      description?: string,
    },
    auth0Token: string
  ): Promise<any>{
    const url = `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/create-record`;

    console.log("📤 Attempting to create file record:", {
      url,
      whitelabelId,
      objectKey: fileData.objectKey
    });

    try {
      // Use type assertion to handle Cloudflare-specific options
      const fetchOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${auth0Token}`,
        },
        body: JSON.stringify(fileData),
        // Add CF worker specific options
        cf: {
          cacheTtl: 0, // Don"t cache this request
          cacheEverything: false
        }
      } as RequestInit;

      const response = await fetch(url, fetchOptions);

      if(!response.ok) {
        const errorData = await response.json().catch(()=>null);
        console.error("❌ Failed to create file record:", {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        throw new Error(`Failed to create file record: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log("✅ File record created successfully:", result);
      return result;
    }catch(error) {
      console.error("💥 Error in createFileRecord:", {
        error: error.message,
        whitelabelId,
        objectKey: fileData.objectKey
      });
      throw error;
    }
  }

  private initializeMetadata(file: any): WorkflowMetadata{
    return {
      startTime: Date.now(),
      steps: {
        r2Processing: {
          duration: 0,
          originalChunks: 0
        },
        chunking: {
          duration: 0,
          originalChunks: 0,
          processor: file.processor || "unstructured",
          success: false,
          error: undefined
        },
        deJunk: {
          duration: 0,
          chunksBeforeDeJunk: 0,
          chunksAfterDeJunk: 0,
          skipped: false,
          relevanceThreshold: file.processorConfig?.relevanceThreshold || 0.3
        },
        vectorization: {
          duration: 0,
          totalBatches: 0,
          totalVectors: 0,
          batchSize: EMBEDDING_BATCH_SIZE,
          embeddingsProvider: file.processorConfig?.embeddings_provider || "cloudflare"
        }
      },
      fileInfo: {
        fileId: file.fileId,
        fileName: file.fileName,
        processor: file.processor,
        processorConfig: file.processorConfig,
        size: 0,
        mimeType: ""
      },
      status: "completed"
    };
  }

}
