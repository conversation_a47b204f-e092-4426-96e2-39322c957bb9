import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./test/setup-comprehensive.js'],
    include: ['**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*',
      '**/boundary/edge-cases.test.js',
      '**/boundary/workflow-boundaries.test.js',
      '**/test/boundary/edge-cases.test.js',
      '**/test/boundary/workflow-boundaries.test.js'
    ],
    alias: {
      'cloudflare:workers': resolve(__dirname, './test/mocks/cloudflare-workers.js')
    }
  },
  resolve: {
    alias: {
      'cloudflare:workers': resolve(__dirname, './test/mocks/cloudflare-workers.js')
    }
  }
});
