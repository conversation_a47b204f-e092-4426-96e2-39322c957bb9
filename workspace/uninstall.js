#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// Base paths
const BASEPATH = path.resolve(__dirname, "..");
console.log(`🚧 Debug: BASEPATH = ${BASEPATH}`);

// Log process.argv to inspect what arguments are being passed
console.log(`🚧 Debug: process.argv = ${process.argv}`);

// Function to run npx glob to search for files
function runGlobSync(pattern, options = {}) {
  try {
    console.log(`🚧 Running npx glob with pattern: ${pattern}`);
    // Use --nodir with ! to get only directories
    const globOptions = options.onlyDirectories ? '-- "!*.*"' : '';
    const ignorePattern = options.ignore ? options.ignore.map(p => `--ignore "${p}"`).join(' ') : '';
    const command = `npx glob "${pattern}" ${ignorePattern} ${globOptions}`;
    console.log(`🚧 Debug: Running command: ${command}`);
    const output = execSync(command, { encoding: "utf8" });
    return output.trim().split("\n").filter(Boolean);
  } catch (err) {
    console.error(`❌ Error running npx glob: ${err}`);
    console.error(`❌ It seems 'glob' is not installed globally. Please install it using: pnpm add -g glob`);
    process.exit(1);
  }
}

// Function to run npx js-yaml to load YAML files
function runYamlParseSync(filePath) {
  try {
    console.log(`🚧 Running npx js-yaml on: ${filePath}`);
    const output = execSync(`npx js-yaml "${filePath}"`, { encoding: "utf8" });
    return JSON.parse(output);
  } catch (err) {
    console.error(`❌ Error running npx js-yaml: ${err}`);
    process.exit(1);
  }
}

// Function to clean a directory by removing node_modules, lockfiles, dist, and pnpm-store
function cleanDirectory(directory) {
  console.log(`🚧 Debug: Cleaning directory ${directory}`);

  const nodeModulesPath = path.join(directory, "node_modules");
  const pnpmLockFile = path.join(directory, "pnpm-lock.yaml");
  const packageLockFile = path.join(directory, "package-lock.json");
  const distFolder = path.join(directory, "dist");

  // Remove node_modules
  if (fs.existsSync(nodeModulesPath)) {
    console.log(`⚠️ Removing node_modules : ${directory}`);
    fs.rmSync(nodeModulesPath, { recursive: true, force: true });
  } else {
    console.log(`🚫 No node_modules found in: ${directory}`);
  }

  // Remove lockfiles
  if (fs.existsSync(pnpmLockFile)) {
    console.log(`⚠️ Removing pnpm-lock.yaml in: ${directory}`);
    fs.rmSync(pnpmLockFile, { force: true });
  } else if (fs.existsSync(packageLockFile)) {
    console.log(`⚠️ Removing package-lock.json in: ${directory}`);
    fs.rmSync(packageLockFile, { force: true });
  } else {
    console.log(`🚫 No lockfiles found in: ${directory}`);
  }

  // Remove dist folder
  if (fs.existsSync(distFolder)) {
    console.log(`⚠️ Removing dist folder in: ${directory}`);
    fs.rmSync(distFolder, { recursive: true, force: true });
  } else {
    console.log(`🚫 No dist folder found in: ${directory}`);
  }

  // Use npx glob to find and remove .pnpm-store directories
  const storePaths = runGlobSync(`${directory}/**/.pnpm-store`);
  storePaths.forEach((storePath) => {
    console.log(`⚠️ Removing .pnpm-store in: ${storePath}`);
    try {
      fs.rmSync(storePath, { recursive: true, force: true });
    } catch (err) {
      console.error(`❌ Error removing ${storePath}: ${err.message}`);
    }
  });
}

// Function to parse the pnpm-workspace.yaml and clean each package
function cleanWorkspacePackages() {
  const workspaceFile = path.join(BASEPATH, "pnpm-workspace.yaml");

  if (!fs.existsSync(workspaceFile)) {
    console.error("❌ Error: pnpm-workspace.yaml not found");
    process.exit(1);
  }

  console.log(`🚧 Parsing workspace packages from ${workspaceFile}`);

  // Read package.json instead of pnpm-workspace.yaml since it's more reliable
  const packageJson = require(path.join(BASEPATH, 'package.json'));
  const workspacePatterns = packageJson.workspaces.packages;

  if (!workspacePatterns || workspacePatterns.length === 0) {
    console.log("🚫 No workspace packages found in package.json");
    return;
  }

  workspacePatterns.forEach((pattern) => {
    console.log(`🚧 Debug: Processing workspace pattern: ${pattern}`);
    // Use glob to match directories
    const directories = runGlobSync(path.join(BASEPATH, pattern), { 
      onlyDirectories: true,
      ignore: ['**/node_modules/**', '**/dist/**', '**/*.{js,ts,jsx,tsx,css,scss,json,md}']
    });
    
    directories.forEach((directory) => {
      // Only clean if it contains a package.json
      if (fs.existsSync(directory) && fs.existsSync(path.join(directory, 'package.json'))) {
        cleanDirectory(directory);
      }
    });
  });
}

// Function to clean root directory
function cleanRoot() {
  console.log(
    "----- ⬇️  Uninstalling Root Dependencies and Cleaning Root Artifacts ⬇️  -----",
  );
  cleanDirectory(BASEPATH);
  cleanDirectory(path.join(BASEPATH, "./workspace/resources"));
}

// Function to run pnpm store prune
function prunePnpmStore() {
  console.log("🧹 Running pnpm store prune to clean up unused cached packages...");
  try {
    execSync("pnpm store prune", { stdio: "inherit" });
    console.log("✅ pnpm store prune completed successfully.");
  } catch (err) {
    console.error(`❌ Error running pnpm store prune: ${err.message}`);
  }
}

// Main function to handle uninstall logic
function main() {
  const args = process.argv.slice(2); // Skip the first two elements (node and script path)
  console.log(`🚧 Debug: Arguments = ${args}`);

  const uninstallType = args[0] || "--all";

  if (uninstallType === "--all" || uninstallType === "--ci") {
    cleanRoot();
    cleanWorkspacePackages();
  } else if (uninstallType === "--root") {
    cleanRoot();
  } else if (uninstallType === "--workspace") {
    cleanWorkspacePackages();
  } else {
    console.error(`❌ Unknown uninstall type: ${uninstallType}`);
    process.exit(1);
  }

  // Prune pnpm store at the end of the uninstall process
  prunePnpmStore();

  console.log("✅ Uninstall script completed successfully.");
}

main();
