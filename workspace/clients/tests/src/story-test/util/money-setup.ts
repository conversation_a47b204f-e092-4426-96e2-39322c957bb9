import { AuthFetch } from "../../globals/auth0";
import { API_TEST_ENDPOINT_ORIGIN } from "../../constants/internal";
import { getUsersBalance } from "@divinci-ai/actions";
import { fetchBody, handleFetch } from "@divinci-ai/utils";

/**
 * Add funds to a user's NanoUSD wallet for testing (server-side operation)
 */
async function addFundsToUser(
  fetcher: typeof fetch, userId: string, amountInNanoDollars: bigint
): Promise<void>{
  await handleFetch(fetcher(
    `${API_TEST_ENDPOINT_ORIGIN}/money/${userId}/add-funds`,
    fetchBody("POST", { funds: amountInNanoDollars.toString() })
  ));
}

/**
 * Clear a user's NanoUSD wallet by setting balance to zero
 */
async function clearUserWallet(
  fetcher: typeof fetch, userId: string
): Promise<void>{
  await handleFetch(fetcher(
    `${API_TEST_ENDPOINT_ORIGIN}/money/${userId}/wallet`,
    { method: "DELETE" }
  ));
}

/**
 * Set exact funds for a user by clearing their wallet and adding the specified amount
 * This ensures the user has exactly the amount specified, regardless of previous balance
 */
export async function setFundsForUser(
  fetcher: typeof fetch, userId: string, amountInNanoDollars: bigint
): Promise<void>{
  // First clear the user's wallet completely
  await clearUserWallet(fetcher, userId);

  // Then add the exact amount specified
  if(amountInNanoDollars > 0n) {
    await addFundsToUser(fetcher, userId, amountInNanoDollars);
  }
}


/**
 * Get user's current balance using the actions
 */
export async function getUserBalance(userFetch: AuthFetch): Promise<bigint>{
  const result = await getUsersBalance(userFetch);
  return result.balance;
}

