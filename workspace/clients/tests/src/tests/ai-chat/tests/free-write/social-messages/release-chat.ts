
import { Test } from "tap";
import { addChatUserToUserMessage } from "@divinci-ai/actions";
import { uniqueId } from "@divinci-ai/utils";

export async function chatWithRelease(test: Test){

  const [chatUser, releaseUser] = getUsers(2);

  const release = createRelease(releaseUser);
  const chat = await createChat(chatUser);
  await addReleaseToChat(chatUser, release);

  const userContent = "Hello World! " + uniqueId();
  const { messageId: chatMessage } = await addChatUserToUserMessage(
    chatUser, chat, { content: userContent, release }
  );

  await doesMessageExist(
    test, { chatUser, releaseUser }, { chat, release },
    { messageId: chatMessage, content: userContent }
  );

  const releaseContent = "Hello World 2! " + uniqueId();
  const { messageId: threadMessage } = await addReleaseThreadUserToUserMessage(
    releaseUser, chat, { content: releaseContent, release }
  );

  await doesMessageExist(
    test, { chatUser, releaseUser }, { chat, release },
    { messageId: threadMessage, content: releaseContent }
  );
}

async function doesMessageExist(
  test: Test,
  { chatUser, releaseUser }: { chatUser: AuthFetch, releaseUser: AuthFetch },
  { chat, release }: { chat: ChatResult, release: Release },
  { messageId, content }: { messageId: string, content: string }
){

  const chatFromUser = await getChat(chatUser, chat._id);
  const messageFromUser = chatFromUser.transcript.messages.find((message)=>(message._id === messageId));
  test.ok(messageFromUser, "User should have found the message");
  test.equal(messageFromUser.content, content, "Message Content is as expected");
  const chatAssistantMessages = chatFromUser.transcript.messages.filter((message)=>(message.role === "assistant"));
  test.equal(chatAssistantMessages.length, 0, "Should be no assistant messages");
  const chatErrorMessages = chatFromUser.transcript.messages.filter((message)=>(message.role === "error"));
  test.equal(chatErrorMessages.length, 0, "Should be no error messages");


  const chatFromRelease = await getReleaseThread(releaseUser, release, chat);
  const messageFromRelease = chatFromRelease.transcript.messages.find((message)=>(message._id === messageId));
  test.ok(messageFromRelease, "Release owner should have found the chat message");
  test.equal(messageFromRelease.content, content, "Message Content is as expected");
  const releaseAssistantMessages = chatFromRelease.transcript.messages.filter((message)=>(message.role === "assistant"));
  test.equal(releaseAssistantMessages.length, 0, "Should be no assistant messages");
  const releaseErrorMessages = chatFromRelease.transcript.messages.filter((message)=>(message.role === "error"));
  test.equal(releaseErrorMessages.length, 0, "Should be no error messages");
}
