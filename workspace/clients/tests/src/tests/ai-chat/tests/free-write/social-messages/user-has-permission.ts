
import { Test } from "tap";
import { addChatUserToUserMessage } from "@divinci-ai/actions";
import { givePermissionsToUser } from "../../../../../test-prep";
import { errorHasStatus } from "../../../../../util/error";
import { uniqueId } from "@divinci-ai/utils";

export async function userCanUseChatWithPermission(test: Test){

  const [chatUser, permissionUser] = getUsers(2);

  const chat = await createChat(chatUser);

  try {
    await addChatUserToUserMessage(
      chatUser, chat, { content: "Hello World!" }
    );
    throw new Error("Should have throw an error");
  }catch(e){
    if(!errorHasStatus(e, 404)) throw e;
    test.pass("Can't find chat that the user doesn't have permission to use");
  }

  await givePermissionsToUser(chatUser, chat, permissionUser);

  const { messageId } = await addChatUserToUserMessage(
    permissionUser, chat, { content: "Hello World!" }
  );
  test.pass("able to add message to chat");

  const chatFromUser = await getChat(chatUser, chat._id);
  const messageFromUser = chatFromUser.transcript.messages.find((message)=>(message._id === messageId));
  test.ok(messageFromUser, "Should have found the message");
}

export async function userCanUseReleaseChatWithPermission(test: Test){

  const [chatUser, permissionUser, releaseUser] = getUsers(2);

  const chat = await createChat(chatUser);
  await givePermissionsToUser(chatUser, chat, permissionUser);

  const release = createRelease(releaseUser);
  try {
    await addChatUserToUserMessage(
      permissionUser, chat, { content: "Hello World!", release }
    );
    throw new Error("Should have throw an error");
  }catch(e){
    if(!errorHasStatus(e, 400)) throw e;
    test.pass("Can't use release that hasn't been added to chat");
  }

  let chatFromUser = await getChat(chatUser, chat._id);
  t.equal(chatFromUser.transcript.messages.length, 0, "Should have no messages");

  await addReleaseToChat(chatUser, release);

  const content = "Hello World!" + uniqueId();
  const { messageId } = await addChatUserToUserMessage(
    permissionUser, chat, { content, release }
  );

  chatFromUser = await getChat(chatUser, chat._id);
  const messageFromUser = chatFromUser.transcript.messages.find((message)=>(message._id === messageId));
  test.ok(messageFromUser, "Should have found the message");
}
