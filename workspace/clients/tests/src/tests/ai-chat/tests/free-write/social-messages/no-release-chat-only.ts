
import { Test } from "tap";
import { addChatUserToUserMessage } from "@divinci-ai/actions";
import { errorHasStatus } from "../../../../../util/error";

export async function noReleaseChatOnly(test: Test){
  const [chatUser, releaseUser] = getUsers(2);

  const release = createRelease(releaseUser);
  const chat = await createChat(chatUser);
  await addReleaseToChat(chatUser, release);
  await addReleaseChat(chatUser, chat, release);

  const { messageId } = await addChatUserToUserMessage(
    chatUser, chat, { content: "Hello World!" }
  );

  const chatFromRelease = await getReleaseThread(releaseUser, release, chat);
  const message = chatFromRelease.transcript.messages.find((message)=>(message._id === messageId));
  test.not(message, "Should not have found the message");

  const chatFromUser = await getChat(chatUser, chat._id);
  const messageFromUser = chatFromUser.transcript.messages.find((message)=>(message._id === messageId));
  test.ok(messageFromUser, "Should have found the message");
}

export async function releaseCantUseChat(t: Test){
  const [chatUser, releaseUser] = getUsers(2);

  const release = createRelease(releaseUser);
  const chat = await createChat(chatUser);

  try {
    await addReleaseThreadUserToUserMessage(
      releaseUser, chat, { content: "Hello World!", release }
    );
    throw new Error("Should have failed to add message");
  }catch(e) {
    if(!errorHasStatus(e, 404)) throw e;
    t.pass("Can't find chat that's not using the release");
  }
  const chatFromUser = await getChat(chatUser, chat._id);
  t.equal(chatFromUser.transcript.messages.length, 0, "Should have no messages");
}

