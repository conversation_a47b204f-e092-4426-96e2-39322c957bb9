
import { Test } from "tap";

import { chatWithRelease } from "./release-chat";
import { noReleaseChatOnly, releaseCantUseChat } from "./no-release-chat-only";
import { userCanUseChatWithPermission, userCanUseReleaseChatWithPermission } from "./user-has-permission";

const TEST_LIST = [
  {
    name: "Chat With Release", fn: chatWithRelease
  },
  {
    name: "No Release Chat Only", fn: noReleaseChatOnly
  },
  {
    name: "Release Can't Use Chat", fn: releaseCantUseChat
  },
  {
    name: "User Can Use Chat With Permission", fn: userCanUseChatWithPermission
  },
  {
    name: "User Can Use Release Chat With Permission", fn: userCanUseReleaseChatWithPermission
  },
];

export async function runTest(test: Test){
  for(const { name, fn } of TEST_LIST){
    await test.test(name, fn);
  }
}
