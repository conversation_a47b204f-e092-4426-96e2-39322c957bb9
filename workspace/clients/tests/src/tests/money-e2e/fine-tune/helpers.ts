import { delay } from "@divinci-ai/utils";

import { AuthFetch } from "../../../globals/auth0";
import { uniqueId } from "@divinci-ai/utils";

import {
  whitelabelCreate,
  createFineTune,
  createFinetuneFile,
  createFineTuneJob,
  cancelFineTuneJob,
  getFineTune,
} from "@divinci-ai/actions";

import { ToolInitialCosts } from "@divinci-ai/tools";
import { StartProcessReturnCancelWait } from "../helpers/money-system-tests";
import {
  testPaymentSuccessProcessCancelledRefund,
  testPaymentFailureNoProcess,

  testMultipleProcessesCancelled,
  test1Cancel2PaymentFailure,
} from "../helpers/money-system-tests";
import { Test } from "tap";


export const FINETUNE_FILE_CONTENTS = [
  [
    {
      "prompt": "hello",
      "response": "world"
    },
  ]
];

export async function initializeFineTune(
  userFetch: AuthFetch,
  finetuneTool: string,
){
  const { whitelabel } = await whitelabelCreate(userFetch, {
    title: `Test Whitelabel ${uniqueId()}`,
    description: "Test whitelabel for fine tune"
  });

  const finetune = await createFineTune(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      title: `Test AI Chat ${uniqueId()}`,
      description: "Test fine tune",
      finetuneTool,
    }
  );

  const file = await createFinetuneFile(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      file: new File([JSON.stringify(FINETUNE_FILE_CONTENTS)], "hello-world.json"),
      name: "Test File " + uniqueId(),
      description: "Hello World!",
    }
  );

  const job = await createFineTuneJob(
    userFetch,
    { whitelabelId: whitelabel._id, customAiId: finetune._id },
    { fileId: file._id },
  );

  return {
    whitelabel,
    finetune,
    file,
    job,
  };
}

type CreateFineTuneReturn = Awaited<ReturnType<typeof initializeFineTune>>;

export async function cancelFineTune(
  fetcher: typeof fetch, values: CreateFineTuneReturn
){
  await cancelFineTuneJob(
    fetcher,
    {
      whitelabelId: values.whitelabel._id,
      customAiId: values.finetune._id
    }
  );
}

export async function pollForFineTuneEnd(
  fetcher: typeof fetch, values: CreateFineTuneReturn, maxRetries = 20
){
  for(let i = 0; i < maxRetries; i++, await delay(1000)){
    const fineTune = await getFineTune(
      fetcher,
      {
        whitelabelId: values.whitelabel._id,
        customAiId: values.finetune._id
      }
    );
    if(fineTune.active) continue;
    return fineTune;
  }
  throw new Error("Timed out");
}




export function runFineTuneTests(
  test: Test,
  userFetch: AuthFetch,
  expectedCosts: ToolInitialCosts,
  cancellableProcess: StartProcessReturnCancelWait,
  failedPayment: StartProcessReturnCancelWait,
){
  testPaymentSuccessProcessCancelledRefund(
    test, userFetch, expectedCosts, cancellableProcess
  );

  testPaymentFailureNoProcess(
    test, userFetch, expectedCosts, failedPayment
  );

  testMultipleProcessesCancelled(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: cancellableProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: cancellableProcess,
    }
  );

  test1Cancel2PaymentFailure(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: cancellableProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
}
