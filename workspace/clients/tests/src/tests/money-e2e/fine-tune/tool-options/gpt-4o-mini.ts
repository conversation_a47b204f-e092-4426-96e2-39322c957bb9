import { OpenAIGPT4oMiniFineTune } from "@divinci-ai/tools";

import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";

import {
  initializeFineTune,
  cancelFineTune,
  pollForFineTuneEnd,
} from "../helpers";

import { FINETUNE_FILE_CONTENTS, runFineTuneTests } from "../helpers";

/**
 * Test that Fine Tune GPT-4o-mini works with sufficient funds and proper cancellation
 */

export async function testFineTuneGPT4oMini(parentTest: Test, userFetch: AuthFetch){
  await parentTest.test("GPT-4o mini", async (test)=>{
    const expectedCosts = await OpenAIGPT4oMiniFineTune.pricing.getEstimatedCost(
      {}, {
        threads: FINETUNE_FILE_CONTENTS,
      }
    );

    runFineTuneTests(
      test, userFetch, expectedCosts,
      (test)=>(cancelableProcess(test, userFetch)),
      (test)=>(failedPayment(test, userFetch)),
    );
  });
}

async function cancelableProcess(test: Test, userFetch: AuthFetch){
  const results = await initializeFineTune(
    userFetch, OpenAIGPT4oMiniFineTune.id
  );

  return {
    cancelProcess: async ()=>{
      await cancelFineTune(userFetch, results);
    },
    waitForCompletion: Promise.resolve().then(async ()=>{
      const finetune = await pollForFineTuneEnd(
        userFetch, results
      );
      const successfulFile = finetune.files.find((fileId)=>(fileId === results.file._id));
      if(successfulFile) throw new Error("Successful file found");
      const failedFile = finetune.failedFiles.find((file)=>(file.fileId === results.file._id));
      if(!failedFile) throw new Error("Failed file not found");
      test.equal(failedFile.reason, "cancelled", "Should have cancelled");
    }),
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch): Promise<any>{
  // This should fail due to insufficient funds, so we don't expect it to return anything
  // But we need to return the expected structure for the type system
  await initializeFineTune(
    userFetch, OpenAIGPT4oMiniFineTune.id
  );
  // If we reach here, the test framework will catch that this should have thrown an error
  throw new Error("Should have thrown an error");
}
