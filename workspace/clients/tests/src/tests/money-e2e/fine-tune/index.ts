import { Test } from "tap";
import { AuthFetch } from "../../../globals/auth0";

// Test functions
import { testFineTuneGPT4o } from "./tool-options/gpt-4o";
import { testFineTuneGPT4oMini } from "./tool-options/gpt-4o-mini";

const FINE_TUNE_TESTS = [
  // { name: "Fine Tune GPT-4o", fn: testFineTuneGPT4o },
  { name: "Fine Tune GPT-4o-mini", fn: testFineTuneGPT4oMini },
];

export async function runTest(test: Test, user1: AuthFetch){
  for(const testCase of FINE_TUNE_TESTS) {
    await testCase.fn(test, user1);
  }
}
