import { Test } from "tap";
import { AuthFetch } from "../../../globals/auth0";

// Test functions
import { testUrlScrapeDivinciFetch } from "./tool-options/divinci-fetch";
import { HTMLServer } from "../../../story-test/workbench/rag/tests/free-write/html/prep/html-service";


const WEBSITE_SERVER_1 = new HTMLServer();

const RAG_URL_SCRAPER_TESTS = [
  { name: "<PERSON>vinci Fetch", fn: testUrlScrapeDivinciFetch },
  // { name: "Firecrawl", fn: testRagUrlScrapeFirecrawl },
];

export async function runTest(test: Test, user1: AuthFetch){
  const htmlServer = await WEBSITE_SERVER_1.use();
  for(const testCase of RAG_URL_SCRAPER_TESTS) {
    await testCase.fn(test, user1, htmlServer.url);
  }
  await WEBSITE_SERVER_1.release();
}
