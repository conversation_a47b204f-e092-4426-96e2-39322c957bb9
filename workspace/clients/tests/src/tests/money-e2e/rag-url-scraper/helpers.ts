import { delay, DIVINCI_TEST_PROCESS_Config } from "@divinci-ai/utils";

import { AuthFetch } from "../../../globals/auth0";
import { uniqueId } from "@divinci-ai/utils";

import { URLScraperBase } from "@divinci-ai/tools";
import {
  whitelabelCreate,
  ragVectorHTMLPageScrape,
  ragVectorHTMLPageCrawl,
  ragVectorHTMLPageGetHostItem
} from "@divinci-ai/actions";

import { ToolInitialCosts } from "@divinci-ai/tools";
import { StartProcessReturnWait } from "../helpers/money-system-tests";
import {
  testPaymentSuccessProcessSuccess,
  testPaymentSuccessProcessFailureRefund,
  testPaymentFailureNoProcess,
  testMultipleProcessesSuccess,
  test1Success2PaymentFailure,
  test1Refund2PaymentFailure,
} from "../helpers/money-system-tests";
import { Test } from "tap";

export const URL_PAYMENT_INPUT = {
  limit: 25,
};

export async function urlScrape(
  userFetch: <PERSON>th<PERSON><PERSON><PERSON>,
  assistant: URL<PERSON><PERSON>raperBase<any>,
  url: string,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const targetUrl = new URL(url);
  const { whitelabel } = await whitelabelCreate(userFetch, {
    title: `Test Whitelabel ${uniqueId()}`,
    description: "Test whitelabel"
  });
  const { crawlId } = await ragVectorHTMLPageScrape(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      url: targetUrl.href,
      scraperTool: { id: assistant.id, config: assistant.inputConfig?.default || {} },
      addToRagVector: [],
    },
    testConfig
  );
  return { crawlId, whitelabel, host: targetUrl.host };
}

export async function urlCrawl(
  userFetch: AuthFetch,
  assistant: URLScraperBase<any>,
  url: string,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const targetUrl = new URL(url);
  const { whitelabel } = await whitelabelCreate(userFetch, {
    title: `Test Whitelabel ${uniqueId()}`,
    description: "Test whitelabel"
  });
  const { crawlId } = await ragVectorHTMLPageCrawl(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      url: targetUrl.href,
      scraperTool: { id: assistant.id, config: assistant.inputConfig?.default || {} },
      limit: URL_PAYMENT_INPUT.limit,
      runSitemap: false,
      ignoreSavedPaths: false,
      validGetParameters: [],
      excludePaths: [],
      allowedPaths: [],
      addToRagVector: [],
    },
    testConfig
  );

  return { crawlId, whitelabel, host: targetUrl.host };
}


type CreateRagFileReturn = Awaited<ReturnType<typeof urlCrawl>>;

export async function pollInfoForCrawl(
  fetcher: typeof fetch, result: CreateRagFileReturn, maxRetries = 20
){
  for(let i = 0; i < maxRetries; i++, await delay(1000)){
    const info = await ragVectorHTMLPageGetHostItem(
      fetcher, { whitelabelId: result.whitelabel._id, urlHost: result.host }
    );
    const crawl = info.crawls.find((c)=>(c._id === result.crawlId));
    if(!crawl) throw new Error("Crawl not found");
    if(crawl.status === "pending") continue;
    return crawl;
  }
  throw new Error("Timed out");
}


export function runAssistantTests(
  test: Test,
  userFetch: AuthFetch,
  expectedCosts: ToolInitialCosts,
  successfulProcess: StartProcessReturnWait,
  failedProcess: StartProcessReturnWait,
  failedPayment: StartProcessReturnWait,
){
  testPaymentSuccessProcessSuccess(
    test, userFetch, expectedCosts, null, successfulProcess
  );

  testPaymentSuccessProcessFailureRefund(
    test, userFetch, expectedCosts, failedProcess
  );

  testPaymentFailureNoProcess(
    test, userFetch, expectedCosts, failedPayment
  );

  testMultipleProcessesSuccess(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    }
  );
  test1Success2PaymentFailure(test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
  test1Refund2PaymentFailure(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: failedProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
}
