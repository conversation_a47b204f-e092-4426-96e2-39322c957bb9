import { Test } from "tap";
import { AuthFetch } from "../../../../../globals/auth0";

import {
  URLScraperDivinciFetchScrape,
} from "@divinci-ai/tools";

import {
  urlScrape,
  pollInfoForCrawl,
  runAssistantTests,
} from "../../helpers";

export async function runScrapeTests(
  test: Test, userFetch: AuthFetch, url: string
){
  const expectedCosts = await URLScraperDivinciFetchScrape.pricing.getEstimatedCost(
    {}, { url, limit: 1 }
  );
  await runAssistantTests(
    test, userFetch, expectedCosts,
    (test)=>(successfulProcess(test, userFetch, url)),
    (test)=>(failedProcess(test, userFetch, url)),
    (test)=>(failedPayment(test, userFetch, url)),
  );
}


async function successfulProcess(test: Test, userFetch: AuthFetch, url: string){
  const result = await urlScrape(
    userFetch,
    URLScraperDivinciFetchScrape,
    url,
    { delay: true, fail: false }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollInfoForCrawl(
        userFetch, result
      );
      test.equal(
        message.status, "completed",
        "Should be in editing mode"
      );
    }),
  };
}

async function failedProcess(test: Test, userFetch: AuthFetch, url: string){
  const result = await urlScrape(
    userFetch,
    URLScraperDivinciFetchScrape,
    url,
    { delay: true, fail: true }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollInfoForCrawl(
        userFetch, result
      );
      test.equal(
        message.status, "failed",
        "Should fave failed to chunk"
      );
    })
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch, url: string): Promise<any>{
  await urlScrape(
    userFetch,
    URLScraperDivinciFetchScrape,
    url,
  );
  throw new Error("Should have thrown an error");
}
