import { Test } from "tap";
import { AuthFetch } from "../../../../../globals/auth0";

import { runCrawlTests } from "./crawl";

import { runScrapeTests } from "./scrape";


export async function testUrlScrapeDivinciFetch(
  parentTest: Test, userFetch: AuthFetch, url: string
){
  await parentTest.test("Divinci OpenParse", async (payTypeTest)=>{
    await payTypeTest.test("Crawl", async (test)=>(
      runCrawlTests(test, userFetch, url)
    ));
    await payTypeTest.test("Scrape", async (test)=>(
      runScrapeTests(test, userFetch, url)
    ));
  });
}
