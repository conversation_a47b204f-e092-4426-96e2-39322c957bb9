import { Test } from "tap";
import { AuthFetch } from "../../../../../globals/auth0";

import {
  RawToChunksDivinciOpenparse,
  ToolInitialCosts,
} from "@divinci-ai/tools";

import { RagVectorTextChunksStatus } from "@divinci-ai/models";

import {
  runAssistantTests,
  rechunkRagFile,
  pollFileForStatus,
} from "../../helpers";

export function runTests(test: Test, userFetch: AuthFetch, expectedCosts: ToolInitialCosts){
  return runAssistantTests(
    test, userFetch, expectedCosts,
    (test)=>(successfulProcess(test, userFetch)),
    (test)=>(failedProcess(test, userFetch)),
    (test)=>(failedPayment(test, userFetch)),
  );
}

async function successfulProcess(test: Test, userFetch: AuthFetch){
  const result = await rechunkRagFile(
    userFetch,
    RawToChunksDivinciOpenparse,
    { delay: true, fail: false }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollFileForStatus(
        userFetch, result
      );
      test.equal(
        message.status, RagVectorTextChunksStatus.EDITING,
        "Should be in editing mode"
      );
    }),
  };
}

async function failedProcess(test: Test, userFetch: AuthFetch){
  const result = await rechunkRagFile(
    userFetch,
    RawToChunksDivinciOpenparse,
    { delay: true, fail: true }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollFileForStatus(
        userFetch, result
      );
      test.equal(
        message.status, RagVectorTextChunksStatus.FAILED_TO_CHUNK,
        "Should fave failed to chunk"
      );
    })
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch): Promise<any>{
  await rechunkRagFile(
    userFetch,
    RawToChunksDivinciOpenparse,
  );
  throw new Error("Should have thrown an error");
}
