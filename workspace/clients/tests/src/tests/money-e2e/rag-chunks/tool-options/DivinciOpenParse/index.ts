import { Test } from "tap";
import { AuthFetch } from "../../../../../globals/auth0";

import {
  RawToChunksDivinciOpenparse,
} from "@divinci-ai/tools";

import { TEST_FILE } from "../../helpers";

import { runTests as runCreateDirectTests } from "./CreateDirect";
import { runTests as runCreatePresignedTests } from "./CreatePresigned";
import { runTests as runRechunkTests } from "./Rechunk";

export async function testRawToChunksDivinciOpenParse(parentTest: Test, userFetch: AuthFetch){
  await parentTest.test("Divinci OpenParse", async (payTypeTest)=>{
    const DEFAULT_CONFIG = RawToChunksDivinciOpenparse.inputConfig?.default;
    if(!DEFAULT_CONFIG) {
      throw new Error("Could not find default config for Divinci OpenParse");
    }

    const expectedCosts = await RawToChunksDivinciOpenparse.pricing.getEstimatedCost(
      DEFAULT_CONFIG,
      {
        Bucket: "", Key: "",
        originalName: TEST_FILE.filename,
        byteLength: TEST_FILE.contents.length,
      }
    );

    await payTypeTest.test("Create File Direct", async (test)=>(
      runCreateDirectTests(test, userFetch, expectedCosts)
    ));

    await payTypeTest.test("Create File Presigned", async (test)=>(
      runCreatePresignedTests(test, userFetch, expectedCosts)
    ));

    await payTypeTest.test("Create File Rechunk", async (test)=>(
      runRechunkTests(test, userFetch, expectedCosts)
    ));

  });
}

