import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";

import {
  createRagFileDirect,
  pollFileForStatus,
} from "../helpers";

import {
  RawToChunksUnstructured,
} from "@divinci-ai/tools";
import { RagVectorTextChunksStatus } from "@divinci-ai/models";

import { TEST_FILE, runAssistantTests } from "../helpers";

export async function testRawToChunksUnstructured(parentTest: Test, userFetch: AuthFetch){
  await parentTest.test("Divinci OpenParse", async (test)=>{
    const DEFAULT_CONFIG = RawToChunksUnstructured.inputConfig?.default;
    if(!DEFAULT_CONFIG) {
      throw new Error("Could not find default config for Unstructured");
    }

    const expectedCosts = await RawToChunksUnstructured.pricing.getEstimatedCost(
      DEFAULT_CONFIG,
      {
        Bucket: "", Key: "",
        originalName: TEST_FILE.filename,
        byteLength: TEST_FILE.contents.length,
      }
    );

    runAssistantTests(
      test, userFetch, expectedCosts,
      (test)=>(successfulProcess(test, userFetch)),
      (test)=>(failedProcess(test, userFetch)),
      (test)=>(failedPayment(test, userFetch)),
    );
  });
}

async function successfulProcess(test: Test, userFetch: AuthFetch){
  const result = await createRagFileDirect(
    userFetch,
    RawToChunksUnstructured,
    { delay: true, fail: false }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollFileForStatus(
        userFetch, result
      );
      test.equal(
        message.status, RagVectorTextChunksStatus.EDITING,
        "Should be in editing mode"
      );
    }),
  };
}

async function failedProcess(test: Test, userFetch: AuthFetch){
  const result = await createRagFileDirect(
    userFetch,
    RawToChunksUnstructured,
    { delay: true, fail: true }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollFileForStatus(
        userFetch, result
      );
      test.equal(
        message.status, RagVectorTextChunksStatus.FAILED_TO_CHUNK,
        "Should fave failed to chunk"
      );
    })
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch): Promise<any>{
  await createRagFileDirect(
    userFetch,
    RawToChunksUnstructured,
  );
  throw new Error("Should have thrown an error");
}
