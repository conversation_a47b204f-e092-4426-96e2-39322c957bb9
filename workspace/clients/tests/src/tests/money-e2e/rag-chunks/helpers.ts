import { delay, DIVINCI_TEST_PROCESS_Config } from "@divinci-ai/utils";

import { AuthFetch } from "../../../globals/auth0";
import { uniqueId } from "@divinci-ai/utils";

import { RawToChunksBase } from "@divinci-ai/tools";
import {
  whitelabelCreate,
  createRagVectorFile,
  ragVectorFileRechunk,
  getRagVectorFile
} from "@divinci-ai/actions";

import { ToolInitialCosts } from "@divinci-ai/tools";
import { StartProcessReturnWait } from "../helpers/money-system-tests";
import {
  testPaymentSuccessProcessSuccess,
  testPaymentSuccessProcessFailureRefund,
  testPaymentFailureNoProcess,
  testMultipleProcessesSuccess,
  test1Success2PaymentFailure,
  test1Refund2PaymentFailure,
} from "../helpers/money-system-tests";
import { Test } from "tap";
import { getTestFile } from "../../../globals/test-file";
import { RagVectorTextChunksStatus } from "@divinci-ai/models";


export const TEST_FILE = {
  filename: "raw-rag-example.pdf",
  contents: getTestFile("./raw-rag-example.pdf"),
};

export async function createRagFileDirect(
  userFetch: AuthFetch,
  assistant: RawToChunksBase<any>,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const { whitelabel } = await whitelabelCreate(userFetch, {
    title: `Test Whitelabel ${uniqueId()}`,
    description: "Test whitelabel for fine tune"
  });

  const file = await createRagVectorFile(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      file: new File([TEST_FILE.contents], TEST_FILE.filename),
      title: "Test File " + uniqueId(),
      description: "Hello World!",
      chunkingTool: {
        id: assistant.id,
        config: assistant.inputConfig?.default || {},
      },
    },
    "never",
    testConfig
  );

  return { file, whitelabel };
}

export async function createRagFilePresigned(
  userFetch: AuthFetch,
  assistant: RawToChunksBase<any>,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const { whitelabel } = await whitelabelCreate(userFetch, {
    title: `Test Whitelabel ${uniqueId()}`,
    description: "Test whitelabel for fine tune"
  });

  const file = await createRagVectorFile(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      file: new File([TEST_FILE.contents], TEST_FILE.filename),
      title: "Test File " + uniqueId(),
      description: "Hello World!",
      chunkingTool: {
        id: assistant.id,
        config: assistant.inputConfig?.default || {},
      },
    },
    "always",
    testConfig
  );

  return { file, whitelabel };
}

export async function rechunkRagFile(
  userFetch: AuthFetch,
  assistant: RawToChunksBase<any>,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const { whitelabel } = await whitelabelCreate(userFetch, {
    title: `Test Whitelabel ${uniqueId()}`,
    description: "Test whitelabel for fine tune"
  });

  const file = await createRagVectorFile(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      file: new File([TEST_FILE.contents], TEST_FILE.filename),
      title: "Test File " + uniqueId(),
      description: "Hello World!",
      chunkingTool: {
        id: assistant.id,
        config: assistant.inputConfig?.default || {},
      },
    },
    "never",
    { delay: false, fail: true }
  );

  const polledFile = await pollFileForStatus(userFetch, { file, whitelabel });
  if(polledFile.status !== RagVectorTextChunksStatus.FAILED_TO_CHUNK){
    throw new Error("Expected file to fail to chunk");
  }

  await ragVectorFileRechunk(
    userFetch,
    { whitelabelId: whitelabel._id, fileId: file._id },
    {
      chunkerTool: {
        id: assistant.id,
        config: assistant.inputConfig?.default || {},
      },
    },
    testConfig
  );

  return { file, whitelabel };
}

type CreateRagFileReturn = Awaited<ReturnType<typeof createRagFileDirect>>;

export async function pollFileForStatus(
  fetcher: typeof fetch, result: CreateRagFileReturn, maxRetries = 20
){
  for(let i = 0; i < maxRetries; i++, await delay(1000)){
    const file = await getRagVectorFile(fetcher, {
      whitelabelId: result.whitelabel._id,
      fileId: result.file._id
    });
    if(file.status === RagVectorTextChunksStatus.CHUNKING) continue;
    return file;
  }
  throw new Error("Timed out");
}


export function runAssistantTests(
  test: Test,
  userFetch: AuthFetch,
  expectedCosts: ToolInitialCosts,
  successfulProcess: StartProcessReturnWait,
  failedProcess: StartProcessReturnWait,
  failedPayment: StartProcessReturnWait,
){
  testPaymentSuccessProcessSuccess(
    test, userFetch, expectedCosts, null, successfulProcess
  );

  testPaymentSuccessProcessFailureRefund(
    test, userFetch, expectedCosts, failedProcess
  );

  testPaymentFailureNoProcess(
    test, userFetch, expectedCosts, failedPayment
  );

  testMultipleProcessesSuccess(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    }
  );
  test1Success2PaymentFailure(test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
  test1Refund2PaymentFailure(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: failedProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
}
