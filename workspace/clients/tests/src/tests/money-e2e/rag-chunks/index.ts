import { Test } from "tap";
import { AuthFetch } from "../../../globals/auth0";

// Test functions
import { testRawToChunksDivinciOpenParse } from "./tool-options/DivinciOpenParse";
import { testRawToChunksUnstructured } from "./tool-options/Unstructured";

const RAG_CHUNKS_TESTS = [
  { name: "Divinci OpenParse", fn: testRawToChunksDivinciOpenParse },
  // { name: "Unstructured", fn: testRawToChunksUnstructured },
];

export async function runTest(test: Test, user1: AuthFetch){
  for(const testCase of RAG_CHUNKS_TESTS) {
    await testCase.fn(test, user1);
  }
}
