import { Test } from "tap";
import { AuthFetch } from "../../../globals/auth0";

// Test functions
import {
  testAudioTranscriptionDivinciPyannoteCloudflareWhisper
} from "./tool-options/divincipyanotte-cloudflarewhisper";

const AUDIO_TRANSCRIPTION_TESTS = [
  {
    name: "Divinci Pyannote + Cloudflare Whisper",
    fn: testAudioTranscriptionDivinciPyannoteCloudflareWhisper
  },
];

export async function runTest(test: Test, user1: AuthFetch){
  for(const testCase of AUDIO_TRANSCRIPTION_TESTS) {
    await testCase.fn(test, user1);
  }
}
