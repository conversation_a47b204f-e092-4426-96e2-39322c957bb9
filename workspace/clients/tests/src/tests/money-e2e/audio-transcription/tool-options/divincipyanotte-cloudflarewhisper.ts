import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";

import { initializeAudioTranscript, pollAudioTranscriptFinish } from "../helpers";

import {
  AUDIO_SPEAKER_TRANSCRIPT_PROCESS_PRICING,
  SpeakerDiaizationDivinciPyannote,
  AudioTranscriptionCloudflareWhisper,
  CostEstimationStatus
} from "@divinci-ai/tools";

import { TEST_FILE, runAssistantTests } from "../helpers";

const TOOL_INPUTS = {
  diarizer: {
    id: SpeakerDiaizationDivinciPyannote.id,
    config: SpeakerDiaizationDivinciPyannote.inputConfig?.default || {}
  },
  transcriber: {
    id: AudioTranscriptionCloudflareWhisper.id,
    config: AudioTranscriptionCloudflareWhisper.inputConfig?.default || {}
  }
};

export async function testAudioTranscriptionDivinciPyannoteCloudflareWhisper(parentTest: Test, userFetch: AuthFetch){
  await parentTest.test("Di<PERSON><PERSON> and Cloudflare Whisper", async (test)=>{


    const expectedCosts = await AUDIO_SPEAKER_TRANSCRIPT_PROCESS_PRICING.getEstimatedCost(
      TOOL_INPUTS,
      {}, {
        audioDuration: TEST_FILE.audioDuration,
      }
    );

    const fullCosts = {
      inputCost: 0n,
      outputEscrow: 0n,
      costStatus: "maximum" as CostEstimationStatus,
    };

    for(const cost of Object.values(expectedCosts)){
      fullCosts.inputCost += cost.inputCost;
      fullCosts.outputEscrow += cost.outputEscrow;
    }

    runAssistantTests(
      test, userFetch, fullCosts,
      (test)=>(successfulProcess(test, userFetch)),
      (test)=>(failedProcess(test, userFetch)),
      (test)=>(failedPayment(test, userFetch)),
    );
  });
}

async function successfulProcess(test: Test, userFetch: AuthFetch){
  const result = await initializeAudioTranscript(
    userFetch,
    TOOL_INPUTS,
    { delay: true, fail: false }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const audio = await pollAudioTranscriptFinish(
        userFetch, result
      );
      test.equal(audio.processStatus, "completed", "Should have completed");
    }),
  };
}

async function failedProcess(test: Test, userFetch: AuthFetch){
  const result = await initializeAudioTranscript(
    userFetch,
    TOOL_INPUTS,
    { delay: true, fail: true }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const audio = await pollAudioTranscriptFinish(
        userFetch, result
      );
      test.equal(audio.processStatus, "failed", "Should have completed");
    })
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch): Promise<any>{
  await initializeAudioTranscript(
    userFetch,
    TOOL_INPUTS,
  );
  throw new Error("Should have thrown an error");
}
