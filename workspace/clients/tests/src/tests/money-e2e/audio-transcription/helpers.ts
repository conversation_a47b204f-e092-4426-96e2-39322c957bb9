import { delay, DIVINCI_TEST_PROCESS_Config } from "@divinci-ai/utils";

import { AuthFetch } from "../../../globals/auth0";
import { uniqueId } from "@divinci-ai/utils";

import { whitelabelCreate, dataSourceAudioCreateFile, dataSourceAudioItem } from "@divinci-ai/actions";

import { Test } from "tap";
import { getTestFile } from "../../../globals/test-file";
import { AudioTranscriptStatus, SelectedPublicToolWithConfig } from "@divinci-ai/models";

export const TEST_FILE = {
  filename: "homer-haircut.small.mp3",
  contents: getTestFile("./homer-haircut.small.mp3"),
  // This is the result from ffmpeg locally
  // audioDuration: 8_400_979 / 1_000_000

  // This is result that comes from the ffmpeg worker
  audioDuration: 8_472_000 / 1_000_000
};


export async function initializeAudioTranscript(
  userFetch: AuthFetch,
  tools: {
    diarizer: SelectedPublicToolWithConfig,
    transcriber: SelectedPublicToolWithConfig,
  },
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const { whitelabel } = await whitelabelCreate(userFetch, {
    title: `Test Whitelabel ${uniqueId()}`,
    description: "Test whitelabel for fine tune"
  });

  const audio = await dataSourceAudioCreateFile(
    userFetch,
    { whitelabelId: whitelabel._id },
    {
      mediaFile: new File([TEST_FILE.contents], TEST_FILE.filename),
      diarizerTool: tools.diarizer,
      transcriberTool: tools.transcriber,
    },
    "never",
    testConfig
  );

  return { whitelabel, audio };
}

type CreateAudioTranscriptReturn = Awaited<ReturnType<typeof initializeAudioTranscript>>;
export async function pollAudioTranscriptFinish(
  fetcher: typeof fetch, result: CreateAudioTranscriptReturn, maxRetries = 45
){
  for(let i = 0; i < maxRetries; i++, await delay(1000)){
    const audioFile = await dataSourceAudioItem(
      fetcher, { whitelabelId: result.whitelabel._id, audioId: result.audio._id }
    );
    switch(audioFile.processStatus){
      case AudioTranscriptStatus.Failed: return audioFile;
      case AudioTranscriptStatus.Completed: return audioFile;
    }
  }
  throw new Error("Timed out");
}

import { ToolInitialCosts } from "@divinci-ai/tools";
import { StartProcessReturnWait } from "../helpers/money-system-tests";
import {
  testPaymentSuccessProcessSuccess,
  testPaymentSuccessProcessFailureRefund,
  testPaymentFailureNoProcess,
  testMultipleProcessesSuccess,
  test1Success2PaymentFailure,
  test1Refund2PaymentFailure,
} from "../helpers/money-system-tests";



export function runAssistantTests(
  test: Test,
  userFetch: AuthFetch,
  expectedCosts: ToolInitialCosts,
  successfulProcess: StartProcessReturnWait,
  failedProcess: StartProcessReturnWait,
  failedPayment: StartProcessReturnWait,
){
  testPaymentSuccessProcessSuccess(
    test, userFetch, expectedCosts, null, successfulProcess
  );

  testPaymentSuccessProcessFailureRefund(
    test, userFetch, expectedCosts, failedProcess
  );

  testPaymentFailureNoProcess(
    test, userFetch, expectedCosts, failedPayment
  );

  testMultipleProcessesSuccess(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    }
  );
  test1Success2PaymentFailure(test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
  test1Refund2PaymentFailure(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: failedProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
}
