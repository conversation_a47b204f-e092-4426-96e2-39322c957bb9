import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";
import { getUserBalance, setFundsForUser } from "../../../../story-test/util/money-setup";
import { NANO_MULTIPLIER } from "@divinci-ai/tools";


import { PaidProcess } from "./types";


/**
 * Test: Multiple Processes Successfully Run at Once
 */

export async function testMultipleProcessesSuccess(
  test: Test,
  userFetch: AuthFetch,
  process1: PaidProcess,
  process2: PaidProcess,
){
  test.test(`Multi Payment: Both successful`, async (subTest)=>{
    subTest.ok(
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow > 0n,
      "Expected cost of process1 should be greater than zero"
    );
    subTest.ok(
      process2.expectedCost.inputCost + process2.expectedCost.outputEscrow > 0n,
      "Expected cost of process2 should be greater than zero"
    );

    // Calculate total expected cost
    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 dollar buffer
        + NANO_MULTIPLIER
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    const initialBalance = await getUserBalance(userFetch);
    subTest.equal(
      initialBalance,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 dollar buffer
        + NANO_MULTIPLIER
      ),
      "Set User balance should match expected"
    );
    subTest.ok(
      initialBalance >= (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have sufficient funds"
    );

    const { waitForCompletion: waitForCompletion1 } = await process1.startProcess(subTest);
    const after1Balance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= after1Balance,
      "User should have less balance after first process"
    );
    subTest.equal(
      initialBalance - after1Balance,
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow,
      "Should deducted the expected amount after first process"
    );

    const { waitForCompletion: waitForCompletion2 } = await process2.startProcess(subTest);
    const after2Balance = await getUserBalance(userFetch);
    subTest.ok(
      after1Balance > after2Balance,
      "User should have less balance after second process"
    );
    subTest.equal(
      after1Balance - after2Balance,
      process2.expectedCost.inputCost + process2.expectedCost.outputEscrow,
      "Should deducted the expected amount after second process"
    );

    await Promise.all([
      waitForCompletion1,
      waitForCompletion2,
    ]);
    subTest.pass(`✅ Both processes completed successfully`);

    const finalBalance = await getUserBalance(userFetch);
    subTest.ok(finalBalance >= 0n, "Balance should not go negative");
    subTest.ok(
      initialBalance - finalBalance >= process1.expectedCost.inputCost + process2.expectedCost.inputCost,
      "Should deduct at least the input cost for both processes"
    );
    subTest.ok(
      initialBalance - finalBalance <= (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "Should deduct no more than the total cost for both processes"
    );
  });
}



/**
 * Test: First Process Payment Succeeds, Second Process Payment Fails
 */
export async function test1Success2PaymentFailure(
  test: Test,
  userFetch: AuthFetch,
  process1: PaidProcess,
  process2: PaidProcess
){
  test.test(`Multi Payment: First succeeds, second payment fails`, async (subTest)=>{
    subTest.ok(
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow > 0n,
      "Expected cost of process1 should be greater than zero"
    );
    subTest.ok(
      process2.expectedCost.inputCost + process2.expectedCost.outputEscrow > 0n,
      "Expected cost of process2 should be greater than zero"
    );

    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 cent short
        - 1n
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    const initialBalance = await getUserBalance(userFetch);
    subTest.equal(
      initialBalance,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 cent short
        - 1n
      ),
      "Set User balance should match expected"
    );
    subTest.ok(
      initialBalance >= (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
      ),
      "User should have sufficient funds for process 1 alone"
    );
    subTest.ok(
      initialBalance >= (
        process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have sufficient funds for process 2 alone"
    );

    subTest.ok(
      initialBalance < (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have insufficient funds for both processes"
    );

    const { waitForCompletion: waitForCompletion1 } = await process1.startProcess(subTest);
    const after1Balance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= after1Balance,
      "User should have less balance"
    );
    subTest.equal(
      initialBalance - after1Balance,
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );

    try {
      await process2.startProcess(subTest);
      throw new Error("Should have failed");
    }catch(e){
      if(typeof e !== "object" || e === null || Array.isArray(e)){
        throw e;
      }
      if(!("statusCode" in e) || typeof e.statusCode !== "number" || e.statusCode !== 402){
        throw e;
      }
      subTest.pass("Process should fail with payment error");
    }

    await waitForCompletion1;

    // Verify money was deducted correctly
    const finalBalance = await getUserBalance(userFetch);
    const deducted = initialBalance - finalBalance;
    subTest.ok(deducted >= 0n, "Should have deducted some money");
    if(process1.expectedCost.outputEscrow === 0n) {
      subTest.equal(
        deducted, process1.expectedCost.inputCost,
        "Should deduct exactly the input cost"
      );
    } else {
      subTest.ok(
        deducted >= process1.expectedCost.inputCost,
        "Should deduct at least the input cost"
      );
      subTest.ok(
        deducted <= process1.expectedCost.inputCost + process1.expectedCost.outputEscrow,
        "Should not deduct more than inputCost + outputEscrow"
      );
    }

    subTest.ok(finalBalance >= 0n, "Balance should not go negative");
  });
}

/**
 * Test: First Process Payment Succeeds and Process fails, Second Process Payment Fails
 */
export async function test1Refund2PaymentFailure(
  test: Test,
  userFetch: AuthFetch,
  process1: PaidProcess,
  process2: PaidProcess
){
  test.test(`Multi Payment: First process fails, second payment fails`, async (subTest)=>{
    subTest.ok(
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow > 0n,
      "Expected cost of process1 should be greater than zero"
    );
    subTest.ok(
      process2.expectedCost.inputCost + process2.expectedCost.outputEscrow > 0n,
      "Expected cost of process2 should be greater than zero"
    );

    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 cent short
        - 1n
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    const initialBalance = await getUserBalance(userFetch);
    subTest.equal(
      initialBalance,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 cent short
        - 1n
      ),
      "Set User balance should match expected"
    );
    subTest.ok(
      initialBalance >= (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
      ),
      "User should have sufficient funds for process 1 alone"
    );

    subTest.ok(
      initialBalance >= (
        process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have sufficient funds for process 2 alone"
    );

    subTest.ok(
      initialBalance < (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have insufficient funds for both processes"
    );

    const { waitForCompletion: waitForCompletion1 } = await process1.startProcess(subTest);
    const after1Balance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= after1Balance,
      "User should have less balance"
    );
    subTest.equal(
      initialBalance - after1Balance,
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );

    try {
      await process2.startProcess(subTest);
      throw new Error("Should have failed");
    }catch(e){
      if(typeof e !== "object" || e === null || Array.isArray(e)){
        throw e;
      }
      if(!("statusCode" in e) || typeof e.statusCode !== "number" || e.statusCode !== 402){
        throw e;
      }
      subTest.pass("Process should fail with payment error");
    }

    await waitForCompletion1;

    // Verify money was deducted correctly
    const finalBalance = await getUserBalance(userFetch);
    subTest.equal(
      finalBalance, initialBalance,
      "No money should be deducted on process failure"
    );
  });
}

