import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";
import { getUserBalance, setFundsForUser } from "../../../../story-test/util/money-setup";
import { NANO_MULTIPLIER } from "@divinci-ai/tools";


import { CancellablePaidProcess } from "./types";


export async function testMultipleProcessesCancelled(
  test: Test,
  userFetch: AuthFetch,
  process1: CancellablePaidProcess,
  process2: CancellablePaidProcess,
){
  test.test(`Multi Payment: Both Cancelled`, async (subTest)=>{
    subTest.ok(
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow > 0n,
      "Expected cost of process1 should be greater than zero"
    );
    subTest.ok(
      process2.expectedCost.inputCost + process2.expectedCost.outputEscrow > 0n,
      "Expected cost of process2 should be greater than zero"
    );

    // Calculate total expected cost
    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 dollar buffer
        + NANO_MULTIPLIER
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    const initialBalance = await getUserBalance(userFetch);
    subTest.equal(
      initialBalance,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 dollar buffer
        + NANO_MULTIPLIER
      ),
      "Set User balance should match expected"
    );
    subTest.ok(
      initialBalance >= (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have sufficient funds"
    );

    const process1Result = await process1.startProcess(subTest);
    const after1Balance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= after1Balance,
      "User should have less balance"
    );
    subTest.equal(
      initialBalance - after1Balance,
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );
    const process2Result = await process2.startProcess(subTest);
    const after2Balance = await getUserBalance(userFetch);
    subTest.ok(
      after1Balance >= after2Balance,
      "User should have less balance"
    );
    subTest.equal(
      after1Balance - after2Balance,
      process2.expectedCost.inputCost + process2.expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );

    await process1Result.cancelProcess(subTest);
    await process2Result.cancelProcess(subTest);

    await Promise.all([
      process1Result.waitForCompletion,
      process2Result.waitForCompletion,
    ]);
    subTest.pass(`✅ Both processes completed successfully`);

    // Verify refund occurred (balance should be back to initial or close to it)
    const finalBalance = await getUserBalance(userFetch);
    subTest.equal(
      finalBalance,
      initialBalance,
      "Balance should be refunded to initial amount"
    );
  });
}



/**
 * Test: First Process Payment Succeeds, Second Process Payment Fails
 */
export async function test1Cancel2PaymentFailure(
  test: Test,
  userFetch: AuthFetch,
  process1: CancellablePaidProcess,
  process2: CancellablePaidProcess
){
  test.test(`Multi Payment: First cancels, second payment fails`, async (subTest)=>{
    subTest.ok(
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow > 0n,
      "Expected cost of process1 should be greater than zero"
    );
    subTest.ok(
      process2.expectedCost.inputCost + process2.expectedCost.outputEscrow > 0n,
      "Expected cost of process2 should be greater than zero"
    );

    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 cent short
        - 1n
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    const initialBalance = await getUserBalance(userFetch);
    subTest.equal(
      initialBalance,
      (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
        // 1 cent short
        - 1n
      ),
      "Set User balance should match expected"
    );
    subTest.ok(
      initialBalance >= (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
      ),
      "User should have sufficient funds for process 1 alone"
    );
    subTest.ok(
      initialBalance >= (
        process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have sufficient funds for process 2 alone"
    );

    subTest.ok(
      initialBalance < (
        process1.expectedCost.inputCost + process1.expectedCost.outputEscrow
        + process2.expectedCost.inputCost + process2.expectedCost.outputEscrow
      ),
      "User should have insufficient funds for both processes"
    );

    const process1Result = await process1.startProcess(subTest);
    const after1Balance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= after1Balance,
      "User should have less balance"
    );
    subTest.equal(
      initialBalance - after1Balance,
      process1.expectedCost.inputCost + process1.expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );

    try {
      await process2.startProcess(subTest);
      throw new Error("Should have failed");
    }catch(e){
      if(typeof e !== "object" || e === null || Array.isArray(e)){
        throw e;
      }
      if(!("statusCode" in e) || typeof e.statusCode !== "number" || e.statusCode !== 402){
        throw e;
      }
      subTest.pass("Process should fail with payment error");
    }

    await process1Result.cancelProcess(subTest);

    await process1Result.waitForCompletion;

    // Verify money was deducted correctly
    const finalBalance = await getUserBalance(userFetch);
    subTest.equal(
      finalBalance,
      initialBalance,
      "Balance should be refunded to initial amount"
    );
  });
}

