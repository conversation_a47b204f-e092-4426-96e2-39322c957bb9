import { Test } from "tap";
import { ToolInitialCosts } from "@divinci-ai/tools";

export type StartProcessReturnWait = (t: Test) => Promise<{
  waitForCompletion: Promise<any>,
}>;

export type PaidProcess = {
  expectedCost: ToolInitialCosts,
  startProcess: StartProcessReturnWait,
};

export type StartProcessReturnCancelWait = (t: Test) => Promise<{
  cancelProcess: (test: Test) => Promise<any>,
  waitForCompletion: Promise<any>,
}>;

export type CancellablePaidProcess = {
  expectedCost: ToolInitialCosts,
  startProcess: StartProcessReturnCancelWait,
};
