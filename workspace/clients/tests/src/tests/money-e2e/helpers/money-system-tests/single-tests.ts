import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";
import { getUserBalance, setFundsForUser } from "../../../../story-test/util/money-setup";
import { NANO_MULTIPLIER, ToolInitialCosts, ToolFinalCosts } from "@divinci-ai/tools";

import {
  StartProcessReturnWait, StartProcessReturnCancelWait
} from "./types";

/**
 * Test: Payment Succeed, Process Successful, No Refund
 */
export async function testPaymentSuccessProcessSuccess(
  test: Test,
  userFetch: AuthFetch,
  expectedCost: ToolInitialCosts,
  finalCost: ToolFinalCosts | null,
  executeProcess: StartProcessReturnWait,
){
  test.test(`Single Payment: Success`, async (subTest)=>{
    // Setup: Set exact funds needed plus buffer
    subTest.ok(
      expectedCost.inputCost + expectedCost.outputEscrow > 0n,
      "Expected cost should be greater than zero"
    );
    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        expectedCost.inputCost + expectedCost.outputEscrow
        // 1 dollar buffer
        + NANO_MULTIPLIER
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    // Get initial balance
    const initialBalance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= expectedCost.inputCost + expectedCost.outputEscrow,
      "User should have sufficient funds"
    );
    subTest.equal(
      initialBalance,
      expectedCost.inputCost + expectedCost.outputEscrow + NANO_MULTIPLIER,
      "Set User balance should match expected"
    );

    // Execute the process
    const { waitForCompletion } = await executeProcess(subTest);

    const updatedBalance = await getUserBalance(userFetch);
    subTest.ok(
      updatedBalance < initialBalance,
      "Balance should be less after initial cost is deducted"
    );
    subTest.equal(
      initialBalance - updatedBalance,
      expectedCost.inputCost + expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );


    await waitForCompletion;

    // Verify money was deducted correctly
    const finalBalance = await getUserBalance(userFetch);
    const deducted = initialBalance - finalBalance;
    subTest.ok(deducted >= 0n, "Should have deducted some money");
    if(expectedCost.outputEscrow === 0n) {
      subTest.equal(
        deducted, expectedCost.inputCost,
        "Should deduct exactly the input cost"
      );
    } else {
      subTest.ok(
        deducted <= expectedCost.inputCost + expectedCost.outputEscrow,
        "Should not deduct more than inputCost + outputEscrow"
      );
      if(finalCost !== null){
        subTest.equal(
          deducted, expectedCost.inputCost + finalCost.outputCost,
          "Should deduct exactly the input cost + final output cost"
        );
      }
    }

    subTest.ok(finalBalance >= 0n, "Balance should not go negative");

  });
}

/**
 * Test: Payment Succeed, Process Failure, Payment Refunded
 */
export async function testPaymentSuccessProcessFailureRefund(
  test: Test,
  userFetch: AuthFetch,
  expectedCost: ToolInitialCosts,
  startProcess: StartProcessReturnWait
){
  test.test(`Single Payment: Process Failure with Refund`, async (subTest)=>{
    // Setup: Set exact funds needed plus buffer
    subTest.ok(
      expectedCost.inputCost + expectedCost.outputEscrow > 0n,
      "Expected cost should be greater than zero"
    );
    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        expectedCost.inputCost + expectedCost.outputEscrow
        // 1 dollar buffer
        + NANO_MULTIPLIER
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    // Get initial balance
    const initialBalance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= expectedCost.inputCost + expectedCost.outputEscrow,
      "User should have sufficient funds"
    );
    subTest.equal(
      initialBalance,
      expectedCost.inputCost + expectedCost.outputEscrow + NANO_MULTIPLIER,
      "Set User balance should match expected"
    );

    const { waitForCompletion } = await startProcess(subTest);

    const updatedBalance = await getUserBalance(userFetch);
    subTest.ok(
      updatedBalance < initialBalance,
      "Balance should be less after initial cost is deducted"
    );
    subTest.equal(
      initialBalance - updatedBalance,
      expectedCost.inputCost + expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );

    await waitForCompletion;

    // Verify refund occurred (balance should be back to initial or close to it)
    const finalBalance = await getUserBalance(userFetch);
    subTest.equal(
      finalBalance,
      initialBalance,
      "Balance should be refunded to initial amount"
    );
  });
}

/**
 * Test: Payment Succeed, Process Cancelled, Payment Refunded
 */
export async function testPaymentSuccessProcessCancelledRefund(
  test: Test,
  userFetch: AuthFetch,
  expectedCost: ToolInitialCosts,
  startProcess: StartProcessReturnCancelWait,
){
  test.test(`Single Payment: Process Cancelled with Refund`, async (subTest)=>{
    // Setup: Set exact funds needed plus buffer
    subTest.ok(
      expectedCost.inputCost + expectedCost.outputEscrow > 0n,
      "Expected cost should be greater than zero"
    );
    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        expectedCost.inputCost + expectedCost.outputEscrow
        // 1 dollar buffer
        + NANO_MULTIPLIER
      )
    );
    subTest.pass(`💰 Set up user with sufficient funds`);

    // Get initial balance
    const initialBalance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance >= expectedCost.inputCost + expectedCost.outputEscrow,
      "User should have sufficient funds"
    );
    subTest.equal(
      initialBalance,
      expectedCost.inputCost + expectedCost.outputEscrow + NANO_MULTIPLIER,
      "Set User balance should match expected"
    );

    const { waitForCompletion, cancelProcess } = await startProcess(subTest);

    const updatedBalance = await getUserBalance(userFetch);
    subTest.ok(
      updatedBalance < initialBalance,
      "Balance should be less after initial cost is deducted"
    );
    subTest.equal(
      initialBalance - updatedBalance,
      expectedCost.inputCost + expectedCost.outputEscrow,
      "Should deducted the expected amount"
    );

    await cancelProcess(subTest);

    await waitForCompletion;

    // Verify refund occurred (balance should be back to initial or close to it)
    const finalBalance = await getUserBalance(userFetch);
    subTest.equal(
      finalBalance,
      initialBalance,
      "Balance should be refunded to initial amount"
    );
  });
}

/**
 * Test: Payment Failure, No Process
 */
export async function testPaymentFailureNoProcess(
  test: Test,
  userFetch: AuthFetch,
  expectedCost: ToolInitialCosts,
  failProcess: StartProcessReturnWait,
){
  test.test(`Single Payment: Payment Failure`, async (subTest)=>{
    // Setup: Set insufficient funds (less than required)
    subTest.ok(
      expectedCost.inputCost + expectedCost.outputEscrow > 0n,
      "Expected cost should be greater than zero"
    );
    await setFundsForUser(
      userFetch,
      userFetch.userId,
      (
        expectedCost.inputCost + expectedCost.outputEscrow - 1n
      )
    );

    // Get initial balance
    const initialBalance = await getUserBalance(userFetch);
    subTest.ok(
      initialBalance < expectedCost.inputCost + expectedCost.outputEscrow,
      "User should have insufficient funds"
    );
    subTest.equal(
      initialBalance,
      expectedCost.inputCost + expectedCost.outputEscrow - 1n,
      "Set User balance should match expected"
    );

    try {
      await failProcess(subTest);
      throw new Error("Should have failed");
    }catch(e){
      if(typeof e !== "object" || e === null || Array.isArray(e)){
        throw e;
      }
      if(!("statusCode" in e) || typeof e.statusCode !== "number" || e.statusCode !== 402){
        throw e;
      }
      subTest.pass("Process should fail with payment error");
    }

    // Verify no money was deducted
    const finalBalance = await getUserBalance(userFetch);
    subTest.equal(
      finalBalance, initialBalance,
      "No money should be deducted on payment failure"
    );
  });
}

