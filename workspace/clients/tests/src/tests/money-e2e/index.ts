import { Test } from "tap";

import { AuthFetch, authFetch } from "../../globals/auth0";
import { AUTH0_USERS } from "../../constants/auth0";

import { setFundsForUser } from "../../story-test/util/money-setup";

// Import individual test suites
import { runTest as aiAssistantsTests } from "./ai-assistants";
import { runTest as audioTranscriptionTests } from "./audio-transcription";
import { runTest as fineTuneTests } from "./fine-tune";
import { runTest as ragChunksTests } from "./rag-chunks";
import { runTest as ragUrlScraperTests } from "./rag-url-scraper";

const MONEY_E2E_TEST_LIST: Array<{
  name: string,
  run: (test: Test, user1: AuthFetch) => Promise<any>,
}> = [
  { name: "AI Assistants", run: aiAssistantsTests },
  { name: "Speaker Sensitive Audio Transcription", run: audioTranscriptionTests },
  // { name: "Fine Tune Models", run: fineTuneTests },
  { name: "RAG Raw to Chunks", run: ragChunksTests },
  { name: "RAG URL Scraper", run: ragUrlScraperTests },
];

export async function runTest(test: Test){
  const [user1] = await Promise.all([
    authFetch(AUTH0_USERS[0]),
  ]);

  for(const testSuite of MONEY_E2E_TEST_LIST) {
    // Reset money models before each test suite
    await setFundsForUser(user1, user1.userId, 0n);

    await test.test(testSuite.name, async (subTest)=>{
      await testSuite.run(subTest, user1);
    });
  }
}
