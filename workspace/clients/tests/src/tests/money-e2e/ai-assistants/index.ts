import { Test } from "tap";
import { AuthFetch } from "../../../globals/auth0";

// Test functions
import { testAIAssistantGPT4o } from "./tool-optons/gpt-4o";
import { testAIAssistantGPT4oMini } from "./tool-optons/gpt-4o-mini";
import { testAIAssistantDALLE3 } from "./tool-optons/dalle-3";
import { testAIAssistantDALLE2 } from "./tool-optons/dalle-2";
import { testAIAssistantMermaid } from "./tool-optons/mermaid";

const AI_ASSISTANTS_TESTS = [
  // { name: "AI Assistant GPT-4o", fn: testAIAssistantGPT4o },
  { name: "AI Assistant GPT-4o-mini", fn: testAIAssistantGPT4oMini },
  // { name: "AI Assistant DALL-E 3", fn: testAIAssistantDALLE3 },
  // { name: "AI Assistant DALL-E 2", fn: testAIAssistantDALLE2 },
  // { name: "AI Assistant Mermaid", fn: testAIAssistantMermaid },
];

export async function runTest(test: Test, user1: AuthFetch){
  for(const testCase of AI_ASSISTANTS_TESTS) {
    await testCase.fn(test, user1);
  }
}
