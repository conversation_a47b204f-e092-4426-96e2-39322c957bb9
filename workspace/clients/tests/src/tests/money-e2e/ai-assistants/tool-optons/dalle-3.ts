import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";

import { TEST_PROMPTS, initializeChatMessage, pollChatForMessage, runAssistantTests } from "../helpers";

import {
  GeneratorAssistantInput,
  OpenAIDalle3Assistant,
} from "@divinci-ai/tools";
import { TRANSCRIPT_MESSAGE_CATEGORY_ENUM } from "@divinci-ai/models";

/**
 * Test that AI Assistant DALL-E 3 works with sufficient funds
 */

const ASSISTANT_INPUT: GeneratorAssistantInput = {
  thread: [
    {
      role: "user",
      content: TEST_PROMPTS.image,
      category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM.TEXT,
      user: "anonymous"
    },
  ],
  responseValues: { id: "0" },
};

const ASSISTANT_OUTPUT = { text: "doesn't matter" };

export async function testAIAssistantDALLE3(parentTest: Test, userFetch: AuthFetch){
  await parentTest.test("DALL-E 3", async (test)=>{
    const expectedCosts = await OpenAIDalle3Assistant.pricing.getEstimatedCost(
      {}, ASSISTANT_INPUT
    );

    const finalCosts = await OpenAIDalle3Assistant.pricing.getFinalCost(
      {}, ASSISTANT_INPUT, ASSISTANT_OUTPUT,
    );

    runAssistantTests(
      test, userFetch, expectedCosts, finalCosts,
      (test)=>(successfulProcess(test, userFetch)),
      (test)=>(failedProcess(test, userFetch)),
      (test)=>(failedPayment(test, userFetch)),
    );
  });
}

async function successfulProcess(test: Test, userFetch: AuthFetch){
  const { chat, messageIds } = await initializeChatMessage(
    userFetch,
    OpenAIDalle3Assistant.id,
    TEST_PROMPTS.image,
    { delay: true, fail: false }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollChatForMessage(
        userFetch, chat._id, messageIds.responseId
      );
      test.equal(message.role, "assistant", "Should return an assistant response");
      test.ok(message.content && message.content.length > 0, "Should return image URL");
      test.ok(URL.canParse(message.content), "Should return a valid URL");
    }),
  };
}

async function failedProcess(test: Test, userFetch: AuthFetch){
  const { chat, messageIds } = await initializeChatMessage(
    userFetch,
    OpenAIDalle3Assistant.id,
    TEST_PROMPTS.image,
    { delay: true, fail: true }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollChatForMessage(
        userFetch, chat._id, messageIds.responseId
      );
      test.equal(message.role, "error", "Should have thrown an error");
    })
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch): Promise<any>{
  await initializeChatMessage(
    userFetch,
    OpenAIDalle3Assistant.id,
    TEST_PROMPTS.image,
  );
  throw new Error("Should have thrown an error");
}
