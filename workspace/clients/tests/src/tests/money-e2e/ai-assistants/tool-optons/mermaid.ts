import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";

import { TEST_PROMPTS, initializeChatMessage, pollChatForMessage, runAssistantTests } from "../helpers";

import {
  GeneratorAssistantInput,
  OpenAIGPT4oMiniMermaid,
} from "@divinci-ai/tools";
import { TRANSCRIPT_MESSAGE_CATEGORY_ENUM } from "@divinci-ai/models";

/**
 * Test that AI Assistant Mermaid diagram generation works with sufficient funds
 */

const ASSISTANT_INPUT: GeneratorAssistantInput = {
  thread: [
    {
      role: "user",
      content: TEST_PROMPTS.diagram.prompt,
      category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM.DIAGRAM,
      user: "anonymous"
    },
  ],
  responseValues: { id: "0" },
};


export async function testAIAssistantMermaid(parentTest: Test, userFetch: AuthFetch){
  await parentTest.test("GPT-4o Mermaid", async (test)=>{
    const expectedCosts = await OpenAIGPT4oMiniMermaid.pricing.getEstimatedCost(
      {}, ASSISTANT_INPUT
    );

    runAssistantTests(
      test, userFetch, expectedCosts, null,
      (test)=>(successfulProcess(test, userFetch)),
      (test)=>(failedProcess(test, userFetch)),
      (test)=>(failedPayment(test, userFetch)),
    );
  });
}

async function successfulProcess(test: Test, userFetch: AuthFetch){
  const { chat, messageIds } = await initializeChatMessage(
    userFetch,
    OpenAIGPT4oMiniMermaid.id,
    TEST_PROMPTS.diagram.prompt,
    { delay: true, fail: false }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollChatForMessage(
        userFetch, chat._id, messageIds.responseId
      );
      test.equal(message.role, "assistant", "Should return an assistant response");
      test.ok(message.content && message.content.length > 0, "Should return content");
      const found = TEST_PROMPTS.diagram.response.includes(message.content);
      test.ok(found, "Should return expected content");
    }),
  };
}

async function failedProcess(test: Test, userFetch: AuthFetch){
  const { chat, messageIds } = await initializeChatMessage(
    userFetch,
    OpenAIGPT4oMiniMermaid.id,
    TEST_PROMPTS.diagram.prompt,
    { delay: true, fail: true }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollChatForMessage(
        userFetch, chat._id, messageIds.responseId
      );
      test.equal(message.role, "error", "Should have thrown an error");
    })
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch): Promise<any>{
  await initializeChatMessage(
    userFetch,
    OpenAIGPT4oMiniMermaid.id,
    TEST_PROMPTS.diagram.prompt,
  );
  throw new Error("Should have failed");
}
