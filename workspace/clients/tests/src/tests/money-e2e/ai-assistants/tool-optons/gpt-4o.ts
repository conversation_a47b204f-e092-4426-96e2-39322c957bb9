import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";

import { TEST_PROMPTS, initializeChatMessage, pollChatForMessage, runAssistantTests } from "../helpers";

import {
  GeneratorAssistantInput,
  OpenAIGPT4oAssistant,
} from "@divinci-ai/tools";
import { TRANSCRIPT_MESSAGE_CATEGORY_ENUM } from "@divinci-ai/models";

/**
 * Test that AI Assistant GPT-4o works with sufficient funds
 */

const ASSISTANT_INPUT: GeneratorAssistantInput = {
  thread: [
    {
      role: "user",
      content: TEST_PROMPTS.text.prompt,
      category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM.TEXT,
      user: "anonymous"
    },
  ],
  responseValues: { id: "0" },
};

const ASSISTANT_OUTPUT = { text: TEST_PROMPTS.text.response };


export async function testAIAssistantGPT4o(parentTest: Test, userFetch: AuthFetch){
  await parentTest.test("GPT-4o", async (test)=>{
    const expectedCosts = await OpenAIGPT4oAssistant.pricing.getEstimatedCost(
      {}, ASSISTANT_INPUT
    );

    const finalCosts = await OpenAIGPT4oAssistant.pricing.getFinalCost(
      {}, ASSISTANT_INPUT, ASSISTANT_OUTPUT,
    );

    runAssistantTests(
      test, userFetch, expectedCosts, finalCosts,
      (test)=>(successfulProcess(test, userFetch)),
      (test)=>(failedProcess(test, userFetch)),
      (test)=>(failedPayment(test, userFetch)),
    );
  });
}

async function successfulProcess(test: Test, userFetch: AuthFetch){
  const { chat, messageIds } = await initializeChatMessage(
    userFetch,
    OpenAIGPT4oAssistant.id,
    TEST_PROMPTS.text.prompt,
    { delay: true, fail: false }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollChatForMessage(
        userFetch, chat._id, messageIds.responseId
      );
      test.equal(message.role, "assistant", "Should return an assistant response");
      test.equal(message.content, TEST_PROMPTS.text.response, "Should return expected content");
    }),
  };
}

async function failedProcess(test: Test, userFetch: AuthFetch){
  const { chat, messageIds } = await initializeChatMessage(
    userFetch,
    OpenAIGPT4oAssistant.id,
    TEST_PROMPTS.text.prompt,
    { delay: true, fail: true }
  );

  return {
    waitForCompletion: Promise.resolve().then(async ()=>{
      const message = await pollChatForMessage(
        userFetch, chat._id, messageIds.responseId
      );
      test.equal(message.role, "error", "Should have thrown an error");
    })
  };
}

async function failedPayment(test: Test, userFetch: AuthFetch): Promise<any>{
  await initializeChatMessage(
    userFetch,
    OpenAIGPT4oAssistant.id,
    TEST_PROMPTS.text.prompt,
  );
  throw new Error("Should have failed");
}
