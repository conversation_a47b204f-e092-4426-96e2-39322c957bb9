import { delay, DIVINCI_TEST_PROCESS_Config } from "@divinci-ai/utils";
import { aichatGetTranscript } from "@divinci-ai/actions";

import { AuthFetch } from "../../../globals/auth0";
import { uniqueId } from "@divinci-ai/utils";

import { aichatCreate, aichatAddMessage } from "@divinci-ai/actions";

import { ToolFinalCosts, ToolInitialCosts } from "@divinci-ai/tools";
import { StartProcessReturnWait } from "../helpers/money-system-tests";
import {
  testPaymentSuccessProcessSuccess,
  testPaymentSuccessProcessFailureRefund,
  testPaymentFailureNoProcess,
  testMultipleProcessesSuccess,
  test1Success2PaymentFailure,
  test1Refund2PaymentFailure,
} from "../helpers/money-system-tests";
import { Test } from "tap";


/**
 * Test prompts for AI assistants
 */
export const TEST_PROMPTS = {
  text: {
    prompt: "Hello, please respond with exactly 'Hello World!'",
    response: "Hello World!"
  },
  image: "A simple red circle on a white background",
  diagram: {
    prompt: "Create a simple flowchart with Start -> Process -> End",
    response: [
      "flowchart TD\n    A[Start] --> B[Process]\n    B --> C[End]",
      "graph TD;\n    A[Start] --> B[Process];\n    B --> C[End];",
      "graph TD\n    A[Start] --> B[Process]\n    B --> C[End]"
    ],
  }
};


export async function initializeChatMessage(
  userFetch: AuthFetch,
  assistantId: string,
  prompt: string,
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const { chat } = await aichatCreate(userFetch, {
    title: `Test AI Chat ${uniqueId()}`,
    releases: [] // No releases needed for direct AI assistant usage
  });

  const messageIds = await aichatAddMessage(
    userFetch,
    { chatId: chat._id },
    {
      content: prompt,
      assistantName: assistantId
    },
    testConfig
  );

  return { chat, messageIds };
}

export async function pollChatForMessage(
  fetcher: typeof fetch, chatId: string, responseId: string, maxRetries = 20
){
  for(let i = 0; i < maxRetries; i++, await delay(1000)){
    const transcript = await aichatGetTranscript(fetcher, { chatId });
    const message = transcript.messages.find((message)=>(message._id === responseId));
    if(!message) continue;
    return message;
  }
  throw new Error("Timed out");
}




export function runAssistantTests(
  test: Test,
  userFetch: AuthFetch,
  expectedCosts: ToolInitialCosts,
  finalCosts: ToolFinalCosts | null,
  successfulProcess: StartProcessReturnWait,
  failedProcess: StartProcessReturnWait,
  failedPayment: StartProcessReturnWait,
){
  testPaymentSuccessProcessSuccess(
    test, userFetch, expectedCosts, finalCosts, successfulProcess
  );

  testPaymentSuccessProcessFailureRefund(
    test, userFetch, expectedCosts, failedProcess
  );

  testPaymentFailureNoProcess(
    test, userFetch, expectedCosts, failedPayment
  );

  testMultipleProcessesSuccess(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    }
  );
  test1Success2PaymentFailure(test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: successfulProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
  test1Refund2PaymentFailure(
    test, userFetch,
    {
      expectedCost: expectedCosts,
      startProcess: failedProcess,
    },
    {
      expectedCost: expectedCosts,
      startProcess: failedPayment,
    }
  );
}
