import React, {
  createContext, useContext, PropsWithChildren,
  useCallback,
} from "react";

import {
  TrackedValueTypes, TrackedValueState, useTrackedGetter
} from "../../../util/react/tracked-value";

const UsersMoneyContext = createContext<
  TrackedValueTypes<{ balance: bigint }> & { update: ()=>any }
>({
  state: TrackedValueState.Failure,
  value: void 0,
  loading: false,
  error: { message: "No Provider" },
  update: ()=>(Promise.reject("No User")),
});

export function useUsersMoney(){
  return useContext(UsersMoneyContext);
}

import { useAuth0 } from "@auth0/auth0-react";
import { useAuth0Fetch } from "../../auth0-user";
import { getUsersBalance } from "@divinci-ai/actions";
export function UsersMoneyProvider({ children }: PropsWithChildren){
  const { isAuthenticated } = useAuth0();
  const auth0Fetch = useAuth0Fetch();

  const fetchState = useTrackedGetter({
    getter: !isAuthenticated ? void 0 : useCallback(()=>(
      getUsersBalance(auth0Fetch)
    ), [auth0Fetch]),
    eraseLastValue: false
  });
  /*
  const { value: wsValue } = useTrackedWebsocket<{ balance: NanoUSDMoneyParts }>({
    requiresAuth: true,
    url: !isAuthenticated ? void 0 : "/user/money",
  });
  */

  return (
    <UsersMoneyContext.Provider
      value={fetchState}
    >
      { children }
    </UsersMoneyContext.Provider>
  );
}
