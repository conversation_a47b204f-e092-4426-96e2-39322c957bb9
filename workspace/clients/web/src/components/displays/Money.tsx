import React from "react";

import { amountSingleToParts, NANO_MULTIPLIER } from "@divinci-ai/tools";
const NANO_LENGTH = NANO_MULTIPLIER.toString().length - 1;
export function NanoUSDToDollarString(
  { nano, sliceNano = Number.POSITIVE_INFINITY }: { nano: bigint, sliceNano?: number }
){
  const negative = nano < 0n;
  if(negative) nano = -nano;
  const parts = amountSingleToParts(nano);
  const nanoStr = parts.nano.toString();
  const toPad = NANO_LENGTH - nanoStr.length;
  const nanoString = ("0".repeat(toPad) + nanoStr).slice(0, sliceNano);
  return <>{negative ? "-" : ""}${parts.usd.toString()}.{nanoString}</>;
}
