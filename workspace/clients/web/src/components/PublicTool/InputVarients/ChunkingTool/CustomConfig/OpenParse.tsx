import React from "react";
import { InputProps } from "../../../../../util/react/input";
import { SharedConfig } from "./SharedConfig";
import { OpenParseConfig } from "@divinci-ai/tools";



const DEFAULT_PROVIDER = "ollama";

export function OpenParseCustomConfig({ value, onChange }: InputProps<OpenParseConfig>){
  return (
    <>
      <SharedConfig value={value} onChange={(newValues)=>(onChange({ ...value, ...newValues }))} />
      <div className="field">
        <label className="label">Semantic Chunking</label>
        <div className="control">
          <label className="checkbox">
            <input
              type="checkbox"
              checked={value.semantic || false}
              onChange={(e)=>(onChange({
                ...value, semantic: e.target.checked,
                embeddingsProvider: e.target.checked ? DEFAULT_PROVIDER : "none"
              }))}
            />
            {" "}Enable semantic chunking
          </label>
        </div>
      </div>

      {value.semantic && (
        <div className="field">
          <label className="label">Embeddings Provider</label>
          <div className="control">
            <div className="select">
              <select
                value={value.embeddingsProvider || "ollama"}
                onChange={(e)=>(onChange({
                  ...value,
                  embeddingsProvider: e.target.value as OpenParseConfig["embeddingsProvider"]
                }))}
              >
                <option value="ollama">Ollama</option>
                <option value="cloudflare">Cloudflare</option>
                <option value="none">None</option>
              </select>
            </div>
          </div>
        </div>
      )}
      <div className="field">
        <label className="label">Minimum Tokens</label>
        <div className="control">
          <input
            className="input"
            type="number"
            placeholder={"256"}
            min="1"
            max={(value.maxTokens) -1}
            value={value.minTokens}
            onChange={(e)=>(onChange({
              ...value,
              minTokens: parseInt(e.target.value)
            }))}
          />
        </div>
        <p className="help">Minimum number of tokens per chunk (recommended: 256)</p>
      </div>

      <div className="field">
        <label className="label">Maximum Tokens</label>
        <div className="control">
          <input
            className="input"
            type="number"
            placeholder={"512"}
            min={(value.minTokens) +1}
            value={value.maxTokens}
            onChange={(e)=>(onChange({
              ...value,
              maxTokens: parseInt(e.target.value)
            }))}
          />
        </div>
        <p className="help">Maximum number of tokens per chunk (recommended: 1024)</p>
      </div>

      {/* Chunk Overlap Field with percentage calculation based on midpoint */}
      <div className="field">
        <label className="label">Chunk Overlap</label>
        <div className="control">
          <input
            className="input"
            type="number"
            placeholder="125"
            min="0"
            max={value.maxTokens}
            value={value.chunkOverlap}
            onChange={(e)=>(onChange({
              ...value,
              chunkOverlap: parseInt(e.target.value)
            }))}
          />
        </div>
        <p className="help">
          {(()=>{
            const minToks = value.minTokens;
            const maxToks = value.maxTokens;
            const avgTokens = (minToks + maxToks) / 2;
            const overlap = value.chunkOverlap;
            const overlapPercentage = Math.round((overlap / avgTokens) * 100);

            return (
              <>
                Number of tokens to overlap between chunks
                ({overlapPercentage}% of average chunk size)
                <br/>
                <span className={
                  overlapPercentage >= 20 && overlapPercentage <= 25
                    ? "has-text-success"
                    : "has-text-warning"
                }>
                  Recommended: 20-25% overlap for structured documents
                </span>
              </>
            );
          })()}
        </p>
      </div>

      {/* Use Tokens vs Characters Toggle */}
      <div className="field">
        <label className="label">Chunking Method</label>
        <div className="control">
          <label className="radio">
            <input
              type="radio"
              name="chunkingMethod"
              checked={value.useTokens !== false}
              onChange={()=>(onChange({
                ...value,
                useTokens: true
              }))}
            />
            {" "}Token-based chunking
          </label>
          <label className="radio ml-4">
            <input
              type="radio"
              name="chunkingMethod"
              checked={value.useTokens === false}
              onChange={()=>(onChange({
                ...value,
                useTokens: false
              }))}
            />
            Character-based chunking
          </label>
        </div>
        <p className="help">
          Token-based chunking is more accurate for LLM context windows. <br/>
          Character-based may be better for specific use cases.
        </p>
      </div>
    </>
  );
}
