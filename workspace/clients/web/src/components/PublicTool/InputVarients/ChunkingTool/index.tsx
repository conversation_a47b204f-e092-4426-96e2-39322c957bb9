import React, { useEffect } from "react";

import { InputProps } from "../../../../util/react/input";
import { PublicToolSelect } from "../..";

import { ShallowObject } from "@divinci-ai/utils";

import { RAG_RAW_TO_CHUNKS, RawToChunksInput } from "@divinci-ai/tools";
import { UnstructuredCustomConfig } from "./CustomConfig/Unstructured";
import { OpenParseCustomConfig } from "./CustomConfig/OpenParse";
import { getInitialToolValue } from "@divinci-ai/tools";

export const RAW_TO_CHUNKS_LIST = Object.values(RAG_RAW_TO_CHUNKS);

export function getDefaultChunkingTool(id?: string | null){
  return getInitialToolValue(RAG_RAW_TO_CHUNKS, id);
}

export function ChunkingToolSelect(
  { value, onChange, input }: InputProps<{ id: string, config: ShallowObject }> & { input?: RawToChunksInput }
){

  useEffect(()=>{
    if(RAW_TO_CHUNKS_LIST.length === 0) return;
    if(value) return;
    onChange(getDefaultChunkingTool());
  }, []);

  return (
    <PublicToolSelect<RawToChunksInput>
      value={value}
      onChange={onChange}
      available={RAW_TO_CHUNKS_LIST}
      input={input}
      customConfigInputs={{
        "@unstructured": UnstructuredCustomConfig,
        "@divinci-ai/openparse": OpenParseCustomConfig,
      }}
    />
  );
}
