import React from "react";
import { InputProps } from "../../../../../util/react/input";
import { SharedConfig } from "./SharedConfig";
import { UnstructuredConfig } from "@divinci-ai/tools";

export function UnstructuredCustomConfig({ value, onChange }: InputProps<UnstructuredConfig>){
  return (
    <>
      <SharedConfig value={value} onChange={(newValues)=>(onChange({ ...value, ...newValues }))} />
      <div className="field">
        <label className="label">Chunking Strategy</label>
        <div className="control">
          <div className="select">
            <select
              value={value.chunkingStrategy}
              onChange={(e)=>(onChange({
                ...value,
                chunkingStrategy: e.target.value as UnstructuredConfig["chunkingStrategy"]
              }))}
            >
              <option value="by_title">By Title</option>
              <option value="by_page">By Page</option>
              <option value="by_similarity">By Similarity</option>
              <option value="by_character">By Character</option>
            </select>
          </div>
          <p className="help">
            {value.chunkingStrategy === "by_title" && "Preserves section boundaries when determining chunks' contents"}
            {value.chunkingStrategy === "by_page" && "Preserves page boundaries when determining chunks' contents"}
            {value.chunkingStrategy === "by_similarity" && "Uses embeddings to identify and combine topically similar sequential elements"}
            {value.chunkingStrategy === "by_character" && "Basic strategy using only max characters to combine sequential elements"}
          </p>
        </div>
      </div>

      <div className="field">
        <label className="label">Max Characters</label>
        <div className="control">
          <input
            className="input"
            type="number"
            value={value.maxCharacters}
            onChange={(e)=>(onChange({
              ...value,
              maxCharacters: parseInt(e.target.value)
            }))}
          />
        </div>
        <p className="help">Maximum number of characters in a chunk</p>
      </div>

      {value.chunkingStrategy === "by_similarity" && (
        <div className="field">
          <label className="label">Similarity Threshold</label>
          <div className="control">
            <input
              className="input"
              type="number"
              min="0.01"
              max="0.99"
              step="0.01"
              value={value.similarityThreshold}
              onChange={(e)=>(onChange({
                ...value,
                similarityThreshold: parseFloat(e.target.value)
              }))}
            />
          </div>
          <p className="help">Minimum similarity (0.01-0.99) required between consecutive elements to be included in the same chunk</p>
        </div>
      )}

      <div className="field">
        <label className="label">Advanced Options</label>
        <div className="control">
          <label className="checkbox">
            <input
              type="checkbox"
              checked={value.includeOriginalElements}
              onChange={(e)=>(onChange({
                ...value,
                includeOriginalElements: e.target.checked
              }))}
            />
            {" "}Include original elements in metadata
          </label>
        </div>

        {value.chunkingStrategy === "by_title" && (
          <div className="control mt-2">
            <label className="checkbox">
              <input
                type="checkbox"
                checked={value.multipageSections}
                onChange={(e)=>(onChange({
                  ...value,
                  multipageSections: e.target.checked
                }))}
              />
              {" "}Allow sections to span multiple pages
            </label>
          </div>
        )}
      </div>
    </>
  );
}
