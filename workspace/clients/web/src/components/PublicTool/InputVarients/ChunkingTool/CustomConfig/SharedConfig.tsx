import React from "react";
import { InputProps } from "../../../../../util/react/input";

type CommonConfig = {
  skipDeJunk: boolean,
  semantic_chunking: boolean,
  relevanceThreshold: number,
};


import toolStyles from "./ChunkingToolSelect.module.css";

export function SharedConfig({ value, onChange }: InputProps<CommonConfig>){
  return (
    <>
      <div className="field">
        <label className="label">Common Options</label>

        <div className="control mb-3">
          <label className={toolStyles.tooltip}>
            <input
              type="checkbox"
              checked={value.skipDeJunk}
              onChange={(e)=>(onChange({
                ...value,
                skipDeJunk: e.target.checked
              }))}
            />
            {" "}Skip Chunk DeJunk
            <span className={toolStyles.infoIcon} />
            <span className={toolStyles.tooltiptext}>
              May help with filtering out irrelevant chunks like page formating and numbers.
              Will add to processing times.
              <br/><br/>
              Uses Hugging Face's DistilBERT SST-2 model to evaluate chunk relevance.
              Chunks with low relevance scores are filtered out to improve quality.
              <br/>
              <a
                href="https://huggingface.co/distilbert-base-uncased-finetuned-sst-2-english"
                target="_blank"
                rel="noopener noreferrer"
                className={toolStyles.tooltipLink}
                onClick={(e)=>e.stopPropagation()}
              >
                Learn more
                <span className="icon is-small ml-1" style={{ fontSize: "0.75rem" }}>
                  <i className="fas fa-external-link-alt" />
                </span>
              </a>
            </span>
          </label>
        </div>

        {/* Relevance Threshold - only show when Skip DeJunk is false */}
        {!value.skipDeJunk && (
          <div className="control">
            <label className={`label ${toolStyles.tooltip}`}>
              Relevance Threshold
              <span className={toolStyles.infoIcon} />
              <span className={toolStyles.tooltiptext}>
                Sets the minimum relevance score (0.01-0.99) for chunks to be included.
                Lower values keep more chunks, higher values are more selective.
              </span>
            </label>
            <input
              className="input is-small"
              style={{ margin: "0 7px" }}
              type="number"
              min="0.01"
              max="0.99"
              step="0.01"
              value={value.relevanceThreshold || 0.3}
              onChange={(e)=>(onChange({
                ...value,
                relevanceThreshold: parseFloat(e.target.value)
              }))}
            />
            <p className="help">
              {value.relevanceThreshold <= 0.3 && "More inclusive - keeps more chunks"}
              {value.relevanceThreshold > 0.3 && value.relevanceThreshold <= 0.6 && "Balanced - moderate filtering"}
              {value.relevanceThreshold > 0.6 && "More selective - keeps only highly relevant chunks"}
            </p>
          </div>
        )}
      </div>
    </>
  );
}
