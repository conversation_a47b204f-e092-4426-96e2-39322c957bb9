.tooltip {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.infoIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  border: 1px solid #666;
  border-radius: 50%;
  margin-left: 0.25rem;
  color: #666;
  font-size: 0.65rem;
  cursor: help;
}

.infoIcon::before {
  content: "i";
  font-family: serif;
  font-style: italic;
  font-size: 0.7rem;
  line-height: 1;
  position: relative;
}

.tooltiptext {
  visibility: hidden;
  background-color: #333;
  color: #fff;
  text-align: center;
  padding: 8px 12px;
  border-radius: 6px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  width: 280px;
  font-size: 0.85rem;
  line-height: 1.4;
  /* Add padding at the bottom to create a safe hover area */
  padding-bottom: 15px;
  /* Ensure the padding area is included in the hover target */
  margin-bottom: -5px;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
}

.tooltiptext::after {
  content: "";
  position: absolute;
  bottom: -10px; /* Position from bottom instead of top */
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

.tooltipLink {
  color: #fff;
  text-decoration: underline;
  margin-left: 4px;
}

.tooltipLink:hover {
  color: #ddd;
}
