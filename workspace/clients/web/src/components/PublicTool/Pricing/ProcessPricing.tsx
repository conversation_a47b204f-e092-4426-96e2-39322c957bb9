import React, { useMemo } from "react";
import { ProcessPricingStrategy } from "@divinci-ai/tools";
import { usePromisedMemo } from "../../../util/react/utility";

import { DisplayItemizedCosts } from "./PublicToolPricing";

import { NanoUSDToDollarString } from "../../displays/Money";

import "./pricing-table.css";
import { ShallowObject } from "@divinci-ai/utils";
import { SpanWithToolTip } from "../../utility/span-with-tooltip";

export function ProcessPricing<ProcessConfig extends ShallowObject, Input, Too<PERSON><PERSON><PERSON> extends string>(
  { pricing, selectedTools, processConfig, input }: {
    pricing: ProcessPricingStrategy<ProcessConfig, Input, any, ToolKey>,
    selectedTools: Record<ToolKey, {
      id: string,
      config: ProcessConfig,
    }>,
    processConfig: ProcessConfig,
    input: undefined | Input,
  }
){
  const toolCosts = usePromisedMemo(
    async ()=>(
      typeof input === "undefined" ? void 0 : (
        await pricing.getEstimatedCost(
          selectedTools, processConfig, input
        )
      )
    ),
    [pricing, selectedTools, processConfig, input]
  );

  const totalCosts = useMemo(
    ()=>{
      if(typeof input === "undefined") return;
      if(toolCosts.status === "pending") return;
      if(toolCosts.status === "failed") return;
      if(toolCosts.value === void 0) return;
      let totalInitial = 0n;
      let totalEscrow = 0n;
      let total = 0n;
      for(const costs of Object.values(toolCosts.value)){
        totalInitial += costs.inputCost;
        totalEscrow += costs.outputEscrow;
        total += costs.inputCost + costs.outputEscrow;
      }
      return { total, totalInitial, totalEscrow };
    },
    [toolCosts, input]
  );

  if(toolCosts.status === "pending") return <div>Loading...</div>;
  if(toolCosts.status === "failed") return <div>Failed to load costs</div>;

  // If input is undefined, then shouldn't display anything
  if(toolCosts.value === void 0) return null;

  if(typeof totalCosts === "undefined") return <div>Failed to calculate total costs</div>;

  return (
    <div>
      <h1 className="title">Total Tool Pricing</h1>
      <table className="table pricing-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Cost</th>
          </tr>
        </thead>
        <tbody>
          {[
            ...Object.entries(toolCosts.value).map(([key, costs])=>{
              const title = (
                key in pricing.selectableInfo ? pricing.selectableInfo[key as ToolKey].title :
                key in pricing.mandatoryInfo ? pricing.mandatoryInfo[key].title :
                key
              );
              return <DisplayItemizedCosts key={key} title={title} costs={costs} />;
            }),
          ]}
        </tbody>
        <tfoot>
          {totalCosts.totalInitial === 0n ? null : <tr>
            <td><SpanWithToolTip tooltip={INITIAL_COST_TOOLTIP} >Total Initial Cost</SpanWithToolTip></td>
            <td className="money">
              <NanoUSDToDollarString nano={totalCosts.totalInitial} />
            </td>
          </tr>}
          {totalCosts.totalEscrow === 0n ? null : <tr>
            <td><SpanWithToolTip tooltip={OUTPUT_ESCROW_TOOLTIP} >Total Escrow</SpanWithToolTip></td>
            <td className="money">
              <NanoUSDToDollarString nano={totalCosts.totalEscrow} />
            </td>
          </tr>}
          <tr className="is-success">
            <td>Total</td>
            <td className="money">
              <NanoUSDToDollarString nano={totalCosts.total} />
            </td>
          </tr>
        </tfoot>
      </table>
    </div>
  );
}

const INITIAL_COST_TOOLTIP = "The initial cost charged immediately upon usage.";

const OUTPUT_ESCROW_TOOLTIP = [
  "This amount of money will be put into escrow until the tool is finished.",
  "Onced finished, this escrow amount will be refunded and the final cost will be charged."
].join("\n");
