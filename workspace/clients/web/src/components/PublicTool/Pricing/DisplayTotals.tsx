import React from "react";
import { NanoUSDToDollarString } from "../../displays/Money";
import { SpanWithToolTip } from "../../utility/span-with-tooltip";

export function DisplayTotals(
  { total, totalInitial, totalEscrow }: {
    total: bigint,
    totalInitial: bigint,
    totalEscrow: bigint,
  }
){
  if(total === 0n){
    return (
      <tr className="is-success">
        <td>Total</td>
        <td className="money">
          <NanoUSDToDollarString nano={total} />
        </td>
      </tr>
    );
  }
  return <>
    {totalInitial !== 0n && <tr>
      <td><SpanWithToolTip tooltip={INITIAL_COST_TOOLTIP} >Total Initial Cost</SpanWithToolTip></td>
      <td className="money">
        <NanoUSDToDollarString nano={totalInitial} />
      </td>
    </tr>}
    {totalEscrow !== 0n && <tr>
      <td><SpanWithToolTip tooltip={OUTPUT_ESCROW_TOOLTIP} >Total Escrow</SpanWithToolTip></td>
      <td className="money">
        <NanoUSDToDollarString nano={totalEscrow} />
      </td>
    </tr>}
    {totalInitial !== 0n && totalEscrow !== 0n && <tr className="is-success">
      <td>Total</td>
      <td className="money">
        <NanoUSDToDollarString nano={total} />
      </td>
    </tr>}
  </>;
}

const INITIAL_COST_TOOLTIP = "The initial cost charged immediately upon usage.";

const OUTPUT_ESCROW_TOOLTIP = [
  "For each item, money will be into escrow until the tool is finished.",
  "Onced an item is finished, the item's escrow amount will be refunded and a final cost will be charged."
].join("\n");
