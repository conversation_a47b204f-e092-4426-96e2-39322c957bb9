import React, { useMemo } from "react";
import { ProcessPricingStrategy } from "@divinci-ai/tools";
import { usePromisedMemo } from "../../../util/react/utility";

import { DisplayItemizedCosts } from "./PublicToolPricing";

import "./pricing-table.css";
import { ShallowObject } from "@divinci-ai/utils";
import { SelectedPublicToolWithConfig } from "@divinci-ai/models";
import { DisplayTotals } from "./DisplayTotals";

export function BulkProcessPricing<
  ProcessConfig extends ShallowObject, Input, <PERSON><PERSON><PERSON><PERSON> extends string
>(
  { pricing, selectedTools, processConfig, input, getInputName }: {
    pricing: ProcessPricingStrategy<ProcessConfig, Input, any, ToolKey>,
    selectedTools: Record<ToolKey, SelectedPublicToolWithConfig>,
    processConfig: ProcessConfig,
    input: Array<Input>,
    getInputName: (input: Input)=>string,
  }
){
  const toolCosts = usePromisedMemo(
    async ()=>(
      input.length === 0 ? [] : (
        await Promise.all(input.map(async (input)=>(
          {
            input,
            costParts: await pricing.getEstimatedCost(
              selectedTools, processConfig, input
            )
          }
        )))
      )
    ),
    [pricing, selectedTools, processConfig, input]
  );

  const totalCosts = useMemo(
    ()=>{
      if(typeof input === "undefined") return;
      if(toolCosts.status === "pending") return;
      if(toolCosts.status === "failed") return;
      let totalInitial = 0n;
      let totalEscrow = 0n;
      let total = 0n;
      for(const { costParts } of toolCosts.value){
        for(const costs of Object.values(costParts)){
          totalInitial += costs.inputCost;
          totalEscrow += costs.outputEscrow;
          total += costs.inputCost + costs.outputEscrow;
        }
      }
      return { total, totalInitial, totalEscrow };
    },
    [toolCosts, input]
  );

  if(input.length === 0) return null;
  if(toolCosts.status === "pending") return <div>Loading...</div>;
  if(toolCosts.status === "failed") return <div>Failed to load costs</div>;
  if(typeof totalCosts === "undefined") return <div>Failed to calculate total costs</div>;

  return (
    <table className="table pricing-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Cost</th>
        </tr>
      </thead>
      <tbody>
        {
          toolCosts.value.map(({ input, costParts })=>{
            const inputTitle = getInputName(input);
            return Object.entries(costParts).map(([key, costs])=>{
              const title = (
                key in pricing.selectableInfo ? pricing.selectableInfo[key as ToolKey].title :
                key in pricing.mandatoryInfo ? pricing.mandatoryInfo[key].title :
                key
              );
              return <DisplayItemizedCosts
                key={key}
                title={`${inputTitle} - ${title}`}
                costs={costs}
              />;
            });
          })
        }
      </tbody>
      <tfoot>
        <DisplayTotals
          total={totalCosts.total}
          totalInitial={totalCosts.totalInitial}
          totalEscrow={totalCosts.totalEscrow}
        />
      </tfoot>
    </table>
  );
}
