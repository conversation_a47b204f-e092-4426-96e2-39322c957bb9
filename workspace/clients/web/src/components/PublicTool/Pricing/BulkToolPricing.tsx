import React, { useMemo } from "react";
import { ToolConfig } from "@divinci-ai/tools";
import { usePromisedMemo } from "../../../util/react/utility";

import { DisplayItemizedCosts } from "./PublicToolPricing";

import "./pricing-table.css";
import { SelectedPublicToolWithConfig } from "@divinci-ai/models";
import { DisplayTotals } from "./DisplayTotals";

export function BulkToolPricing<Input>(
  { availableTools, selectedTool, input, getInputName }: {
    availableTools: Record<string, ToolConfig<any, any, Input, any>>,
    selectedTool: SelectedPublicToolWithConfig,
    input: Array<Input>,
    getInputName: (input: Input)=>string,
  }
){
  const activeTool = availableTools[selectedTool.id];

  const toolCosts = usePromisedMemo(
    async ()=>{
      if(!activeTool) return [];
      if(input.length === 0) return [];
      return await Promise.all(input.map(async (input)=>{
        try {
          return {
            input,
            costs: await activeTool.pricing.getEstimatedCost(
              selectedTool.config, input
            )
          };
        }catch(e){
          console.error("Tool Pricing Error:", e);
          throw e;
        }
      }));
    },
    [selectedTool, activeTool, input]
  );

  const totalCosts = useMemo(
    ()=>{
      if(typeof input === "undefined") return;
      if(toolCosts.status === "pending") return;
      if(toolCosts.status === "failed") return;
      let totalInitial = 0n;
      let totalEscrow = 0n;
      let total = 0n;
      for(const { costs } of toolCosts.value){
        totalInitial += costs.inputCost;
        totalEscrow += costs.outputEscrow;
        total += costs.inputCost + costs.outputEscrow;
      }
      return { total, totalInitial, totalEscrow };
    },
    [toolCosts, input]
  );

  if(input.length === 0) return null;
  if(toolCosts.status === "pending") return <div>Loading...</div>;
  if(toolCosts.status === "failed"){
    return (
      <>
        <div>Failed to load costs</div>
        <pre>{toolCosts.error.message}</pre>
      </>
    );
  }
  if(typeof totalCosts === "undefined") return <div>Failed to calculate total costs</div>;

  return (
    <div>
      <h1 className="title">Total Tool Pricing</h1>
      <table className="table pricing-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Cost</th>
          </tr>
        </thead>
        <tbody>
          {
            toolCosts.value.map(({ input, costs })=>{
              const inputTitle = getInputName(input);
              return <DisplayItemizedCosts
                key={inputTitle}
                title={`${inputTitle}`}
                costs={costs}
              />;
            })
          }
        </tbody>
        <tfoot>
          <DisplayTotals
            total={totalCosts.total}
            totalInitial={totalCosts.totalInitial}
            totalEscrow={totalCosts.totalEscrow}
          />
        </tfoot>
      </table>
    </div>
  );
}
