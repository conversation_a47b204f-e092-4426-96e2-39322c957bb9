import React from "react";

import { ToolConfig, CostEstimationStatus, ToolInitialCosts } from "@divinci-ai/tools";
import { ShallowObject } from "@divinci-ai/utils";

import { usePromisedMemo } from "../../../util/react/utility";

import "./pricing-table.css";

export function PublicToolPricing<Config extends ShallowObject, Input>(
  { tool, config, input }: {
    tool: ToolConfig<any, Config, Input, any, any>,
    config: Config,
    input: Input,
  }
){
  const result = usePromisedMemo(
    async ()=>(
      typeof input === "undefined" ? void 0 :
      tool.pricing.getEstimatedCost(config, input)
    ),
    [tool, config, input]
  );

  switch(result.status){
    case "pending": return (
      <div className="notification is-warning">Loading Pricing...</div>
    );
    case "failed": {
      console.error("Failed to load pricing", result.error);
      return (
        <div className="notification is-danger">
          <div>Failed to load pricing</div>
          <pre>{result.error.message}</pre>
        </div>
      );
    }
    case "succeeded": {
      // If input is undefined, then shouldn't display anything
      if(result.value === void 0) return null;
      return (<PublicToolPricingDisplayCosts costs={result.value} />);
    }
  }
}

import { NanoUSDToDollarString } from "../../displays/Money";
import { SpanWithToolTip } from "../../utility/span-with-tooltip";

const INPUT_COST_TOOLTIP = "The initial cost charged immediately upon usage.";

const OUTPUT_ESCROW_TOOLTIP = [
  "This amount of money will be put into escrow until the tool is finished.",
  "Onced finished, this escrow amount will be refunded and the final cost will be charged."
].join("\n");

const COST_STATUS_TOOLTIP = {
  exact: "The final cost will be exactly this amount.",
  unknown: "The final cost is unknown, may be greater or less than this initial amount.",
  maximum: "The final cost will be less than or equal to this amount.",
  minimum: "The final cost will be greater than or equal to this amount.",
};

export function PublicToolPricingDisplayCosts({ costs }: { costs: ToolInitialCosts }){
  return (
    <table className="table pricing-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Amount</th>
        </tr>
      </thead>
      <tbody>
        <DisplayItemizedCosts costs={costs} />
        <tr className="is-success">
          <td>
            <SpanWithToolTip tooltip={COST_STATUS_TOOLTIP[costs.costStatus]} >
            Total ({capitalize(costs.costStatus)})
            </SpanWithToolTip>
          </td>
          <td className="money">
            <NanoUSDToDollarString
              nano={costs.inputCost + costs.outputEscrow}
            />
          </td>
        </tr>
      </tbody>
    </table>
  );

}


function capitalize(str: string){
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function DisplayItemizedCosts(
  { title, costs }: { title?: string, costs: ToolInitialCosts }
){

  const titleWithWhitespace = title ? `${title}: ` : "";

  return (
    <>
      {costs.inputCost === 0n ? null : <tr>
        <td>
          <SpanWithToolTip tooltip={INPUT_COST_TOOLTIP} >{titleWithWhitespace}Cost</SpanWithToolTip>
        </td>
        <td className="money">
          <NanoUSDToDollarString nano={costs.inputCost} />
        </td>
      </tr>}
      {costs.outputEscrow === 0n ? null : <tr>
        <td>
          <SpanWithToolTip tooltip={OUTPUT_ESCROW_TOOLTIP} >{titleWithWhitespace}Escrow</SpanWithToolTip>
        </td>
        <td className="money">
          <NanoUSDToDollarString nano={costs.outputEscrow} />
        </td>
      </tr>}
    </>
  );
}

// the pricingCharacter might be useful in the future
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function pricingCharater(costStatus: CostEstimationStatus){
  switch(costStatus){
    case "unknown": return "≈";
    case "exact": return "=";
    case "maximum": return "≤";
    case "minimum": return "≥";
  }
}
