import React from "react";
import { PublicToolInfo as PublicToolInfoType } from "@divinci-ai/models";
import ReactMarkdown from "react-markdown";
import styles from "./PublicToolInfo.module.css";

export function PublicToolInfoDisplay({ tool }: { tool: PublicToolInfoType }){
  return (
    <div className={styles.divinciScraperPadding}>
      <article className={[styles.articleContainer, "message", tool.deprecated ? "is-warning" : "is-success"].join(" ")}>
        <div className="message-header">
          <p className={styles.statusText}>
            {tool.deprecated ? "Deprecated" : "Available"}
          </p>
        </div>
        <div className="message-body">
          <h3 className="title">
            <a href={tool.url} target="_blank">{tool.title}</a>
          </h3>
          <h6 className="subtitle">
            <a href={tool.orgUrl} target="_blank">Created by {tool.org}</a>
            </h6>
          {tool.provider && (
            <h6 className="subtitle">
              <a href={tool.provider.url} target="_blank">Powered by {tool.provider.name}</a>
            </h6>
          )}
          <ReactMarkdown>{tool.description}</ReactMarkdown>
        </div>
      </article>
    </div>
  );
}
