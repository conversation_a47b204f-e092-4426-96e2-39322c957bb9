import { WhiteLabelReleaseDoc } from "@divinci-ai/models";
import React, {
  useState, createContext, useContext, PropsWithChildren,
} from "react";

type SocialMessageType = {
  availableReleases: Array<{ title: string, release: undefined | WhiteLabelReleaseDoc }>,
  release: undefined | { title: string, release: undefined | WhiteLabelReleaseDoc },
  setRelease: (release: string | undefined) => any,
};

const SocialMessageContext = createContext<SocialMessageType>({
  availableReleases: [],
  release: { title: "No Release", release: void 0 },
  setRelease: ()=>{},
});

export function useSocialMessage(){
  return useContext(SocialMessageContext);
}

export function SocialMessageProvider(
  { children, defaultRelease, availableReleases }: PropsWithChildren<{
    defaultRelease?: string,
    availableReleases: SocialMessageType["availableReleases"],
  }>
){
  const [release, setRelease] = useState<{ title: string, release: undefined | WhiteLabelReleaseDoc } | undefined>(()=>{
    const release = findRelease(availableReleases, defaultRelease);
    if(release) return release;
    return availableReleases[0];
  });

  return (
    <SocialMessageContext.Provider
      value={{
        availableReleases,
        release,
        setRelease: (releaseId: string | undefined)=>{
          const release = findRelease(availableReleases, releaseId);
          if(!release) throw new Error("Release not found");
          setRelease(release);
        },
      }}
    >
      { children }
    </SocialMessageContext.Provider>
  );
}

function findRelease(
  available: Array<{ title: string, release: undefined | WhiteLabelReleaseDoc }>,
  releaseId: string | undefined,
){
return available.find((item)=>(
  !item.release ? !releaseId ? true : false :
  item.release._id === releaseId
));
}
