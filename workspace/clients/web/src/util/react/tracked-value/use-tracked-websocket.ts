import { useEffect, useState, useRef } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { RecoveringWebsocket } from "../../../util/websocket";
import { useOptionalAuth0WebSocket } from "../../../globals/auth0-user";

export enum TrackedWebsocketValueState {
  Stopped = "STOPPED",
  Connecting = "Connecting",
  Failure = "FAILURE",
  Ready = "READY",
}

export type TrackedWebsocketResult = (
  | { state: TrackedWebsocketValueState.Stopped, loading: false, error: undefined }
  | { state: TrackedWebsocketValueState.Connecting, loading: true, error: undefined }
  | { state: TrackedWebsocketValueState.Failure, loading: false, error: Error }
  | { state: TrackedWebsocketValueState.Ready, loading: false, error: undefined }
);


export function useTrackedWebsocket<Value>({
  requiresAuth, url
}: { requiresAuth: boolean, url?: string }){
  const { isAuthenticated } = useAuth0();
  const lastValue = useRef(-1);
  const createWS = useOptionalAuth0WebSocket();
  const activeWS = useRef<null | RecoveringWebsocket>(null);

  const [state, setState] = useState<TrackedWebsocketResult>({
    state: TrackedWebsocketValueState.Stopped,
    loading: false,
    error: void 0,
  });
  const [value, setValue] = useState<Value | null>(null);

  useEffect(()=>{
    if(!isAuthenticated) {
      return;
    }
    const currentValue = Date.now();
    lastValue.current = currentValue;
    if(!url){
      setState({
        state: TrackedWebsocketValueState.Stopped,
        loading: false,
        error: void 0,
      });
      setValue(null);
      return;
    }

    Promise.resolve().then(async ()=>{
      try {
        if(requiresAuth && !isAuthenticated){
          throw new Error("Not authenticated");
        }
        const websocket = await createWS(url);
        if(lastValue.current !== currentValue){
          return websocket.close();
        }
        setState({
          state: TrackedWebsocketValueState.Ready,
          loading: false,
          error: void 0,
        });

        activeWS.current = websocket;
        websocket.onMessage.on((event)=>{
          const updatedValue = JSON.parse(event.data);
          setValue(updatedValue as Value);
        });
        websocket.onReadyStateChange.on((state)=>{
          switch(state){
            case "delay": return setState({
              state: TrackedWebsocketValueState.Connecting,
              loading: true,
              error: void 0,
            });
            case "attempt": return setState({
              state: TrackedWebsocketValueState.Connecting,
              loading: true,
              error: void 0,
            });
            case "open": return setState({
              state: TrackedWebsocketValueState.Ready,
              loading: false,
              error: void 0,
            });
          }
        });
      }catch(e: unknown){
        console.error("websocket error:", e);
        const error = (e instanceof Error) ? e : new Error("Unknown Error");
        setState({
          state: TrackedWebsocketValueState.Failure,
          loading: false,
          error,
        });
      }
    });

    return ()=>{
      if(activeWS.current !== null) activeWS.current.close();
      activeWS.current = null;
    };

  }, [createWS, requiresAuth, isAuthenticated, url]);

  return { ...state, value };
}

