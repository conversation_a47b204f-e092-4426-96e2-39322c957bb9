import React, { useState, useEffect } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { useLoginWithRedirectState } from "../../../../../globals/auth0-user";
import { useDeviceSize } from "../../../../../globals/device-size";

import { NormalForm } from "../../../../../components/Transcript/views/Chat/MessageForm/NormalForm";

import styles from "../../../../../components/Transcript/views/Chat/Transcript/messageList.module.css";

import TextToSpeechSVG from "../../../../../components/Transcript/views/Chat/MessageForm/text-to-speech.svg";
import { useTranscript } from "../../../../../components/Transcript/data/TranscriptContext";

import { useReplyTo } from "../../../../../components/Transcript/data/ReplyToContext";
import { useSocialMessage } from "../../../../../components/Transcript/data/SocialMessageContext";
import { JSON_EXTRA_Unknown } from "@divinci-ai/utils";

import { extractErrorMessages } from "../../../../../components/Transcript/views/Chat/MessageForm/AddMessageError/extractErrorMessages";
import { RenderErrorMessages } from "../../../../../components/Transcript/views/Chat/MessageForm/AddMessageError/RenderErrorMessages";

import {
  useSettingsView, SettingsViewProvider,
} from "../../../../../components/Transcript/views/Chat/MessageForm/Settings";

import {
  WhiteLabelSettingsInfo,
  WhiteLabelSettingsForm,
  WhiteLabelSettingsButtons,
} from "./Settings";

export function WhiteLabelMessageForm({
  addMessage,
  addSocialMessage,
}: {
  addMessage: (
    id: string,
    message: {
      content: string,
      assistantName?: string,
      replyTo?: string,
      release?: string,
    },
  ) => Promise<JSON_EXTRA_Unknown>,
  addSocialMessage?: (
    message: {
      content: string,
      release?: string,
    },
  ) => Promise<JSON_EXTRA_Unknown>,
}){
  return (
    <SettingsViewProvider>
      <WhiteLabelMessageFormInner
        addMessage={addMessage}
        addSocialMessage={addSocialMessage}
      />
    </SettingsViewProvider>
  );
}

function WhiteLabelMessageFormInner({
  addMessage,
  addSocialMessage,
}: {
  addMessage: (
    id: string,
    message: {
      content: string,
      assistantName?: string,
      replyTo?: string,
      release?: string,
    },
  ) => Promise<JSON_EXTRA_Unknown>,
  addSocialMessage?: (
    message: {
      content: string,
      release?: string,
    },
  ) => Promise<JSON_EXTRA_Unknown>,
}){
  const { transcript } = useTranscript();
  const { device } = useDeviceSize();
  const { isAuthenticated } = useAuth0();
  const loginWithRedirect = useLoginWithRedirectState();
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Function to toggle the drawer
  const toggleDrawer = (newValue: boolean)=>{
    setDrawerOpen(newValue);
  };

  const [waiting, setWaiting] = useState(
    transcript === null ? false : transcript.awaitingResponse,
  );
  const [error, setError] = useState<{ type: string, errors: Array<string> }>({ type: "none", errors: [] });
  const [content, setContent] = useState("");
  const [formType, setFormType] = useState("text");
  const { message: replyToMessage } = useReplyTo();
  const { release: socialRelease } = useSocialMessage();
  const { settingsView } = useSettingsView();

  useEffect(()=>{
    if(!transcript) return;
    setWaiting(transcript.awaitingResponse);
  }, [transcript]);

  if(!isAuthenticated) {
    return (
      <h1>
        <button
          className="button"
          onClick={(e)=>{
            e.preventDefault();
            loginWithRedirect();
          }}
        >
          🔐 Please Login to add to this chat.
        </button>
      </h1>
    );
  }

  if(!transcript || waiting) {
    return <h1>⏳ Please wait...</h1>;
  }

  async function submitForm(){
    try {
      if(!transcript){
        throw new Error("No Transcript Available");
      }

      setError({ type: "none", errors: [] });
      setWaiting(true);

      switch(settingsView){
        case "assistant": {
          if(!socialRelease){
            throw new Error("No Release Chosen");
          }
          await addMessage(transcript._id, {
            content,
            replyTo:
              typeof replyToMessage === "object" ? replyToMessage?._id : void 0,
            assistantName: socialRelease.release?._id, // Use release ID as assistant name for whitelabel
            release: socialRelease.release?._id,
          });
          break;
        }
        case "social": {
          if(!socialRelease){
            throw new Error("No Release Chosen");
          }
          if(!addSocialMessage){
            throw new Error("No addUserToUserMessage Function Available");
          }
          await addSocialMessage({
            content,
            release: socialRelease.release?._id,
          });
          break;
        }
        default: {
          throw new Error("No Settings View Chosen");
        }
      }

      setContent("");
    }catch(e){
      setError(extractErrorMessages(e));
    } finally {
      setWaiting(false);
    }
  }

  return (
    <div className={`${styles.messageFormContainer}`}>
      <WhiteLabelSettingsForm
        content={content}
        drawerOpen={drawerOpen}
        toggleDrawer={toggleDrawer}
      />
      <div className={`${styles.messageFormParent}`} style={{ minHeight: "auto" }}>

      {device === "mobile" ?
        <span className={`${styles.aiNoticeWrapper}`}>
          <span className={`${styles.aiNotice}`}>
            <i className={`fas fa-shield-alt ${styles.privacyNoticed}`}></i>  By using Divinci AI, you agree to our <a
              href={`//divinci.app/terms-of-service.html`}
              target="_blank"
              rel="noopener noreferrer"
            >Terms</a> and have read our <a
              href={`//divinci.app/privacy-policy.html`}
              target="_blank"
              rel="noopener noreferrer"
            >Privacy Policy.</a>
          </span>
        </span>
        : ""}
      <div className="chat-input-form">
        <div className="form-container" style={{ position: "relative" }}>
          <RenderErrorMessages {...error} />
          <WhiteLabelSettingsInfo toggleDrawer={toggleDrawer} drawerOpen={drawerOpen} />
          {formType === "text" ? (
          <span className={`${styles.chatInputTextarea}`}>
            <NormalForm
              style={{ flexGrow: 1 }}
              placeholderValue="Type your message here..."
              value={content}
              onChange={setContent}
              onKeyDown={(e)=>{
                if(e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  submitForm();
                }
              }}
            />
          </span>
          ) : formType === "speech" ? (
            <h1>We don't currently offer speech to text</h1>
          ) : null}
          {device !== "mobile" ?
            <span className={`${styles.aiNoticeWrapper}`}>
              <span className={`${styles.aiNotice}`}>
                <i className={`fas fa-shield-alt ${styles.privacyNoticed}`}></i>  By using Divinci AI, you agree to our <a
                  href={`//divinci.app/terms-of-service.html`}
                  target="_blank"
                  rel="noopener noreferrer"
                >Terms</a> and have read our <a
                  href={`//divinci.app/privacy-policy.html`}
                  target="_blank"
                  rel="noopener noreferrer"
                >Privacy Policy.</a>
              </span>
            </span>
            : "" }
        </div>
        <div className="chat-submit-buttons">
          <button className="button is-outlined speech-button">
            <TextToSpeechSVG
              className="text-to-speech-svg"
              onClick={()=>(
                setFormType(formType === "text" ? "speech" : "text")
              )}
            />
          </button>
          <WhiteLabelSettingsButtons />
          <button
            className="button is-outlined send-chat-button"
            type="button"
            disabled={!content.trim()}
            onClick={(e)=>{
              e.preventDefault();
              submitForm();
            }}
          >
            Send
          </button>
        </div>
      </div>
      </div>
    </div>
  );
}
