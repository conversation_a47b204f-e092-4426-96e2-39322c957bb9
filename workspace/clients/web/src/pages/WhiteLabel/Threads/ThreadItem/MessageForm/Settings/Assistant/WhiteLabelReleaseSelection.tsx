import React from "react";
import { useSocialMessage } from "../../../../../../../components/Transcript/data/SocialMessageContext";
import ReactMarkdown from "react-markdown";
import { WhiteLabelReleaseDoc } from "@divinci-ai/models";

export function WhiteLabelReleaseSelection(){
  const { availableReleases, release } = useSocialMessage();

  if(availableReleases.length === 0){
    return (
      <div className="box">
        <h4 className="subtitle">Available Assistants</h4>
        <div>No assistants available for this whitelabel thread</div>
      </div>
    );
  }

  if(availableReleases.length === 1){
    const singleRelease = availableReleases[0];
    return (
      <div className="box">
        <h4 className="subtitle">Assistant</h4>
        <div>Using: {singleRelease.title}</div>
        {singleRelease.release && (
          <ReactMarkdown>{singleRelease.release.description}</ReactMarkdown>
        )}
      </div>
    );
  }

  return (
    <div className="box">
      <h4 className="subtitle">Choose Assistant</h4>
      <div style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "stretch",
        gap: "10px",
        maxHeight: "300px",
        overflowY: "auto"
      }}>
        {availableReleases.map((releaseItem)=>(
          !releaseItem.release ? (
            <VoidItem key="void" active={release?.release} item={releaseItem} />
          ) : (
            <ReleaseItem key={releaseItem.release._id} active={release?.release} item={releaseItem.release} />
          )
        ))}
      </div>
    </div>
  );
}

function VoidItem(
  { active, item }: { active: WhiteLabelReleaseDoc | undefined, item: { title: string } }
){
  const { setRelease } = useSocialMessage();
  const activeId = active?._id || "void";
  const itemId = "void";

  return (
    <div>
      <div>
        <input
          type="radio"
          name="whitelabel-assistant-release"
          value={itemId}
          checked={itemId === activeId}
          onChange={()=>{
            setRelease(void 0);
          }}
        />
        <span>{item.title}</span>
      </div>
      <div style={{ fontSize: "0.9em", color: "#666", marginLeft: "20px" }}>
        Send messages without using any specific assistant
      </div>
    </div>
  );
}

function ReleaseItem({ active, item }: { active: WhiteLabelReleaseDoc | undefined, item: WhiteLabelReleaseDoc }){
  const { setRelease } = useSocialMessage();
  const activeId = active?._id || "void";
  const itemId = item._id;

  return (
    <div style={{ marginBottom: "10px" }}>
      <div>
        <input
          type="radio"
          name="whitelabel-assistant-release"
          value={itemId}
          checked={itemId === activeId}
          onChange={()=>{
            setRelease(itemId);
          }}
        />
        <span style={{ marginLeft: "8px", fontWeight: "bold" }}>{item.title}</span>
      </div>
      <div style={{ fontSize: "0.9em", color: "#666", marginLeft: "20px" }}>
        <ReactMarkdown>{item.description}</ReactMarkdown>
      </div>
    </div>
  );
}
