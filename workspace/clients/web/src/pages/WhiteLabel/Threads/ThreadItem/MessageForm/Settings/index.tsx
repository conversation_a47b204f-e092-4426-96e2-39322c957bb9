import React from "react";
import { useSettingsView } from "../../../../../../components/Transcript/views/Chat/MessageForm/Settings";

import {
  <PERSON><PERSON> as AssistantButton,
  Info as AssistantInfo,
  Settings as AssistantSettings
} from "./Assistant";
import {
  Button as SocialButton,
  Info as SocialInfo,
  Settings as SocialSettings
} from "../../../../../../components/Transcript/views/Chat/MessageForm/Settings/Social";

export function WhiteLabelSettingsButtons(){
  return (
    <>
      <AssistantButton />
      <SocialButton />
    </>
  );
}

export function WhiteLabelSettingsInfo({ toggleDrawer, drawerOpen }: {
  toggleDrawer: (newState: boolean)=>any,
  drawerOpen: boolean,
}){
  const { settingsView } = useSettingsView();
  switch(settingsView){
    case "assistant": return <AssistantInfo toggleDrawer={toggleDrawer} drawerOpen={drawerOpen} />;
    case "social": return <SocialInfo toggleDrawer={toggleDrawer} drawerOpen={drawerOpen} />;
    default: return null;
  }
}

import classNames from "classnames";
import styles from "../../../../../../components/Transcript/views/Chat/Transcript/messageList.module.css";

export function WhiteLabelSettingsForm({
  content,
  drawerOpen,
  toggleDrawer
}: {
  content: string,
  drawerOpen: boolean,
  toggleDrawer: (newState: boolean)=>any,
}){
  const { settingsView } = useSettingsView();
  // Class to toggle the drawer's visibility using new flexbox positioning
  const drawerClasses = classNames("chat-inline-settings", "drawer", "panel", styles.settingsDrawer, {
    "is-active": drawerOpen,
  });

  return (
    <div className={drawerClasses}>
      {/* Drawer Content */}
      {settingsView === "assistant" ? (
        <AssistantSettings content={content} />
      ) : settingsView === "social" ? (
        <SocialSettings content={content} />
      ) : null}
    </div>
  );
}
