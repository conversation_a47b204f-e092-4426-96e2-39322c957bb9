import React from "react";

import { WhiteLabelMessageForm } from "./WhiteLabelMessageForm";
import { useAuth0Fetch } from "../../../../../globals/auth0-user";

import { useParams } from "react-router";

import { addReleaseChatAIMessage, addReleaseChatSocialMessage } from "@divinci-ai/actions";
import { JSON_EXTRA_Unknown } from "@divinci-ai/utils";

export function ThreadMessageForm(){
  const auth0Fetch = useAuth0Fetch();
  const params = useParams();

  return (
    <WhiteLabelMessageForm
      addMessage={async (id: string, { content, replyTo, release }: {
        content: string,
        assistantName?: string,
        replyTo?: string,
        release?: string,
      }): Promise<JSON_EXTRA_Unknown>=>{
        // Transcript should always exist here
        const { whitelabelId, chatId } = params;
        if(!whitelabelId || !chatId) throw new Error("Missing whitelabelId or chatId");
        if(!release) throw new Error("Missing release");
        return addReleaseChatAIMessage(
          auth0Fetch,
          { whitelabelId, chatId },
          { content, replyTo, release }
        );
      }}
      addSocialMessage={async ({ content, release }: {
        content: string,
        release?: string,
      }): Promise<JSON_EXTRA_Unknown>=>{
        const { whitelabelId, chatId } = params;
        if(!whitelabelId || !chatId) throw new Error("Missing whitelabelId or chatId");
        if(!release) throw new Error("Missing release");
        return addReleaseChatSocialMessage(
          auth0Fetch,
          { whitelabelId, chatId },
          { content, release }
        );
      }}
    />
  );
}
