import React from "react";

import classNames from "classnames";
import { useSettingsView } from "../../../../../../../components/Transcript/views/Chat/MessageForm/Settings";
import { SpanWithToolTip } from "../../../../../../../components/utility/span-with-tooltip";

import ButtonTT from "./ButtonTT.md";
export function Button(){
  const { settingsView, setSettingsView } = useSettingsView();
  return (
    <button
      className="button is-small"
      onClick={(e)=>{
        e.preventDefault();
        setSettingsView("assistant");
      }}
      disabled={settingsView === "assistant"}
    >
      <SpanWithToolTip tooltip={ButtonTT} clickable={false}>AI Message</SpanWithToolTip>
    </button>
  );
}

import { useSocialMessage } from "../../../../../../../components/Transcript/data/SocialMessageContext";
export function Info({ toggleDrawer, drawerOpen }: {
  toggleDrawer: (newState: boolean)=>any,
  drawerOpen: boolean,
}){
  const { release } = useSocialMessage();

  return (
    <div className="chat-input-menu">
      {release && (
        <button
          className="button"
          onClick={()=>(toggleDrawer(!drawerOpen))}
        >{release.title}</button>
      )}
      {/* Drawer Toggle Button */}
      <button
        className={classNames("button chat-options-toggle-button", {
          "is-drawer-open": drawerOpen, // Add this class if the drawer is open
        })}
        onClick={()=>(toggleDrawer(!drawerOpen))}
      >
        <span className="chatOptionsTogglesSymbol">
          {drawerOpen ? "ⓧ": "⚙"}
        </span>
      </button>
    </div>
  );
}

import styles from "../../../../../../../components/Transcript/views/Chat/Transcript/messageList.module.css";
import { ReplyTo } from "../../../../../../../components/Transcript/views/Chat/MessageForm/ReplyTo";
import { TextToSpeech } from "../../../../../../../components/Transcript/views/Chat/MessageForm/TextToSpeech";
import { useTranscript } from "../../../../../../../components/Transcript/data/TranscriptContext";
import { WhiteLabelReleaseSelection } from "./WhiteLabelReleaseSelection";

export function Settings(
  { content }: { content: string }
){
  const { transcript } = useTranscript();
  {/* Drawer Content */}
  if(!transcript) return null;
  return (
    <div className={`panel-block ${styles.optionsPanel}`}>
      <div className={styles.whiteLabelSettingsContainer}>
        <div className="box miscOptions">
          <h4 className="subtitle">
            Misc Options
          </h4>
          <div className={`${styles.speechOptionsParent}`}>
            <TextToSpeech transcript={transcript} />
          </div>
          <div className="replyToOptionsParent">
            <ReplyTo />
          </div>
        </div>
        <WhiteLabelReleaseSelection />
      </div>
    </div>
  );
}
