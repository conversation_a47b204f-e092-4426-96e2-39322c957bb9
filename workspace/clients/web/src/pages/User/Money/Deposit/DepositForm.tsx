import React, { useState, useEffect, useCallback } from "react";
import { depositUserFunds, getUsersPaymentMethods } from "@divinci-ai/actions";
import { useAuth0Fetch } from "../../../../globals/auth0-user";
import { PaymentMethods } from "./PaymentMethods";
import { useUsersMoney } from "../../../../globals/user-preferences/money";
import { NanoUSDToDollarString } from "../../../../components/displays/Money";
import { TrackedValueState, useTrackedGetter } from "../../../../util/react/tracked-value";

import styles from "./money.module.css";
import { useRunGetter, GetterState } from "../../../../util/fetch";

export function DepositForm(){
  const usersMoney = useUsersMoney();
  const auth0Fetch = useAuth0Fetch();
  const [body, setBody] = useState<{ amount: number, paymentMethodId: null | string }>({
    amount: 10,
    paymentMethodId: null,
  });
  const { run, state, error } = useRunGetter(useCallback(async ()=>{
    if(body.amount <= 0) throw new Error("Amount cannot be 0 or less");
    if(!body.paymentMethodId) throw new Error("Please Select a Payment Method");

    return await depositUserFunds(auth0Fetch, {
      amount: Math.round(body.amount * 100), // Convert to cents
      paymentMethodId: body.paymentMethodId,
    });
  }, [auth0Fetch, body]));

  const { value: paymentMethods, update: updatePaymentMethods } = useTrackedGetter(
    {
      getter: useCallback(()=>(getUsersPaymentMethods(auth0Fetch)), [auth0Fetch]),
    }
  );

  useEffect(()=>{
    if(body.paymentMethodId) return;
    if(!paymentMethods) return;
    if(paymentMethods.length === 0) return;
    setBody((body)=>({
      ...body,
      paymentMethodId: paymentMethods[0].id,
    }));
  }, [paymentMethods]);

  return (
    <div>
      <h2 className={styles.sectionTitle}>Deposit Funds</h2>

      {usersMoney.state === TrackedValueState.SUCCESS && (
        <div className={styles.balanceDisplay}>
          <strong>Current Balance: </strong>
          <span><NanoUSDToDollarString nano={usersMoney.value.balance} /></span>
        </div>
      )}

      {state === GetterState.Value && (
        <div className={styles.successMessage}>
          Deposit successful! Your balance has been updated.
        </div>
      )}

      <form
        onSubmit={async (e: React.FormEvent)=>{
          try {
            e.preventDefault();
            await run();
            usersMoney.update();
          }catch(e: any){
            console.error("Error:", e);
          }
        }}
        className={styles.depositForm}
      >
        <div className={styles.formGroup}>
          <label htmlFor="amount">Amount (USD):</label>
          <input
            type="number"
            id="amount"
            name="amount"
            max="10000"
            min="1"
            step="0.01"
            value={body.amount}
            onChange={(e: React.ChangeEvent<HTMLInputElement>)=>{
              const value = parseFloat(e.target.value);
              if(!isNaN(value) && value > 0 && value <= 10000) {
                setBody({ ...body, amount: value });
              }
            }}
            className={styles.amountInput}
            disabled={state === GetterState.Loading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="paymentMethod">Payment Method:</label>
          <select
            id="paymentMethod"
            name="paymentMethod"
            value={body.paymentMethodId || ""}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>)=>{
              setBody({ ...body, paymentMethodId: e.target.value });
            }}
            className={styles.paymentMethodSelect}
            disabled={state === GetterState.Loading || !paymentMethods || paymentMethods.length === 0}
          >
            <option value="">Select a payment method</option>
            {paymentMethods && paymentMethods.map(pm=>(
              <option key={pm.id} value={pm.id}>
                {pm.card.brand} •••• {pm.card.last4} (Expires: {pm.card.exp_month}/{pm.card.exp_year})
              </option>
            ))}
          </select>
        </div>

        {error && <pre className={styles.errorMessage}>{JSON.stringify(error)}</pre>}

        {(!paymentMethods || paymentMethods.length === 0) && (
          <div className={styles.warningMessage}>
            You need to add a payment method before you can make a deposit.
          </div>
        )}

        <button
          type="submit"
          className="button is-primary"
          disabled={
            state === GetterState.Loading || isBodyValid(body) !== true
          }
        >
          {state === GetterState.Loading ? "Processing..." : "Deposit"}
        </button>
      </form>

      <div className={styles.paymentMethodsSection}>
        <h2 className={styles.sectionTitle}>Payment Methods</h2>
        <PaymentMethods onUpdate={updatePaymentMethods} />
      </div>
    </div>
  );
}

function isBodyValid(body: { amount: number, paymentMethodId: null | string }){
  if(body.amount <= 0) return "Amount cannot be 0 or less";
  if(!body.paymentMethodId) return "Please Select a Payment Method";
  return true;
}
