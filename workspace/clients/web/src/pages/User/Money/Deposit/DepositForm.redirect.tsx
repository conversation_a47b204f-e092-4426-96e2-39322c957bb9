import React, { useState } from "react";
import { useAuth0FetchJSON } from "../../../../globals/auth0-user";
import { PaymentMethods } from "../PaymentMethods";
import { useUsersMoney } from "../../../../globals/user-preferences/money";
import { DollarBillionthsToPadded } from "../../../../components/displays/Money";

import styles from "./money.module.css";

export function DepositForm(){
  const [amount, setAmount] = useState<number>(10);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const auth0FetchJSON = useAuth0FetchJSON();
  const usersMoney = useUsersMoney();

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>)=>{
    const value = parseFloat(e.target.value);
    if(!isNaN(value) && value > 0) {
      setAmount(value);
    }
  };

  const handleSubmit = async (e: React.FormEvent)=>{
    e.preventDefault();
    setIsProcessing(true);
    setError(null);

    try {
      const { stripeUrl } = await auth0FetchJSON("/user/money/deposit", {
        method: "POST",
        headers: { "content-type": "application/json" },
        body: JSON.stringify({ amount: Math.round(amount * 100) }) // Convert to cents
      }) as { stripeUrl: string };

      // Redirect to Stripe checkout
      window.location.href = stripeUrl;
    }catch(err: any) {
      setError(err.message || "An error occurred while processing your deposit");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div>
      <h2 className={styles.sectionTitle}>Deposit Funds</h2>

      {usersMoney && (
        <div className={styles.balanceDisplay}>
          <strong>Current Balance: </strong>
          <span><DollarBillionthsToPadded billionths={usersMoney.balance.usd} /></span>
        </div>
      )}

      <form onSubmit={handleSubmit} className={styles.depositForm}>
        <div className={styles.formGroup}>
          <label htmlFor="amount">Amount (USD):</label>
          <input
            type="number"
            id="amount"
            name="amount"
            min="1"
            step="0.01"
            value={amount}
            onChange={handleAmountChange}
            className={styles.amountInput}
            disabled={isProcessing}
          />
        </div>

        {error && <div className={styles.errorMessage}>{error}</div>}

        <button
          type="submit"
          className="button is-primary"
          disabled={isProcessing || amount <= 0}
        >
          {isProcessing ? "Processing..." : "Deposit"}
        </button>
      </form>

      <div className={styles.paymentMethodsSection}>
        <h2 className={styles.sectionTitle}>Payment Methods</h2>
        <PaymentMethods />
      </div>
    </div>
  );
}
