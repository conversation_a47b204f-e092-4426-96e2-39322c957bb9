.sectionTitle {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: bold;
}

.balanceDisplay {
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.depositForm {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.amountInput {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.errorMessage {
  color: #ff3860;
  margin-bottom: 1rem;
}

.successMessage {
  color: #23d160;
  background-color: #f0fff4;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-weight: bold;
}

.warningMessage {
  color: #ff9800;
  background-color: #fff9e6;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.paymentMethodSelect {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.paymentMethodsSection {
  margin-top: 2rem;
}

.paymentMethodsContainer {
  margin-top: 1rem;
}

.actionButtons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.noPaymentMethods {
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 5px;
  text-align: center;
}

.paymentMethodsList {
  margin-top: 1.5rem;
}

.paymentMethodCard {
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 5px;
  margin: 0 0.5rem;
}

.loading {
  padding: 1rem;
  text-align: center;
  color: #777;
}

.btnPadding {
  margin-bottom: 1rem;
}

.wordPadding {
  margin-bottom: 0.5rem;
}

.titlePadding {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.firstTitlePadding {
  margin-bottom: 1rem;
}
