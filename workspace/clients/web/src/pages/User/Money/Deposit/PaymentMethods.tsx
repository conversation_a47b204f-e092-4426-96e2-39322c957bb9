import React, { useEffect } from "react";
import { useAuth0FetchJSON } from "../../../../globals/auth0-user";
import { TrackedValueState, useTrackedValue } from "../../../../util/react/tracked-value";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

import styles from "./money.module.css";

interface Card {
  brand: string,
  country: string,
  exp_month: number,
  exp_year: number,
  last4: string,
}

interface PaymentMethod {
  id: string,
  card: Card,
}

const responsive = {
  superLargeDesktop: {
    breakpoint: { max: 4000, min: 3000 },
    items: 5
  },
  desktop: {
    breakpoint: { max: 3000, min: 1024 },
    items: 3
  },
  tablet: {
    breakpoint: { max: 1024, min: 464 },
    items: 2
  },
  mobile: {
    breakpoint: { max: 464, min: 0 },
    items: 1
  }
};

interface PaymentMethodsProps {
  onUpdate?: () => void,
}

export function PaymentMethods({ onUpdate }: PaymentMethodsProps){
  const auth0FetchJSON = useAuth0FetchJSON();
  const { state, value: paymentMethods } = useTrackedValue<Array<PaymentMethod>>(
    { url: "/user/money/payment-method/list", requireAuth: true }
  );


  // Check if we're returning from Stripe
  useEffect(()=>{
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get("status");
    const sessionId = urlParams.get("session_id");

    if(status === "success" && sessionId) {
      // Clear URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);

      // Call onUpdate if provided
      if(onUpdate) {
        onUpdate();
      }
    }
  }, [onUpdate]);

  return (
    <div className={styles.paymentMethodsContainer}>
      <div className={styles.actionButtons}>
        <button
          className="button is-primary"
          onClick={async ()=>{
            try {
              const { stripeUrl } = await auth0FetchJSON("/user/money/payment-method/create") as { stripeUrl: string };
              // Store the current URL to return to after adding payment method
              // This isn't being used
              // sessionStorage.setItem("returnToUrl", window.location.href);
              window.location.href = stripeUrl;
            }catch(e){
              console.error(e);
              alert("Failed to add payment method. Please try again");
            }
          }}
        >
          Add Payment Method
        </button>
        <button
          className="button is-info"
          onClick={async ()=>{
            try {
              const { stripeUrl } = await auth0FetchJSON("/user/money/payment-method/manage") as { stripeUrl: string };
              window.location.href = stripeUrl;
            }catch(e){
              console.error(e);
              alert("Failed to manage payment methods. Please try again");
            }
          }}
        >
          Manage Payment Methods
        </button>
      </div>
      {
      state === TrackedValueState.Loading ? <div>Loading payment methods...</div> :
      state === TrackedValueState.Failure ? <div>Failed to load payment methods</div> :
        paymentMethods.length === 0 ?(
        <div className={styles.noPaymentMethods}>
          No payment methods found. Please add a payment method to make deposits.
        </div>
      ) : (
        <div className={styles.paymentMethodsList}>
          <Carousel responsive={responsive}>
            {paymentMethods.map((pm)=>(
              <div key={pm.id} className={styles.paymentMethodCard}>
                <div>Brand: <span>{pm.card.brand}</span></div>
                <div>Country: <span>{pm.card.country}</span></div>
                <div>Last 4 Digits: <span>{pm.card.last4}</span></div>
                <div>Exp: <span>{pm.card.exp_month}/{pm.card.exp_year}</span></div>
              </div>
            ))}
          </Carousel>
        </div>
      )}
    </div>
  );
}
