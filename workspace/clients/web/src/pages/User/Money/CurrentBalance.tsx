import React from "react";
import { useUsersMoney } from "../../../globals/user-preferences/money";
import { TrackedValueState } from "../../../util/react/tracked-value";
import { NanoUSDToDollarString } from "../../../components/displays/Money";

export function CurrentBalance(){
  const { state, value } = useUsersMoney();

  if(state !== TrackedValueState.SUCCESS) return null;

  return (
    <div className="box">
      <div>
        <strong>Balance: </strong>
        <span><NanoUSDToDollarString nano={value.balance} /></span>
      </div>
    </div>
  );
}

