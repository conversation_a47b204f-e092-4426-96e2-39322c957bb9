import React from "react";
import { Route } from "react-router";

import { relativePath } from "@divinci-ai/utils";

import { PATH_USER_INDEX } from "../paths";

import {
  PATH_USER_WALLET,
  PATH_USER_WALLET_TRANSACTIONS,
} from "./paths";

import { MoneyOutlet } from "./Outlet";
import { MoneyDepositPage } from "./Deposit";
import { MoneyTransactionsPage } from "./Transactions";

export const MoneyRoute = (
  <Route
    path={relativePath(PATH_USER_INDEX, PATH_USER_WALLET)}
    element={<MoneyOutlet />}
  >
    <Route index element={<MoneyDepositPage />} />
    <Route
      path={relativePath(PATH_USER_WALLET, PATH_USER_WALLET_TRANSACTIONS)}
      element={<MoneyTransactionsPage />}
    />
  </Route>
);
