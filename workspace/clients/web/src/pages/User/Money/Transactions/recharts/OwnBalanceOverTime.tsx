import React from "react";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  Legend,
  Brush,
  ResponsiveContainer,
} from "recharts";
import { ChartData } from "../prepareTransactionForChart";

import { TransactionTooltip } from "./ChartTransactionToolTip";

import moment from "moment";

export function OwnBalanceOverTime(
  { chartData }: { chartData: Array<ChartData> }
){
  if(chartData.length === 0){
    return <h1 className="title">No transaction data available for the selected period</h1>;
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <LineChart
        data={chartData}
        margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          tickFormatter={(value)=>moment(value).format("H:mm MMM/d/YYYY")}
          minTickGap={20}
          type="number"
          domain={[
            (dataMin: number)=>dataMin - 1000 * 60 * 5, // 5 min before
            (dataMax: number)=>dataMax + 1000 * 60 * 5, // 5 min after
          ]}
        />
        <YAxis />
        <Tooltip content={<TransactionTooltip />} />
        <Legend />
        <Line type="monotone" dataKey="prev_balance" stroke="#888888" />
        <Line type="monotone" dataKey="net_balance" stroke="#8884d8" />
        <Line type="monotone" dataKey="gain_balance" stroke="#82ca9d" />
        <Line type="monotone" dataKey="cost_balance" stroke="#ff7300" />
        <Brush
          dataKey="timestamp"
          tickFormatter={(value)=>moment(value).format("H:mm MMM/d/YYYY")}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

