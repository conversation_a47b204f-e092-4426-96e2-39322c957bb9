import React from "react";

import {
  AnimatedAxis, // any of these can be non-animated equivalents
  AnimatedGrid,
  XY<PERSON>hart,
  Tooltip,
  LineSeries,
} from "@visx/xychart";
import {
  curveLinear
} from "@visx/curve";

import { ChartData } from "../prepareTransactionForChart";
import { TransactionTooltip } from "./ToolTip";

export function TransactionChart(
  { chartData, width, height }: {
    chartData: Array<ChartData>,
    width: number, height: number,
  }
){
  return (
    <XYChart
      width={width}
      height={height}
      xScale={{ type: "time" }}
      yScale={{ type: "linear" }}
    >
      <AnimatedAxis orientation="left" />
      <AnimatedAxis orientation="bottom" />
      <AnimatedGrid columns={false} numTicks={4} />

      <text
        x={width / 2} y={height - 5}
        textAnchor="middle"
        fontSize={12}
        fill="#666"
      >
        Time
      </text>

      <text
        x={-height / 2} y={15}
        textAnchor="middle"
        transform="rotate(-90)"
        fontSize={12}
        fill="#666"
      >
        Balance (USD)
      </text>


      <LineSeries
        dataKey="Balance After Transaction"
        data={chartData}
        xAccessor={xAccessor}
        yAccessor={(d: ChartData)=>(d.net_balance)}
        curve={curveLinear}
      />

      <Tooltip
        snapTooltipToDatumX
        snapTooltipToDatumY
        showVerticalCrosshair
        showSeriesGlyphs
        renderTooltip={(props)=>{
          const { tooltipData, colorScale } = props;
          if(!tooltipData) return null;
          if(!tooltipData.nearestDatum) return null;
          if(!colorScale) return null;
          const dataPoint = tooltipData.nearestDatum.datum as ChartData;
          return <div>
            <div style={{ color: colorScale(tooltipData.nearestDatum.key) }}>
              {tooltipData.nearestDatum.key}
            </div>
            <TransactionTooltip dataPoint={dataPoint} />
          </div>;
        }}
      />
    </XYChart>
  );
}

function xAccessor(d: ChartData | undefined){
  if(!d) return new Date(0);
  return new Date(d.timestamp);
}
