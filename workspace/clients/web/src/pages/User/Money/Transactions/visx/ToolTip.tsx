
import React from "react";
import { ChartData } from "../prepareTransactionForChart";
import { NanoUSDToDollarString } from "../../../../../components/displays/Money";

export function TransactionTooltip(
  { dataPoint }: { dataPoint: ChartData }
){
  const txn = dataPoint.originalTxn;
  const info = dataPoint.toolTipInfo;

  return (
    <div style={{ padding: "8px", borderRadius: "8px" }}>
      <div><span style={keyStyle} >{new Date(dataPoint.timestamp).toLocaleString()}</span></div>
      <div>Before: <NanoUSDToDollarString nano={info.balanceBefore} /></div>
      <div>Net: <NanoUSDToDollarString nano={info.balanceBefore + info.net_gains + info.net_costs} /></div>
      <div>Gains: <NanoUSDToDollarString nano={info.net_gains} /></div>
      <div>Losses: <NanoUSDToDollarString nano={info.net_costs} /></div>
      <hr />
      <div><span style={keyStyle} >Transaction ID:</span> {txn._id}</div>
      <div><span style={keyStyle} >Type:</span> {txn.transactionType}</div>
      <div><span style={keyStyle} >Step:</span> {txn.transactionStep}</div>
      <div><span style={keyStyle} >Parts:</span> {txn.parts.length}</div>
    </div>
  );
}

const keyStyle = {
  fontWeight: "bold",
};
