import React, { useMemo } from "react";

import { NanoUSDWalletTransactionDoc } from "@divinci-ai/models";
import { NanoUSDToDollarString } from "../../../../components/displays/Money";
import { UserInfoProvider, useUserInfo } from "../../../../components/data/UserInfoContext";

export function TransactionList({ transactions }: { transactions: Array<NanoUSDWalletTransactionDoc> }){
  return <UserInfoProvider>
    {transactions.map((transaction)=>(
      <TransactionItem key={transaction._id} transaction={transaction} />
    ))}
  </UserInfoProvider>;
}

function TransactionItem({ transaction }: { transaction: NanoUSDWalletTransactionDoc }){
  const runner = useUserInfo(transaction.runnerUser);
  const netChanges = useMemo(
    ()=>{
      const userChanges: Record<string, bigint> = {};
      for(const part of transaction.parts){
        const current = userChanges[part.walletUser] || 0n;
        userChanges[part.walletUser] = current + part.amount;
      }
      return userChanges;
    },
    [transaction.parts]
  );
  return (
    <div className="box">
      <h2>{transaction._id}</h2>
      <div>{transaction.transactionType}</div>
      <div>{transaction.transactionStep}</div>
      <div>Runner: {runner.fetching === 1 ? runner.nickname : transaction.runnerUser}</div>
      {Object.entries(netChanges).length === 0 ? null : (
        <table className="table">
          <thead>
            <tr>
              <th>User</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>{Object.entries(netChanges).map(([user, amount])=>(
            <TransactionChanges key={user} user={user} amount={amount} />
          ))}</tbody>
        </table>
      )}
    </div>
  );
}

function TransactionChanges({ user, amount }: { user: string, amount: bigint }){
  const wallet = useUserInfo(user);
  return (
    <tr key={user}>
      <td>{wallet.fetching === 1 ? wallet.nickname : user}</td>
      <td>
        <NanoUSDToDollarString nano={amount} />
      </td>
    </tr>
  );
}
