import React, { useMemo, useState, useEffect } from "react";

import { ChartData } from "../prepareTransactionForChart";

import { TransactionChart } from "./TransactionChart";
import { TransactionBrush } from "./TransactionBrush";

export function OwnBalanceOverTime(
  { chartData }: { chartData: Array<ChartData> }
){
  const [zoomDomain, setZoomDomain] = useState<[number, number] | null>(null);

  useEffect(()=>{
    if(chartData.length > 0 && !zoomDomain) {
      const first = chartData[0].timestamp;
      const last = chartData[chartData.length - 1].timestamp;
      setZoomDomain([first, last]); // or a smaller range to zoom initially
    }
  }, [chartData]);

  const filteredData = useMemo(()=>{
    if(!zoomDomain) return chartData;
    return chartData.filter(d=>d.timestamp >= zoomDomain[0] && d.timestamp <= zoomDomain[1]);
  }, [zoomDomain, chartData]);

  if(chartData.length === 0){
    return <h1 className="title">No transaction data available for the selected period</h1>;
  }

  return (
    <>
    <TransactionChart
      chartData={filteredData}
      width={800} height={300}
    />
    <TransactionBrush
      chartData={chartData}
      onZoomChange={(newDomain)=>(setZoomDomain(newDomain))}
      width={800} height={100}
    />
    </>
  );
}
