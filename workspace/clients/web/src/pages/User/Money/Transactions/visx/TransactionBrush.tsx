import React, { useMemo } from "react";

import { ChartData } from "../prepareTransactionForChart";
import { Brush } from "@visx/brush";
import { scaleLinear, scaleTime } from "@visx/scale";
import { extent, max } from "d3-array";

import { LinePath } from "@visx/shape";
import { PatternLines } from "@visx/pattern";
import { Group } from "@visx/group";

const BRUSH_MARGIN = { top: 0, bottom: 20, left: 50, right: 20 };

export function TransactionBrush(
  { chartData, onZoomChange, width, height }: {
    chartData: Array<ChartData>,
    onZoomChange: (range: [number,number])=>void,
    width: number, height: number,
  }
){
  const patternId = useMemo(()=>`brush-pattern-${Math.random().toString(36).slice(2)}`, []);


  const xDomain = useMemo(()=>extent(chartData, (d)=>d.timestamp) as [number, number], [chartData]);
  const yMax = useMemo(()=>Math.max(1, max(chartData, (d)=>d.net_balance) ?? 0), [chartData]);

  const xScale = useMemo(()=>{
    return scaleTime({
      domain: xDomain,
      range: [BRUSH_MARGIN.left, width - BRUSH_MARGIN.right],
    });
  }, [xDomain, width]);

  const yScale = useMemo(()=>{
    return scaleLinear({
      domain: [0, yMax],
      range: [height - BRUSH_MARGIN.bottom, BRUSH_MARGIN.top],
    });
  }, [yMax, height]);


  return (
    <svg width={width} height={height} >
      <PatternLines
        id={patternId}
        height={8}
        width={8}
        stroke="rgba(0,0,0,0.3)"
        strokeWidth={1}
        orientation={["diagonal"]}
      />

      <Group left={0}>
        <LinePath
          data={chartData}
          x={(d)=>(xScale(d.timestamp))}
          y={(d)=>(yScale(d.net_balance))}
          stroke="#8884d8"
          strokeWidth={2}
        />

        <Brush
          xScale={xScale}
          yScale={yScale}
          width={width}
          height={height}
          margin={BRUSH_MARGIN}
          handleSize={8}
          brushDirection="horizontal"
          resizeTriggerAreas={["left", "right"]}
          onChange={(domain)=>{
            if(!domain) return;
            const { x0, x1 } = domain;
            onZoomChange([x0, x1]);
          }}
          selectedBoxStyle={{ fill: `url(#${patternId})`, stroke: "#000" }}
          useWindowMoveEvents
        />
      </Group>
    </svg>
  );
}

