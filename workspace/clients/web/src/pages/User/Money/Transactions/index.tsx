
import React, { useState, useMemo, useCallback } from "react";

import { useAuth0 } from "@auth0/auth0-react";

import { TrackedValueState, useTrackedGetter } from "../../../../util/react/tracked-value";
import { prepareTransactionsForChart } from "./prepareTransactionForChart";

import { OwnBalanceOverTime as RechartsChart } from "./recharts/OwnBalanceOverTime";
import { OwnBalanceOverTime as VisxChart } from "./visx/OwnBalanceOverTime";
import { TransactionList } from "./TransactionList";
import { getUsersTransactions } from "@divinci-ai/actions";
import { useAuth0Fetch } from "../../../../globals/auth0-user";


const MONTH_IN_MILLISECONDS = 1000 * 60 * 60 * 24 * 30;

export function MoneyTransactionsPage(){
  const { user } = useAuth0();
  const auth0Fetch = useAuth0Fetch();
  const [range, setRange] = useState({ start: Date.now() - MONTH_IN_MILLISECONDS, end: Date.now() });

  const { state, value } = useTrackedGetter({
    getter: useCallback(()=>{
      return getUsersTransactions(auth0Fetch, range);
    }, [auth0Fetch, range]),
  });

  const chartData = useMemo(
    ()=>!value ? null : !user?.sub ? null : prepareTransactionsForChart(user.sub, value),
    [user, value]
  );


  return (
    <div className="container">
      <h1>Transactions</h1>
      <hr/>
      {state === TrackedValueState.Loading ? <div>Loading...</div> : (
        <div className="box">
          <VisxChart chartData={chartData || []} />
        </div>
      )}
      {state !== TrackedValueState.SUCCESS ? null : (
        <TransactionList transactions={value} />
      )}
    </div>
  );
}

