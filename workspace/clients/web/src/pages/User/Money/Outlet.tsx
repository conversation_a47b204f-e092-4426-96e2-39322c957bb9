import React from "react";
import { Outlet } from "react-router";
import { PathTabsOutlet } from "../../../components/Tabs";
import {
  PATH_USER_WALLET,
  PATH_USER_WALLET_TRANSACTIONS,
} from "./paths";


export function MoneyOutlet(){
  return (
    <PathTabsOutlet tabs={[
      { title: "Deposit", pathname: PATH_USER_WALLET },
      { title: "Transactions", pathname: PATH_USER_WALLET_TRANSACTIONS },
    ]}>
      <Outlet />
    </PathTabsOutlet>
  );
}
