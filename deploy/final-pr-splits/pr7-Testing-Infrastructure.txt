=== PR 7: Testing-Infrastructure ===
test-scripts/test-minio-connectivity.sh
test-scripts/test-s3-operations.js
workspace/clients/playwright/.auth/user.json
workspace/clients/tests/.gitignore
workspace/clients/tests/.test-data-cache.json
workspace/clients/tests/Migration-Guide.md
workspace/clients/tests/README.md
workspace/clients/tests/Resource-Pool-Examples.md
workspace/clients/tests/auth.json
workspace/clients/tests/auth/admin.json
workspace/clients/tests/auth/owner.json
workspace/clients/tests/auth/user.json
workspace/clients/tests/hybrid-TODO.md
workspace/clients/tests/package.json
workspace/clients/tests/playwright.config.ts
workspace/clients/tests/src/api-test-mapping.ts
workspace/clients/tests/src/api/api-client.ts
workspace/clients/tests/src/api/user-group-api.ts
workspace/clients/tests/src/api/vector-api.ts
workspace/clients/tests/src/api/whitelabel-api.ts
workspace/clients/tests/src/auth.setup.ts
workspace/clients/tests/src/auth/api-auth.setup.ts
workspace/clients/tests/src/auth/auth-utils.ts
workspace/clients/tests/src/auth/auth.setup.ts
workspace/clients/tests/src/config/config.ts
workspace/clients/tests/src/config/env.ts
workspace/clients/tests/src/constants/auth0.ts
workspace/clients/tests/src/constants/internal.ts
workspace/clients/tests/src/e2e/README.md
workspace/clients/tests/src/e2e/create-whitelabel.spec.ts
workspace/clients/tests/src/e2e/login.spec.ts
workspace/clients/tests/src/e2e/resource-intensive/README.md
workspace/clients/tests/src/e2e/screenshots/.gitignore
workspace/clients/tests/src/e2e/utils/common.ts
workspace/clients/tests/src/e2e/utils/fine-tune-helpers.ts
workspace/clients/tests/src/e2e/utils/test-auth.ts
workspace/clients/tests/src/e2e/utils/test-data-cache.ts
workspace/clients/tests/src/github-test-helper.ts
workspace/clients/tests/src/globals/auth0/client.ts
workspace/clients/tests/src/globals/auth0/index.ts
workspace/clients/tests/src/globals/auth0/mock-auth.ts
workspace/clients/tests/src/globals/auth0/token-cache.ts
workspace/clients/tests/src/globals/auth0/types.ts
workspace/clients/tests/src/index.ts
workspace/clients/tests/src/resources/resource-pool.ts
workspace/clients/tests/src/resources/user-group-pool.ts
workspace/clients/tests/src/resources/vector-pool.ts
workspace/clients/tests/src/resources/whitelabel-pool.ts
workspace/clients/tests/src/story-test-auth.setup.ts
workspace/clients/tests/src/story-test/util/group/mock-user-group-service.ts
workspace/clients/tests/src/story-test/util/group/pooled-actions.ts
workspace/clients/tests/src/story-test/util/permission/usage/permission-types/group-specific.ts
workspace/clients/tests/src/test-mapping.ts
workspace/clients/tests/src/tests/mock-auth.spec.ts
workspace/clients/tests/src/tests/resource-pool.spec.ts
workspace/clients/tests/src/tests/user-group.example.spec.ts
workspace/clients/tests/src/tests/user-group.migrated.spec.ts
workspace/clients/tests/src/tests/user-group.spec.ts
workspace/clients/tests/src/tests/vector.spec.ts
workspace/clients/tests/src/tests/whitelabel.spec.ts
workspace/clients/tests/src/util/request-throttler.ts
workspace/clients/tests/src/util/resource-pool.ts
workspace/clients/tests/src/utils/auth.ts
workspace/clients/tests/src/utils/file-upload.ts
workspace/clients/tests/src/utils/screenshot.ts
workspace/clients/tests/src/utils/test-utils.ts
workspace/clients/tests/src/utils/whitelabel.ts
workspace/clients/web/vitest.setup.ts
workspace/playwright/.auth/user.json
workspace/resources/server-utils/src/http-request/__tests__/middleware.test.ts
workspace/servers/public-api-live/tests/unit/src/setup/database/setupDBs.test.skip.ts
workspace/servers/public-api/tests/unit/src/ux/user/router/phone-number/subscribeToChatWithPhoneNumber.test.skip.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/pending-file.test.ts
