=== PR 2: mTLS-Security ===
change-cloudflare-ssl-mode.sh
check-certificate-details.sh
check-cloudflare-ssl-mode.sh
client.crt
client_pkcs8.key
cloudflare-options-mtls-guide.md
deploy/docker/ci/api-mtls-entrypoint.sh
deploy/docker/ci/service-mtls-entrypoint.sh
deploy/kubernetes/mtls-certificates.yaml
deploy/mTLS-CF-Rule
deploy/scripts/add-ca-to-certificates.sh
deploy/scripts/check-server-cert-secret.sh
deploy/scripts/copy-cert-files.sh
deploy/scripts/create-ca-cert-secret.sh
deploy/scripts/create-client-cert-secrets.sh
deploy/scripts/create-individual-cert-secrets.sh
deploy/scripts/create-mtls-certs-secret.sh
deploy/scripts/create-mtls-secret.sh
deploy/scripts/create-server-cert-secret.sh
deploy/scripts/download-cloudflare-ca-certs.sh
deploy/scripts/extract-gcp-certs.sh
deploy/scripts/extract-mtls-certs.sh
deploy/scripts/generate-mtls-certs.sh
deploy/scripts/install-cloudflare-origin-cert.sh
deploy/scripts/monitor-cert-expiration.sh
deploy/scripts/origin_ca_ecc_root.pem
deploy/scripts/origin_ca_rsa_root.pem
deploy/scripts/private-keys/staging/certs/mtls/origin_ca_ecc_root.pem
deploy/scripts/private-keys/staging/certs/mtls/origin_ca_rsa_root.pem
deploy/scripts/rotate-mtls-certs.sh
deploy/scripts/test-cloudflare-mtls.sh
deploy/scripts/test-local-mtls.sh
deploy/scripts/test-mtls-connection.sh
deploy/scripts/test-mtls-performance.sh
deploy/scripts/tests/analysis/mtls-implementation-plan.md
deploy/scripts/tests/analysis/mtls-implementation-progress.md
deploy/scripts/tests/analysis/mtls-implementation-summary.md
deploy/scripts/tests/certificate-chain-verification.sh
deploy/scripts/tests/certificate-issue-analysis.md
deploy/scripts/tests/certificate-presentation-test.sh
deploy/scripts/tests/cf-cert-test.sh
deploy/scripts/tests/check-certificate-mounts.sh
deploy/scripts/tests/check-cloudflare-ssl-mode.sh
deploy/scripts/tests/cors-mtls-test-fixed.sh
deploy/scripts/tests/cors-mtls-test.sh
deploy/scripts/tests/curl-tls-analysis-wrapper.sh
deploy/scripts/tests/curl-tls-analysis.sh
deploy/scripts/tests/docs/curl-tls-analysis-implementation.md
deploy/scripts/tests/docs/curl-tls-integration.md
deploy/scripts/tests/fix-certificate-mounts.sh
deploy/scripts/tests/fix-mtls-enable-value.sh
deploy/scripts/tests/generate-client-cert-key.sh
deploy/scripts/tests/mtls-comprehensive-test-wrapper.sh
deploy/scripts/tests/mtls-comprehensive-test.sh
deploy/scripts/tests/mtls-connection-test-wrapper.sh
deploy/scripts/tests/mtls-connection-test.sh
deploy/scripts/tests/mtls-cors-fix-implementation-plan.md
deploy/scripts/tests/mtls-cors-implementation-guide.md
deploy/scripts/tests/mtls-diagnostics.sh
deploy/scripts/tests/mtls-framework/README.md
deploy/scripts/tests/mtls-framework/certificate-manager.sh
deploy/scripts/tests/mtls-framework/common.sh
deploy/scripts/tests/mtls-framework/run-tests.sh
deploy/scripts/tests/mtls-testing-guide.md
deploy/scripts/tests/mtls-troubleshooting.md
deploy/scripts/tests/nmap-ssl-scan.sh
deploy/scripts/tests/run-mtls-cors-fixes.sh
deploy/scripts/tests/set-cloudflare-ssl-mode.sh
deploy/scripts/tests/sslyze-scan.sh
deploy/scripts/tests/tcpdump-tls-capture.sh
deploy/scripts/tests/tls-testing/README.md
deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh
deploy/scripts/tests/tls-testing/bin/run-tls-tests.sh
deploy/scripts/tests/tls-testing/docs/CONTRIBUTING.md
deploy/scripts/tests/tls-testing/docs/ROADMAP.md
deploy/scripts/tests/tls-testing/docs/curl-tls-analysis-developer-guide.md
deploy/scripts/tests/tls-testing/docs/curl-tls-analysis-user-guide.md
deploy/scripts/tests/tls-testing/docs/quick-start-guide.md
deploy/scripts/tests/tls-testing/lib/curl_tls_parser.sh
deploy/scripts/tests/tls-testing/lib/reporting.sh
deploy/scripts/tests/tls-testing/lib/tls_utils.sh
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/curl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/nmap_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/openssl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/report.json
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/summary.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_curl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_headers.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/curl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/nmap_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/openssl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/report.json
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/summary.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_curl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_headers.txt
deploy/scripts/tests/tshark-tls-analysis.sh
deploy/scripts/update-gcp-cert-secret.sh
deploy/scripts/update-k8s-mtls.sh
deploy/scripts/verify-cert-before-deploy.sh
deploy/scripts/verify-cloudflare-mtls.sh
docker/mtls-example.yml
docker/mtls-local.yml
docker/nginx-mtls.conf
docs/mtls-gcp-secrets.md
docs/mtls-setup.md
generate-cloudflare-origin-cert.sh
mTLS-PLAN.md
mTLS-README.md
mTLS-SECURITY.md
mTLS-TODO.md
mtls-options-fix-readme.md
mtls-options-fix-summary.md
resolve-mtls-options-issue.md
server.crt
server_tls_policy.yaml
target_proxy.yaml
tools/mtls-testing/mtls-testing-README.md
tools/mtls-testing/test-minio-connectivity.sh
tools/mtls-testing/test-mtls-connections.sh
tools/mtls-testing/test_mtls.py
tools/mtls-testing/test_mtls_advanced.py
trust_config.yaml
url-map.yaml
workspace/resources/mtls/LICENSE.md
workspace/resources/mtls/README.md
workspace/resources/mtls/docs/certificate-generation.md
workspace/resources/mtls/examples/api-client.ts
workspace/resources/mtls/examples/open-parse-client.py
workspace/resources/mtls/examples/open-parse-server.py
workspace/resources/mtls/examples/worker-server.ts
workspace/resources/mtls/package.json
workspace/resources/mtls/src/certificates/index.ts
workspace/resources/mtls/src/certificates/loader.ts
workspace/resources/mtls/src/certificates/paths.ts
workspace/resources/mtls/src/client/agent.ts
workspace/resources/mtls/src/client/fetch.ts
workspace/resources/mtls/src/client/index.ts
workspace/resources/mtls/src/index.ts
workspace/resources/mtls/src/server/index.ts
workspace/resources/mtls/src/server/middleware.ts
workspace/resources/mtls/src/server/setup.ts
workspace/resources/mtls/src/types.ts
workspace/resources/mtls/tsconfig.json
workspace/resources/python-utils/mtls_utils.py
workspace/resources/server-globals/src/certificates/README.md
workspace/resources/server-globals/src/certificates/paths.ts
workspace/resources/server-globals/tests/unit/certificates/paths.test.ts
workspace/resources/server-tools/src/audio/speaker-diarization/divinci-pyannote/api/processFile/mtls-fetch.ts
workspace/resources/server-tools/src/audio/std-utils/mtls-fetch.ts
workspace/resources/server-utils/src/certificates/README.md
workspace/resources/server-utils/src/certificates/validation.ts
workspace/resources/server-utils/src/http-request/mtls-agent.ts
workspace/resources/server-utils/tests/unit/certificates/README.md
workspace/resources/server-utils/tests/unit/certificates/cert-chain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-expiration.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-info.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-key-match.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate.test.ts
workspace/resources/server-utils/tests/unit/certificates/validation.test.ts
workspace/servers/public-api-live/src/middleware/mtls-debug.ts
workspace/servers/public-api/deploy/docker/ci/api-mtls-entrypoint.sh
workspace/servers/public-api/tests/unit/mtls/README.md
workspace/servers/public-api/tests/unit/mtls/certificate-loading.test.ts
workspace/servers/public-api/tests/unit/mtls/client-mtls.test.ts
workspace/servers/public-api/tests/unit/mtls/client-verification.test.ts
workspace/servers/public-api/tests/unit/mtls/https-server.test.ts
workspace/workers/audio-speaker-diarization@pyannote/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/audio-speaker-diarization@pyannote/mtls_utils.py
workspace/workers/audio-splitter@ffmpeg/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/audio-splitter@ffmpeg/src/mtls-server.ts
workspace/workers/chunks-workflow/test/security.test.ts
workspace/workers/chunks-workflow/test/security/security.test.js
workspace/workers/open-parse/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/open-parse/mtls_utils.py
