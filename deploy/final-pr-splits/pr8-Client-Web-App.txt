=== PR 8: Client-Web-App ===
workspace/clients/auth.json
workspace/clients/auth/admin.json
workspace/clients/auth/owner.json
workspace/clients/auth/user.json
workspace/clients/embed/package.json
workspace/clients/web/.env.local
workspace/clients/web/nginx.conf
workspace/clients/web/package.json
workspace/clients/web/src/components/Permission/data/DocPermissionContext.tsx
workspace/clients/web/src/components/Permission/input/ChangeOwner.tsx
workspace/clients/web/src/components/Permission/input/GeneralUser.tsx
workspace/clients/web/src/components/Permission/input/IndividualUser.tsx
workspace/clients/web/src/components/Permission/input/UserGroup.tsx
workspace/clients/web/src/components/ReleaseComponents/Assistant/data/UsableFineTuneModels.tsx
workspace/clients/web/src/components/ReleaseComponents/Assistant/index.tsx
workspace/clients/web/src/components/ReleaseComponents/MessagePrefix/data/MessagePrefix.tsx
workspace/clients/web/src/components/ReleaseComponents/MessagePrefix/index.tsx
workspace/clients/web/src/components/ReleaseComponents/PromptModeration/data/PromptModeration.tsx
workspace/clients/web/src/components/ReleaseComponents/PromptModeration/index.tsx
workspace/clients/web/src/components/ReleaseComponents/RagVector/data/RagVectorIndex.tsx
workspace/clients/web/src/components/ReleaseComponents/RagVector/index.tsx
workspace/clients/web/src/components/ReleaseComponents/ThreadPrefix/index.tsx
workspace/clients/web/src/components/ServiceHealthCheck/HealthCheckTester.tsx
workspace/clients/web/src/components/Tabs/index.tsx
workspace/clients/web/src/components/Tabs/tab-menu.tsx
workspace/clients/web/src/components/Transcript/data/RatingContext.tsx
workspace/clients/web/src/components/Transcript/data/TranscriptContext.tsx
workspace/clients/web/src/components/Transcript/views/Chat/Transcript/MessageList.tsx
workspace/clients/web/src/components/Transcript/views/Chat/Transcript/RenderContent.tsx
workspace/clients/web/src/components/Transcript/views/Chat/Transcript/RenderMermaid.tsx
workspace/clients/web/src/components/Transcript/views/Emoji/context/EmojiPickerComponent.tsx
workspace/clients/web/src/components/Transcript/views/File/Form/Input.tsx
workspace/clients/web/src/components/Transcript/views/File/Form/index.tsx
workspace/clients/web/src/components/data/ReleaseInfoContext.tsx
workspace/clients/web/src/components/data/UserInfoContext.tsx
workspace/clients/web/src/components/displays/BatchStatus.tsx
workspace/clients/web/src/components/displays/Timestamp.tsx
workspace/clients/web/src/components/inputs/CustomSelect/index.tsx
workspace/clients/web/src/components/inputs/ExtractableFileArrayInput/index.tsx
workspace/clients/web/src/contexts/HealthCheckContext.tsx
workspace/clients/web/src/globals/api.ts
workspace/clients/web/src/globals/constants/api.ts
workspace/clients/web/src/globals/local-menu.tsx
workspace/clients/web/src/globals/server-error.tsx
workspace/clients/web/src/index.tsx
workspace/clients/web/src/pages/Chat/ChatIndex/Drawers/left-drawer.tsx
workspace/clients/web/src/pages/Chat/ChatItem/ChatItemIndex/Drawers/left-drawer.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/FineTune/File-Editor/data/CSVEditorStore/db-operations/save.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Form/ChunkingToolSelect.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Item/FileItemDisplay/DeleteButton.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Item/FileItemDisplay/Finalize.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Item/FileItemDisplay/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/InputFileForm.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/submit-add-file-workflow.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/Transcript/Drawers/components/PrefixEditor.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/Transcript/Drawers/left-drawer.tsx
workspace/clients/web/tsconfig.json
workspace/clients/web/typings/trusted-types.d.ts
