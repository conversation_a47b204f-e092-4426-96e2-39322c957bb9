=== PR 8: Testing Infrastructure & E2E Tests ===
.github/scripts/parse-deploy.test.sh
deploy/cli/tests/build-deploy.test.js
deploy/cli/tests/integration.test.js
deploy/cli/tests/parse-args.test.js
deploy/cli/tests/run-tests.test.js
deploy/cli/tests/service-utils.test.js
test-scripts/test-minio-connectivity.sh
test-scripts/test-s3-operations.js
vitest.config.mjs
workspace/clients/playwright/.auth/user.json
workspace/clients/tests/.gitignore
workspace/clients/tests/.test-data-cache.json
workspace/clients/tests/Migration-Guide.md
workspace/clients/tests/README.md
workspace/clients/tests/Resource-Pool-Examples.md
workspace/clients/tests/StorageState_Resource-Pooling-Architecture-for-Testing.md
workspace/clients/tests/auth.json
workspace/clients/tests/auth/admin.json
workspace/clients/tests/auth/owner.json
workspace/clients/tests/auth/user.json
workspace/clients/tests/docs/audio-fine-tune-tests.md
workspace/clients/tests/env/test-local.env
workspace/clients/tests/env/test-staging.env
workspace/clients/tests/fixtures/test-audio.mp3
workspace/clients/tests/hybrid-TODO.md
workspace/clients/tests/package.json
workspace/clients/tests/playwright.config.ts
workspace/clients/tests/src/api-test-mapping.ts
workspace/clients/tests/src/api/api-client.ts
workspace/clients/tests/src/api/user-group-api.ts
workspace/clients/tests/src/api/vector-api.ts
workspace/clients/tests/src/api/whitelabel-api.ts
workspace/clients/tests/src/auth.setup.ts
workspace/clients/tests/src/auth/api-auth.setup.ts
workspace/clients/tests/src/auth/auth-utils.ts
workspace/clients/tests/src/auth/auth.setup.ts
workspace/clients/tests/src/config/config.ts
workspace/clients/tests/src/config/env.ts
workspace/clients/tests/src/constants/auth0.ts
workspace/clients/tests/src/constants/internal.ts
workspace/clients/tests/src/e2e/README.md
workspace/clients/tests/src/e2e/audio-rag-status-check.spec.ts
workspace/clients/tests/src/e2e/audio-rag-status.spec.ts
workspace/clients/tests/src/e2e/audio-to-rag-extended.spec.ts
workspace/clients/tests/src/e2e/audio-transcript-upload.spec.ts
workspace/clients/tests/src/e2e/audio-transcript-zip-upload.spec.ts
workspace/clients/tests/src/e2e/chunks-workflow-api.spec.ts
workspace/clients/tests/src/e2e/chunks-workflow-real.spec.ts
workspace/clients/tests/src/e2e/create-whitelabel.spec.ts
workspace/clients/tests/src/e2e/login.spec.ts
workspace/clients/tests/src/e2e/rag-vector-create.spec.ts
workspace/clients/tests/src/e2e/rag-vector-upload-file-status.spec.ts
workspace/clients/tests/src/e2e/rag-vector-upload-file.spec.ts
workspace/clients/tests/src/e2e/resource-intensive/README.md
workspace/clients/tests/src/e2e/resource-intensive/audio-transcript-to-fine-tune.spec.ts
workspace/clients/tests/src/e2e/screenshots/.gitignore
workspace/clients/tests/src/e2e/utils/audio-transcript-helpers.ts
workspace/clients/tests/src/e2e/utils/common.ts
workspace/clients/tests/src/e2e/utils/fine-tune-helpers.ts
workspace/clients/tests/src/e2e/utils/test-auth.ts
workspace/clients/tests/src/e2e/utils/test-data-cache.ts
workspace/clients/tests/src/github-test-helper.ts
workspace/clients/tests/src/globals/auth0/client.ts
workspace/clients/tests/src/globals/auth0/index.ts
workspace/clients/tests/src/globals/auth0/mock-auth.ts
workspace/clients/tests/src/globals/auth0/storage-state.ts
workspace/clients/tests/src/globals/auth0/token-cache.ts
workspace/clients/tests/src/globals/auth0/types.ts
workspace/clients/tests/src/index.ts
workspace/clients/tests/src/resources/resource-pool.ts
workspace/clients/tests/src/resources/user-group-pool.ts
workspace/clients/tests/src/resources/vector-pool.ts
workspace/clients/tests/src/resources/whitelabel-pool.ts
workspace/clients/tests/src/story-test-auth.setup.ts
workspace/clients/tests/src/story-test/util/group/mock-user-group-service.ts
workspace/clients/tests/src/story-test/util/group/pooled-actions.ts
workspace/clients/tests/src/story-test/util/group/storage-state-actions.ts
workspace/clients/tests/src/story-test/util/permission/usage/permission-types/group-specific.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/create-audio-transcript.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/index.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/upload-zip-file.ts
workspace/clients/tests/src/story-test/workbench/rag/tests/free-write/file-lifecycle/index.ts
workspace/clients/tests/src/test-mapping.ts
workspace/clients/tests/src/tests/mock-auth.spec.ts
workspace/clients/tests/src/tests/resource-pool.spec.ts
workspace/clients/tests/src/tests/user-group.example.spec.ts
workspace/clients/tests/src/tests/user-group.migrated.spec.ts
workspace/clients/tests/src/tests/user-group.spec.ts
workspace/clients/tests/src/tests/vector.spec.ts
workspace/clients/tests/src/tests/whitelabel.spec.ts
workspace/clients/tests/src/util/request-throttler.ts
workspace/clients/tests/src/util/resource-pool.ts
workspace/clients/tests/src/util/storage-state-resource-pool.ts
workspace/clients/tests/src/utils/auth.ts
workspace/clients/tests/src/utils/file-upload.ts
workspace/clients/tests/src/utils/screenshot.ts
workspace/clients/tests/src/utils/storage.ts
workspace/clients/tests/src/utils/test-utils.ts
workspace/clients/tests/src/utils/whitelabel.ts
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice-faststart.mp4
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice.flac
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice.mp4
workspace/clients/tests/test-files/small-audio-test.zip
workspace/clients/web/vitest.setup.ts
workspace/playwright/.auth/user.json
workspace/resources/server-globals/tests/cors/domains-from-env.test.ts
workspace/resources/server-globals/tests/cors/headers.test.ts
workspace/resources/server-globals/tests/unit/certificates/paths.test.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.edge-cases.test.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.test.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/utilities.test.ts
workspace/resources/server-tools/tests/rag/vector/cloudflare/vector-index.test.ts
workspace/resources/server-tools/tests/rag/workflows/ChunkWorkflowClient.integration.test.ts
workspace/resources/server-tools/tests/rag/workflows/ChunkWorkflowClient.test.ts
workspace/resources/server-utils/src/http-request/__tests__/middleware.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-chain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-expiration.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-info.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-key-match.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate.test.ts
workspace/resources/server-utils/tests/unit/certificates/validation.test.ts
workspace/resources/tools/tests/audio-transcript/audio-to-rag-extension.test.ts
workspace/servers/public-api-live/tests/unit/src/setup/database/setupDBs.test.skip.ts
workspace/servers/public-api/tests/unit/mtls/certificate-loading.test.ts
workspace/servers/public-api/tests/unit/mtls/client-mtls.test.ts
workspace/servers/public-api/tests/unit/mtls/client-verification.test.ts
workspace/servers/public-api/tests/unit/mtls/https-server.test.ts
workspace/servers/public-api/tests/unit/src/ux/user/router/phone-number/subscribeToChatWithPhoneNumber.test.skip.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/add-chunks.test.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/pending-file.test.ts
workspace/workers/chunks-workflow/chunks-workflow_tail/test/index.spec.js
workspace/workers/chunks-workflow/test/__snapshots__/snapshot.test.ts.snap
workspace/workers/chunks-workflow/test/basic-processor-functionality.test.js
workspace/workers/chunks-workflow/test/boundary.test.ts
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/edge-cases.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/edge-cases.test.js
workspace/workers/chunks-workflow/test/boundary/skip-original-tests.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.test.js
workspace/workers/chunks-workflow/test/cloudflare-workers.test.js
workspace/workers/chunks-workflow/test/concurrency.test.ts
workspace/workers/chunks-workflow/test/concurrency/parallel-processing.test.js
workspace/workers/chunks-workflow/test/concurrency/resource-contention.test.js
workspace/workers/chunks-workflow/test/error-recovery/retry-logic.test.ts
workspace/workers/chunks-workflow/test/import.test.js
workspace/workers/chunks-workflow/test/index.test.js
workspace/workers/chunks-workflow/test/index.test.ts
workspace/workers/chunks-workflow/test/integration/end-to-end.test.ts
workspace/workers/chunks-workflow/test/parameterized.test.ts
workspace/workers/chunks-workflow/test/parameterized/processor-parameterized.test.js
workspace/workers/chunks-workflow/test/parameterized/workflow-parameterized.test.js
workspace/workers/chunks-workflow/test/performance.test.ts
workspace/workers/chunks-workflow/test/performance/chunking-performance.test.js
workspace/workers/chunks-workflow/test/performance/workflow-performance.test.js
workspace/workers/chunks-workflow/test/processors/openparse.mock.test.ts
workspace/workers/chunks-workflow/test/processors/openparse.smoke.test.ts
workspace/workers/chunks-workflow/test/processors/unstructured.test.js
workspace/workers/chunks-workflow/test/processors/unstructured.test.ts
workspace/workers/chunks-workflow/test/property-based.test.ts
workspace/workers/chunks-workflow/test/property-based/chunking.test.js
workspace/workers/chunks-workflow/test/property-based/chunking.test.skip.js
workspace/workers/chunks-workflow/test/security.test.ts
workspace/workers/chunks-workflow/test/security/security.test.js
workspace/workers/chunks-workflow/test/snapshot.test.ts
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/processor-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/workflow-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/processor-output.test.js
workspace/workers/chunks-workflow/test/snapshot/workflow-output.test.js
workspace/workers/chunks-workflow/test/types.test.ts
workspace/workers/chunks-workflow/test/utils.test.ts
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.js
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.ts
workspace/workers/chunks-workflow/test/utils/storage-client.test.js
workspace/workers/chunks-workflow/test/utils/storage-client.test.ts
workspace/workers/chunks-workflow/test/utils/vectorize-api.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/add-chunks-to-file-record.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/filter-chunks.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/initialize-workflow.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/link-file-to-rag.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/update-file-status.test.ts
workspace/workers/chunks-workflow/tsconfig.test.json
workspace/workers/chunks-workflow/vitest.config.js
workspace/workers/chunks-workflow/vitest.config.ts
workspace/workers/chunks-workflow/vitest.config.ts.bak
workspace/workers/chunks-workflow/vitest.config.ts.new
