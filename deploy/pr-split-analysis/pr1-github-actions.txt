=== PR 1: GitHub Actions & CI/CD Infrastructure ===
.claude/settings.local.json
.github/act/.actrc
.github/act/.github/workflows/reusable/reusable
.github/act/README.md
.github/act/deploy-services-act-test.yml
.github/act/events/deploy-comment-event.json
.github/act/events/pull_request_event.json
.github/act/events/workflow-dispatch-event.json
.github/act/minimal-test.yml
.github/act/mock-workflows/build-deploy-changed-services.yml
.github/act/run-refactored-test.sh
.github/act/run-simple-act-test.sh
.github/act/secrets.env
.github/act/test-deploy-command.sh
.github/act/test-event.json
.github/act/test-git-auth-actions.sh
.github/act/test-git-auth-direct-gh.sh
.github/act/test-git-auth-final.sh
.github/act/test-git-auth-gh-cli-setup.sh
.github/act/test-git-auth-gh-cli.sh
.github/act/test-git-auth-methods.sh
.github/act/test-git-auth.sh
.github/act/test-new-workflow.sh
.github/act/test-refactored-workflow.yml
.github/act/test-simplified.sh
.github/act/test-ubuntu-runner.sh
.github/actions/select-runner/action.yml
.github/runners/build-deploy/.gitignore
.github/runners/build-deploy/arch-aware-runner.sh
.github/runners/build-deploy/cleanup-obsolete-files.sh
.github/runners/build-deploy/docker-compose-arm64.yml
.github/runners/build-deploy/generate-repo-token.sh
.github/runners/build-deploy/readme.md
.github/runners/build-deploy/start-arm64-runners.sh
.github/runners/build-deploy/stop-arm64-runners.sh
.github/scripts/README.md
.github/scripts/github-cli-fallback.sh
.github/scripts/parse-deploy.sh
.github/scripts/parse-deploy.test.sh
.github/scripts/test_input.txt
.github/scripts/test_output.txt
.github/workflows/build-deploy-changed-services.yml
.github/workflows/clear-caches.yml
.github/workflows/comment-actions.yml
.github/workflows/pnpm-allow-all-builds.yml
.husky/pre-push
.npmrc
.vscode/settings.json
deploy/cli/README.md
deploy/cli/USAGE.md
deploy/cli/build-deploy.js
deploy/cli/coverage/lcov-report/base.css
deploy/cli/coverage/lcov-report/block-navigation.js
deploy/cli/coverage/lcov-report/build-deploy.js.html
deploy/cli/coverage/lcov-report/deploy.js.html
deploy/cli/coverage/lcov-report/favicon.png
deploy/cli/coverage/lcov-report/index.html
deploy/cli/coverage/lcov-report/parse-args.js.html
deploy/cli/coverage/lcov-report/prettify.css
deploy/cli/coverage/lcov-report/prettify.js
deploy/cli/coverage/lcov-report/run-tests.js.html
deploy/cli/coverage/lcov-report/service-utils.js.html
deploy/cli/coverage/lcov-report/sort-arrow-sprite.png
deploy/cli/coverage/lcov-report/sorter.js
deploy/cli/coverage/lcov.info
deploy/cli/deploy.js
deploy/cli/divinci-deploy.sh
deploy/cli/install.sh
deploy/cli/package-lock.json
deploy/cli/package.json
deploy/cli/parse-args.js
deploy/cli/run-tests.js
deploy/cli/service-utils.js
deploy/cli/test-cli.sh
deploy/cli/tests/README.md
deploy/cli/tests/build-deploy.test.js
deploy/cli/tests/integration.test.js
deploy/cli/tests/mock-data/api-package.json
deploy/cli/tests/mock-data/api-test-mapping.ts
deploy/cli/tests/mock-data/config.json
deploy/cli/tests/mock-data/web-package.json
deploy/cli/tests/parse-args.test.js
deploy/cli/tests/run-tests.test.js
deploy/cli/tests/service-utils.test.js
package.json
pnpm-lock.yaml
scripts/README.md
scripts/build-images.sh
scripts/check-branch-size.js
scripts/create-test-triggers.ts
scripts/generate-mtls-keys.sh
scripts/merge-coverage.js
scripts/optimize-startup.sh
scripts/run-jest-tests.sh
scripts/start-fast.sh
scripts/stop-fast.sh
scripts/trigger-tests.ts
vitest.config.mjs
