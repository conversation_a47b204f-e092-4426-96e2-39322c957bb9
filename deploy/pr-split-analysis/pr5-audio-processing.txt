=== PR 5: Audio Processing & Pyannote ===
audio-transcript-fine-tune-implementation-plan.md
audio-transcript-rag-implementation-plan.md
deploy/docker/ci/ffmpeg-docker-entrypoint.sh
deploy/docker/ci/ffmpeg.ci.Dockerfile
deploy/docker/ci/pyannote.ci.Dockerfile
docs/audio-services-deployment.md
test-pyannote-fixed.js
test-pyannote.js
test-pyannote.sh
test.mp3
workspace/clients/tests/docs/audio-fine-tune-tests.md
workspace/clients/tests/fixtures/test-audio.mp3
workspace/clients/tests/src/e2e/audio-rag-status-check.spec.ts
workspace/clients/tests/src/e2e/audio-rag-status.spec.ts
workspace/clients/tests/src/e2e/audio-to-rag-extended.spec.ts
workspace/clients/tests/src/e2e/audio-transcript-upload.spec.ts
workspace/clients/tests/src/e2e/audio-transcript-zip-upload.spec.ts
workspace/clients/tests/src/e2e/resource-intensive/audio-transcript-to-fine-tune.spec.ts
workspace/clients/tests/src/e2e/utils/audio-transcript-helpers.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/create-audio-transcript.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/index.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/upload-zip-file.ts
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice-faststart.mp4
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice.flac
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice.mp4
workspace/clients/tests/test-files/small-audio-test.zip
workspace/clients/web/src/components/AudioTranscript/ServiceHealthCheck.module.css
workspace/clients/web/src/components/AudioTranscript/ServiceHealthCheck.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/ChunkerTool.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/DiarizeTool.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/FileInput/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/MediaDisplay.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/URLInput/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/URLTextareaInput/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/TranscribeTool.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/diarizeTool.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/transcribeTool.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateFineTuneFile/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateFineTuneFile/form.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/List.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/form.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/ItemDisplay/DisplaySample/Sample.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/ItemDisplay/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/ItemDisplay/styles.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/Outlet.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/RegenerateTranscript/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/Audio-RAG-Errors.md
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/local-mode.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/types.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/Outlet.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/URL/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/URL/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/URL/types.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/List.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/data/AudioTools.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/utils/toolPreferences.ts
workspace/resources/actions/src/user/preferences/audio-tools.ts
workspace/resources/actions/src/workspace/data-source/audio/life-cycle/create.ts
workspace/resources/actions/src/workspace/data-source/audio/life-cycle/polling.ts
workspace/resources/actions/src/workspace/data-source/audio/mock-test-trigger.ts
workspace/resources/server-models/src/money/transcript-value-wallet/statics/transaction/step-transaction.ts
workspace/resources/server-models/src/white-label/data-source/audio-transcript/methods/generateRagFile.ts
workspace/resources/server-tools/src/audio/speaker-diarization/divinci-pyannote/api/processFile/handleWithFetch.ts
workspace/resources/server-tools/src/audio/speaker-diarization/divinci-pyannote/api/processFile/mtls-fetch.ts
workspace/resources/server-tools/src/audio/speaker-diarization/pyannote/processFile/index.ts
workspace/resources/server-tools/src/audio/speaker-diarization/pyannote/processFile/startJob.ts
workspace/resources/server-tools/src/audio/std-utils/convert-to-flac.ts
workspace/resources/server-tools/src/audio/std-utils/convert-to-mp3.ts
workspace/resources/server-tools/src/audio/std-utils/create-slice.ts
workspace/resources/server-tools/src/audio/std-utils/get-duration.ts
workspace/resources/server-tools/src/audio/std-utils/index.ts
workspace/resources/server-tools/src/audio/std-utils/mtls-fetch.ts
workspace/resources/tools/tests/audio-transcript/audio-to-rag-extension.test.ts
workspace/servers/public-api/src/ux/system/pyannote-constants.ts
workspace/servers/public-api/src/ux/user/router/preferences/audio-tools.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/actions.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/from-file/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/from-presigned/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/from-url/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/ensure-valid-flac.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/s3-copy-original.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/transcribe-media.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/validate-media-name.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/mock-status.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/status.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/tools/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/tools/service-health.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/util/r2-constants.ts
workspace/workers/audio-speaker-diarization@pyannote/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/audio-speaker-diarization@pyannote/mtls_utils.py
workspace/workers/audio-speaker-diarization@pyannote/requirements.txt
workspace/workers/audio-speaker-diarization@pyannote/server.py
workspace/workers/audio-speaker-diarization@pyannote/src/process_file.py
workspace/workers/audio-speaker-diarization@pyannote/src/progress_reporter.py
workspace/workers/audio-speaker-diarization@pyannote/src/s3_client.py
workspace/workers/audio-speaker-diarization@pyannote/wsgi.py
workspace/workers/audio-splitter@ffmpeg/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/audio-splitter@ffmpeg/package.json
workspace/workers/audio-splitter@ffmpeg/src/app/convert-to-mp3/index.ts
workspace/workers/audio-splitter@ffmpeg/src/app/index.ts
workspace/workers/audio-splitter@ffmpeg/src/index.ts
workspace/workers/audio-splitter@ffmpeg/src/mtls-server.ts
workspace/workers/audio-splitter@ffmpeg/src/services/redis.ts
workspace/workers/audio-splitter@ffmpeg/src/services/s3.ts
workspace/workers/audio-splitter@ffmpeg/src/utils/child-process.ts
workspace/workers/audio-splitter@ffmpeg/src/utils/env.ts
