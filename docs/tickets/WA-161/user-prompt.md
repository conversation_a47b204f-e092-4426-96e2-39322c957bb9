# User To User Messages in AIChat Transcripts

Can you write up a plan with a todo list of actionable items based on the featues/changes below

## Why
An important implementation for this feature is when a aichat user wants to talk directly with a release owner. For example, if they have back problems and the AI is not giving the answers it wants, they can call for a human and an employee of the company that owns the release/whitelabel can chat with the user on the companies behalf.

Another use is that a user may want to allow other users to talk to them in the chat but not trigger the actual assistant.

## General Description
A user with messaging permission can add a message that replies to another message or is related to a specific assistant
This message should not trigger an assistant to respond, just gets added to the transcript

A user with the right whitelabel permissions can view the threads that use one of releases created by the whitelabel
This is already created
- server - workspace/servers/public-api/src/ux/whitelabel/router/threads
- client - workspace/clients/web/src/pages/WhiteLabel/Threads

A user with the right whitelabel permissions can respond to a user’s message in the user’s thread. This response should not trigger an assistant to respond, just gets added to the transcript


## Hints on what to change
I believe the major changes you will be handling is
- updating the client for AIChat and Whitelabel Threads to give people the option to reply to a specific message with refer to an assistant without triggering an assistant and highlighting a message noting they are user to user
  - workspace/clients/web/src/pages/Chat/ChatItem
  - workspace/clients/embed/src/pages/chat/chat-item
  - workspace/clients/web/src/pages/WhiteLabel/Threads
- add endpoints in the public-api that accept user to user messages
  - workspace/servers/public-api/src/ux/ai-chat
  - workspace/servers/public-api/src/ux/whitelabel/router/threads
- add methods in the server models to validate and accept user to user messages
  - workspace/resources/server-models/src/ai-chat
- also create end to end tests in the clients/tests

