# User-to-User Messages in AIChat Transcripts - TODO List

## Phase 1: Core Infrastructure

### Message Schema and Types
- [x] **Update TranscriptMessageRole Type** - Add `"user-to-user"` to `TranscriptMessageRole` type in `workspace/resources/models/src/transcript/types.ts`
- [x] **Update Message Schema Enum** - Add `"user-to-user"` to role enum in `workspace/resources/server-models/src/transcript/schema/MessageSchema.ts`
- [x] **Create New Input Context Type** - Add `{ type: "userToUser", value: { replyTo: string, targetUserId?: string } }` to `InputMessageContext` in `workspace/resources/server-models/src/ai-chat/Chat/types.ts`

### Server Model Updates
- [x] **Update Chat addMessage Method** - Add handling for `userToUser` context type in `workspace/resources/server-models/src/ai-chat/Chat/methods/add-message.ts`
- [x] **Create addUserToUserMessage Function** - Create new function that bypasses AI assistant processing in `workspace/resources/server-models/src/transcript/methods/add-chat-message/context/add-request.ts`
- [x] **Update Message Processing Logic** - Add bypass logic for user-to-user messages (handled in addMessage method, wrapped.ts not needed for user-to-user)

## Phase 2: API Endpoints

### AI Chat Endpoints
- [x] **Create addUserToUserMessage Endpoint** - Add new endpoint function in `workspace/servers/public-api/src/ux/ai-chat/router/chat-message.ts`
- [x] **Add User-to-User Route** - Add `POST /:chatId/user-message` route with `interactTranscript` permission in `workspace/servers/public-api/src/ux/ai-chat/router/index.ts`

### Whitelabel Endpoints
- [x] **Create Whitelabel User Message Endpoint** - Create `workspace/servers/public-api/src/ux/whitelabel/router/threads/routes/addUserMessage.ts` for admin responses
- [x] **Add Whitelabel User Message Route** - Add `POST /:chatId/user-message` route in `workspace/servers/public-api/src/ux/whitelabel/router/threads/index.ts`

### Permission Validation
- [x] **Validate User-to-User Permissions** - Ensure `interactTranscript` permission covers user-to-user messaging in permission configs
- [x] **Update Whitelabel Permissions** - Verify whitelabel users have appropriate permissions in `workspace/resources/server-permissions/src/permissions/white-label/default-config.ts`

## Phase 3: Client UI

### Web Client - Chat Item Components
- [x] **Add Reply to User Button** - Add "Reply to User" button in `workspace/clients/web/src/pages/Chat/ChatItem/ChatItemIndex/ChatMessage/Footer/index.tsx`
- [x] **Update Message Form** - Add user-to-user message toggle and mode in `workspace/clients/web/src/pages/Chat/ChatItem/ChatItemIndex/MessageForm/index.tsx`
- [ ] **Update Message Header** - Add user-to-user message indicators in `workspace/clients/web/src/pages/Chat/ChatItem/ChatItemIndex/ChatMessage/Header/index.tsx`

### Web Client - Whitelabel Threads
- [ ] **Add Admin Response Form** - Add message form for whitelabel admins in `workspace/clients/web/src/pages/WhiteLabel/Threads/ThreadItem/index.tsx`
- [ ] **Add Respond as Human Button** - Add "Respond as Human" button/mode for whitelabel admins

### Embed Client Updates
- [ ] **Update Embed Chat Item** - Add user-to-user message support in `workspace/clients/embed/src/pages/chat/chat-item/index.tsx`
- [ ] **Update Embed Message Form** - Add user-to-user messaging capability in `workspace/clients/embed/src/components/Transcript/MessageForm.tsx`
- [ ] **Update Embed Message Header** - Add user-to-user indicators in `workspace/clients/embed/src/pages/chat/chat-item/Message/MessageHeader.tsx`

### Message Display Components
- [x] **Add User-to-User Message Styling** - Add special styling and indicators in `workspace/clients/web/src/components/Transcript/views/Chat/Transcript/RenderContent.tsx`
- [ ] **Update Message List CSS** - Add CSS classes and visual distinction in `workspace/clients/web/src/components/Transcript/views/Chat/Transcript/MessageList.tsx`

## Phase 4: API Actions and Resources

### Client API Actions
- [x] **Create addUserToUserMessage Action** - Create `workspace/resources/actions/addUserToUserMessage.ts` for user-to-user API calls
- [x] **Create addWhitelabelUserMessage Action** - Create `workspace/resources/actions/addWhitelabelUserMessage.ts` for whitelabel admin responses
- [x] **Update Message Form Types** - Update `addMessage` function type to include user-to-user parameters in message form components

## Phase 5: Testing

### E2E Tests
- [x] **Create User-to-User Test Directory** - Create `workspace/clients/tests/src/tests/ai-chat/tests/user-to-user/` directory structure
- [x] **Test Regular User-to-User Messaging** - Create `user-to-user-messaging.ts` test for regular chat user-to-user messages
- [x] **Test Whitelabel Admin Responses** - Create `whitelabel-user-responses.ts` test for whitelabel admin responses to users
- [x] **Test Permission Validation** - Create `permission-validation.ts` test for user-to-user messaging permissions
- [x] **Test Message Display** - Create `message-display.ts` test for message rendering and styling

### Unit Tests
- [x] **Create User-to-User API Tests** - Create `workspace/clients/tests/src/tests/ai-chat/actions/user-to-user-message.ts` for API endpoint testing
- [x] **Test Validation and Error Handling** - Add comprehensive validation and error handling tests
- [x] **Update Test Types** - Add user-to-user message types in `workspace/clients/tests/src/tests/ai-chat/types.ts`

### Additional Tests Created
- [x] **Validation and Error Handling Tests** - Created comprehensive validation tests in `validation-error-handling.ts`
- [x] **API Unit Tests** - Created detailed API endpoint tests in `api-unit-tests.ts`
- [x] **Test Helper Utilities** - Created reusable test helpers in `test-helpers.ts`
- [x] **Integration Tests** - Created end-to-end integration tests in `integration-tests.ts`
- [x] **TAP Framework Integration** - Created proper TAP-based tests in `user-to-user-messaging.ts`
- [x] **Basic Validation Tests** - Created service-independent validation tests in `user-to-user-basic-validation.ts`

### Test Integration Status
- [x] **Tests Build Successfully** - All TypeScript compilation passes
- [x] **Tests Added to Test Suite** - User-to-user tests integrated into AI Chat test suite
- ⚠️ **Tests Ready to Run** - Tests are ready but require environment setup (private keys, services)

## Phase 6: WebSocket and Real-time Support

### WebSocket Updates
- [x] **Update AI Chat WebSocket** - Ensure user-to-user messages broadcast via WebSocket (already working via existing Redis publish in addUserToUserMessage)
- [x] **Update Whitelabel WebSocket** - Add WebSocket support for whitelabel user-to-user messages (uses same transcript WebSocket channel)

## Phase 7: Documentation and Polish

### Documentation
- [ ] **Update API Documentation** - Document new user-to-user message endpoints and parameters
- [ ] **Update Permission Documentation** - Document user-to-user messaging permission requirements
- [ ] **Create User Guide** - Create documentation for using user-to-user messaging features

### Final Polish
- [ ] **Performance Optimization** - Optimize user-to-user message processing and rendering
- [ ] **UI/UX Refinement** - Polish user interface and user experience for user-to-user messaging
- [ ] **Cross-browser Testing** - Test user-to-user messaging across different browsers and devices
- [ ] **Accessibility Testing** - Ensure user-to-user messaging features are accessible

## Validation Checklist

### Functional Requirements
- [ ] **No AI Trigger Validation** - Verify user-to-user messages do not trigger AI assistant responses
- [ ] **Permission Enforcement** - Confirm proper permission validation for user-to-user messaging
- [ ] **Visual Distinction** - Ensure clear visual indicators distinguish user-to-user messages from AI messages
- [ ] **Whitelabel Integration** - Verify seamless integration with existing whitelabel thread management
- [ ] **Backward Compatibility** - Confirm existing functionality remains unchanged

### Technical Requirements
- [ ] **Database Schema Validation** - Verify message schema properly handles user-to-user role
- [ ] **API Response Validation** - Confirm API endpoints return appropriate responses
- [ ] **WebSocket Broadcasting** - Verify real-time updates work for user-to-user messages
- [ ] **Error Handling** - Ensure proper error handling for edge cases
- [ ] **Security Validation** - Confirm user-to-user messaging doesn't introduce security vulnerabilities
