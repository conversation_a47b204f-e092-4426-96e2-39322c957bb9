
AS-201-Payment#API

# Pricing Todo

- [x] Add Value to a user's wallet (stripe) (also debugging value)
- [ ] Add Pricing to all tools
  - [x] AI Assistants
    - [ ] Include full pipeline - Moderation, RAG, Input Tokens, Output Tokens
  - [x] Rag Vector
  - [x] Audio Transcription
  - [x] Fine Tune
    - [x] Server
    - [x] Client
- [x] Run Tool Pricing on Nano Wallets
  - Actual Payment
- [x] Handle Tool Configuration
  - [x] Client sends tool id and config when applicable
  - [x] Server recieves tool id and config when applicable
  - [x] Server gets the actual tool function
- [ ] Handle Tool Lifecycle
  - [x] Start tool
  - [x] View Tool's status
  - [ ] View Payment status
  - [x] Cancel Tool
  - Versions
    - [x] basic process usage (start => fail or succceed)
    - [x] Fine tuning (start => check status => cancel, fail or succeed)
    - [x] Multiple step (scrape => chunking => embedding => add to rag)
- [ ] Make sure the client's pricing is equal to the server pricing
  - I think we want to send "version" number along with anything that will cost money
  - If the client's version number doesn't match the server's version number, fail before anything happens
  - If fail, notify user and allow them to refresh the page?
- [x] Allow extra costs besides just the selected tool's costs
- [ ] When automatically multistep (Or any time), should save the failures
  - website crawl => chunk => cleaner => add to rag


# 2024/05/23 - Done
- Single Process
  - Payment Succeed, Process Successful, No Refund
  - Payment Succeed, Process Failure, Payment Refunded
    - Need to make sure that the intital cost is taken out
    - Manually Throw an Error
    - Need to make sure that the intital cost is refunded
  - Payment Failure, No Process
- Multi Process
  - Multiple Process and Successfully Run at once
  - First Process Payment Succeeds, Second gets Payment Fails

- Payment Types
  - With Escrow
  - Without Escrow

- MultiStep


# 2024/05/21
- AI Assistants should use postgres
- workspace/resources/server-models/src/transcript/statics/prepare-moderate/index.ts



# 2024/05/18 - Active

## General
- FineTune Payment
- Finetune CustomAI Payments - the counting may be the same, but the cost per token is different at times
- AI Assistant Payments
  - Use postgres instead of mongodb
    - use process or individual tools?
  - Able to calculate ahead of time
    - Moderation - This can be somewhat predicted
    - Retrieved Rag Chunks - This can't be predicted
    - Input Tokens
    - Escrow Output Tokens, Refund after response
- Validate Tool and Input before usage


- Make sure the chunking works everywhere (chunking fails)
- Make sure people pay

## Transcript Payment
- workspace/resources/server-models/src/ai-chat/Chat/methods/add-message.ts
- workspace/resources/server-models/src/ai-chat/Chat/statics/anonymous-chat/index.ts
- workspace/resources/server-models/src/white-label/qa/run-prompts/methods/run-prompt/wrapped-run-prompt.ts
- workspace/resources/server-models/src/transcript/methods/add-chat-message/wrapped.ts
  - workspace/resources/server-models/src/money/transcript-value-wallet/statics/transaction/wrap-transaction.ts
  - workspace/resources/server-models/src/white-label/white-label/methods/add-message.ts



# 2024/05/11 - Finished
- Fix chunkerTool to be a SelectedTool
  - workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Item/FileItemDisplay/ChunkingForm.tsx
- Make Rag Vector use PublicTool Input
  - Serverside
    - only handle one file at a time
  - Clientside
    - ensure custom config component is used
    - pass custom config to create
    - handle bulk files in parrallel
- Make sure payment is viewable before sending



# Active Task - Old
- update actions and api to use SelectedTool
  - update the api casting
  - update the Server-Tools organizer to use the ToolConfig rather than just the public info
    - We can make the organizer just recieve a function rather than an interface

# 2024/05/10 - Finished
- View Transactions as a chart

# Current Task - Finished
- We have a multistep process
  - might use one or more internal processes that cost money
  - might use one or more selectable tools that cost money
- we want to attempt to apply initial cost and escrow to a 


# Realistic Stuff
- Handle Audio
- Migrate from all old Mongodb Assistant payments to Postgres
