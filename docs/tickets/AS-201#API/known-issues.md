# Critical

- We save the inputs of a fine tune job which may contain PII
  - https://github.com/Divinci-AI/server/pull/552#discussion_r2094700506
  - workspace/resources/server-models/src/white-label/fine-tune/custom-ai/methods/start-finetune/payAndEscrow.ts

- We aren't handling when we don't have access to postgres when a transaction is completed or failed

# Client Pricing
- Audio Transcription - Added Innacurate
- Fine Tune Pricing not displayed before creating one
- Open Parse - workspace/resources/tools/src/shared-config/tools/rag/raw-to-chunks/divinci-openparse/pricing.ts
- Unstructured - workspace/resources/tools/src/shared-config/tools/rag/raw-to-chunks/unstructured/pricing.ts

# Optimization

- For finetune pricing, We're collecting the entire thread into memory
  - https://github.com/Divinci-AI/server/pull/552#discussion_r2094700507
  - workspace/resources/server-models/src/white-label/fine-tune/custom-ai/methods/start-finetune/payAndEscrow.ts


# Known Issues

- Saving Crawls on the UrlInfo locker instead of as their own document
  - workspace/resources/server-models/src/white-label/rag/html/url-info

- Cancels payment if it takes too long
  - workspace/servers/public-api/src/ux/user/router/money/wallet/deposit.ts

- Release Assistant Chats aren't calculated
  - When a chat is using a release, we are only charging for the user's prompt and assistant's response
  - Prompt Moderation is Ignored Completely
  - Parts not precalculated (User doesn't know it costs money but it does)
    - Rag Context
    - Thread and Message Prefixes

- Text Assistants don't have organization prefix - This is done for backwards compatibilty
  - workspace/resources/tools/src/shared-config/tools/assistant/text/openai/gpt-4o-mini/info.ts
  - workspace/resources/tools/src/shared-config/tools/assistant/text/openai/gpt-4o/info.ts

- On RagFile we're saving byteLength 
  - All rag files before this branch don't have the byteLength
  - We should add a migration to add the byteLength to all rag files

- No QA tests in the workspace/clients/tests

- Some Freewrite Tests need money now
  - I've tried to fix this but running the tests just hangs

- Crawl Polling built by Augment
 - workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/HTMLPage/Root/RunScraper/ServerSide/hooks/useCrawlPolling.ts
 - It's not amazing but it works for now

- QA prompt moderation failures are not noted
  - workspace/resources/server-models/src/white-label/qa/run-prompts/methods/run-prompt/postgres-run-prompt.ts
  - If a prompt is moderated, we shold let the client knowwhat happened (This might be on purpose)

- Anonymous chats object keys may be slightly changed depending on environment
```typescript
// Stringifies JSON in a predictable way for signing
export function canonicalJSONStringify(value: any): string {
  // Early exit if value is not an object
  if(typeof value !== 'object') return JSON.stringify(value);
  // Early exit if value is null
  if(value === null) return JSON.stringify(value);

  if (Array.isArray(value)) {
    // Arrays: preserve order of elements
    return `[${value.map(v => canonicalJSONStringify(v)).join(',')}]`;
  }

  // Objects: sort keys
  const keys = Object.keys(value).sort();
  const props = keys.map(key => {
    const k = JSON.stringify(key);
    const v = canonicalJSONStringify(value[key]);
    return `${k}:${v}`;
  }).join(',');
  return `{${props}}`;
}
```


# Done
- Fine tune has different costs from normal assistants
- URL Scrapper doesn't cost money
  - Single page scrapping and multi page crawling have different "inputs" and "outputs"
  - Can make single page scrapping be considered a `limit: 1` crawl

- Client Pricing
  - Fine Tune - Done
  - Chunk File - Done
  - Crawl and Scrape Pages - Done
