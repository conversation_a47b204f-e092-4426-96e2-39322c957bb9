# TODO

- Fix Tests that use payment
- Notify User when a multistep process fails
  - Crawl => Rag => Add to Vector
  - If Rag fails, no one knows
- Fail if version mismatch
  - We may increase or decrease prices, if the client doesn't match the server we probably want to have the client refresh
- Make Custom Assistant Costs run on the server?
- Send Websocket Event to the Client When Money changes

- For HTML => Rag, show "unknown costs" alert

## Assistant Related
### Raw Assistants
- Add Payment Displays for Assistants
### Releases
- Add Tests for Custom Assistants
- Add Payment Displays for Custom Assistants
## Release Loadouts (QA, Whitelabel Test Transcripts)
- Add Tests for Release Loadouts
- Add Payment Displays for Release Loadouts


# Done
- Correct Payment with FineTune Assistants
- Display "approximation" alert for audio


## Scrape Related
- Fix Crawl/Scrape Pages
  > starting a crawl/scrape returns a crawlId
  > need to poll or send event once the crawl is finished
  buffer: bigint,
