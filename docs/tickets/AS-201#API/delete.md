# Files I'd like to Delete

- workspace/resources/actions/src/workspace/rag-vector/sources/files/createBulk.ts
- workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm
- workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Form
- workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/HTMLPage/Form/ChunkerTool/ChunkerTool.tsx
- workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/HTMLPage/Root/RunScraper/DryRun
  - We can just remove the pages from the tabs so it's unaccessible
- workspace/resources/actions/src/workspace/rag-vector/sources/files/createBulk.ts
  - We're running the files in parrallel instead