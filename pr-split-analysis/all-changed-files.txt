.claude/settings.local.json
.env.runner
.env.runner.new
.github/act/.actrc
.github/act/.github/workflows/reusable/reusable
.github/act/README.md
.github/act/deploy-services-act-test.yml
.github/act/events/deploy-comment-event.json
.github/act/events/pull_request_event.json
.github/act/events/workflow-dispatch-event.json
.github/act/minimal-test.yml
.github/act/mock-workflows/build-deploy-changed-services.yml
.github/act/run-refactored-test.sh
.github/act/run-simple-act-test.sh
.github/act/secrets.env
.github/act/test-deploy-command.sh
.github/act/test-event.json
.github/act/test-git-auth-actions.sh
.github/act/test-git-auth-direct-gh.sh
.github/act/test-git-auth-final.sh
.github/act/test-git-auth-gh-cli-setup.sh
.github/act/test-git-auth-gh-cli.sh
.github/act/test-git-auth-methods.sh
.github/act/test-git-auth.sh
.github/act/test-new-workflow.sh
.github/act/test-refactored-workflow.yml
.github/act/test-simplified.sh
.github/act/test-ubuntu-runner.sh
.github/actions/select-runner/action.yml
.github/runners/build-deploy/.gitignore
.github/runners/build-deploy/arch-aware-runner.sh
.github/runners/build-deploy/cleanup-obsolete-files.sh
.github/runners/build-deploy/docker-compose-arm64.yml
.github/runners/build-deploy/generate-repo-token.sh
.github/runners/build-deploy/readme.md
.github/runners/build-deploy/start-arm64-runners.sh
.github/runners/build-deploy/stop-arm64-runners.sh
.github/scripts/README.md
.github/scripts/github-cli-fallback.sh
.github/scripts/parse-deploy.sh
.github/scripts/parse-deploy.test.sh
.github/scripts/test_input.txt
.github/scripts/test_output.txt
.github/workflows/build-deploy-changed-services.yml
.github/workflows/clear-caches.yml
.github/workflows/comment-actions.yml
.github/workflows/pnpm-allow-all-builds.yml
.gitignore
.husky/pre-push
.npmrc
.srl
.vscode/settings.json
Audio-RAG-E2E-TODO.md
Audio-RAG-Fixes-TODO.md
Audio-RAG-Local-Mode-IMPLEMENTATION.md
Audio-RAG-Local-Mode-PLAN.md
CLAUDE.md
ChunkAndSave-Workflow-PLAN.md
ChunkAndSave-Workflow-TODO.md
Create-MinIO-Buckets.md
FAST_STARTUP.md
PR-Split-PLAN.md
PR-Split-README.md
PR-Split-TODOs.md
SUMMARY.md
Type-Fix-TODO.md
analyze-pr-files.sh
audio-transcript-fine-tune-implementation-plan.md
audio-transcript-rag-implementation-plan.md
aws-sig-v4-curl.py
cf_trace_response.json
change-cloudflare-ssl-mode.sh
check-certificate-details.sh
check-cloudflare-ssl-mode.sh
check-cloudflare-status.sh
chunks-vectorized-migration-plan.md
client.crt
client_pkcs8.key
cloudflare-config-verification.sh
cloudflare-options-mtls-guide.md
cloudflare-output/firewall_rules.json
cloudflare-output/zone_info.json
codex.md
copy-r2-assets.js
cors-test.html
create-split-branches.sh
create_test_file.py
current-policy.json
deploy/cli/README.md
deploy/cli/USAGE.md
deploy/cli/build-deploy.js
deploy/cli/coverage/lcov-report/base.css
deploy/cli/coverage/lcov-report/block-navigation.js
deploy/cli/coverage/lcov-report/build-deploy.js.html
deploy/cli/coverage/lcov-report/deploy.js.html
deploy/cli/coverage/lcov-report/favicon.png
deploy/cli/coverage/lcov-report/index.html
deploy/cli/coverage/lcov-report/parse-args.js.html
deploy/cli/coverage/lcov-report/prettify.css
deploy/cli/coverage/lcov-report/prettify.js
deploy/cli/coverage/lcov-report/run-tests.js.html
deploy/cli/coverage/lcov-report/service-utils.js.html
deploy/cli/coverage/lcov-report/sort-arrow-sprite.png
deploy/cli/coverage/lcov-report/sorter.js
deploy/cli/coverage/lcov.info
deploy/cli/deploy.js
deploy/cli/divinci-deploy.sh
deploy/cli/install.sh
deploy/cli/package-lock.json
deploy/cli/package.json
deploy/cli/parse-args.js
deploy/cli/run-tests.js
deploy/cli/service-utils.js
deploy/cli/test-cli.sh
deploy/cli/tests/README.md
deploy/cli/tests/build-deploy.test.js
deploy/cli/tests/integration.test.js
deploy/cli/tests/mock-data/api-package.json
deploy/cli/tests/mock-data/api-test-mapping.ts
deploy/cli/tests/mock-data/config.json
deploy/cli/tests/mock-data/web-package.json
deploy/cli/tests/parse-args.test.js
deploy/cli/tests/run-tests.test.js
deploy/cli/tests/service-utils.test.js
deploy/config.json
deploy/divinci
deploy/docker/ci/api-bundle.ci.Dockerfile
deploy/docker/ci/api-mtls-entrypoint.sh
deploy/docker/ci/client-docker-entrypoint-updated.sh
deploy/docker/ci/client-docker-entrypoint.sh
deploy/docker/ci/client-public.ci.Dockerfile
deploy/docker/ci/ffmpeg-docker-entrypoint.sh
deploy/docker/ci/ffmpeg.ci.Dockerfile
deploy/docker/ci/open-parse.ci.Dockerfile
deploy/docker/ci/pyannote.ci.Dockerfile
deploy/docker/ci/resources.ci.Dockerfile
deploy/docker/ci/service-mtls-entrypoint.sh
deploy/docker/local/api-live.Dockerfile
deploy/docker/local/api-webhook.Dockerfile
deploy/docker/local/api.Dockerfile
deploy/kubernetes/mtls-certificates.yaml
deploy/mTLS-CF-Rule
deploy/scripts/add-ca-to-certificates.sh
deploy/scripts/check-server-cert-secret.sh
deploy/scripts/copy-cert-files.sh
deploy/scripts/create-ca-cert-secret.sh
deploy/scripts/create-client-cert-secrets.sh
deploy/scripts/create-individual-cert-secrets.sh
deploy/scripts/create-mtls-certs-secret.sh
deploy/scripts/create-mtls-secret.sh
deploy/scripts/create-server-cert-secret.sh
deploy/scripts/download-cloudflare-ca-certs.sh
deploy/scripts/extract-gcp-certs.sh
deploy/scripts/extract-mtls-certs.sh
deploy/scripts/fix-cloudflare-error-526.sh
deploy/scripts/generate-mtls-certs.sh
deploy/scripts/install-cloudflare-origin-cert.sh
deploy/scripts/monitor-cert-expiration.sh
deploy/scripts/origin_ca_ecc_root.pem
deploy/scripts/origin_ca_rsa_root.pem
deploy/scripts/private-keys/staging/certs/mtls/origin_ca_ecc_root.pem
deploy/scripts/private-keys/staging/certs/mtls/origin_ca_rsa_root.pem
deploy/scripts/rotate-mtls-certs.sh
deploy/scripts/test-cloudflare-mtls.sh
deploy/scripts/test-local-mtls.sh
deploy/scripts/test-mtls-connection.sh
deploy/scripts/test-mtls-performance.sh
deploy/scripts/tests/.gitignore
deploy/scripts/tests/CORS_ANALYSIS.md
deploy/scripts/tests/README.md
deploy/scripts/tests/analysis/existing-tests-analysis.md
deploy/scripts/tests/analysis/mtls-implementation-plan.md
deploy/scripts/tests/analysis/mtls-implementation-progress.md
deploy/scripts/tests/analysis/mtls-implementation-summary.md
deploy/scripts/tests/certificate-chain-verification.sh
deploy/scripts/tests/certificate-issue-analysis.md
deploy/scripts/tests/certificate-presentation-test.sh
deploy/scripts/tests/cf-access-testing-readme.md
deploy/scripts/tests/cf-advanced-debug.sh
deploy/scripts/tests/cf-cert-test.sh
deploy/scripts/tests/cf-curl-test.sh
deploy/scripts/tests/cf-direct-test.sh
deploy/scripts/tests/cf-e2e-test.js
deploy/scripts/tests/cf-trace-debug.sh
deploy/scripts/tests/cf_trace_response.json
deploy/scripts/tests/check-certificate-mounts.sh
deploy/scripts/tests/check-cloudflare-ssl-mode.sh
deploy/scripts/tests/check-options-errors.sh
deploy/scripts/tests/cloudflare-access-bypass-test.sh
deploy/scripts/tests/cloudflare-access-test.sh
deploy/scripts/tests/cloudflare-api-trace-test.sh
deploy/scripts/tests/cloudflare-credentials.env
deploy/scripts/tests/cloudflare-rules-analyzer.sh
deploy/scripts/tests/cloudflare-rules-fixer.sh
deploy/scripts/tests/cloudflare-test-fix-report.md
deploy/scripts/tests/cloudflare.env.template
deploy/scripts/tests/common-functions.sh
deploy/scripts/tests/cors-fixes-implementation-guide-final.md
deploy/scripts/tests/cors-fixes-implementation-guide-updated.md
deploy/scripts/tests/cors-fixes-implementation-guide.md
deploy/scripts/tests/cors-mtls-test-fixed.sh
deploy/scripts/tests/cors-mtls-test.sh
deploy/scripts/tests/cors-worker.js
deploy/scripts/tests/curl-tls-analysis-wrapper.sh
deploy/scripts/tests/curl-tls-analysis.sh
deploy/scripts/tests/curl_output_1.log
deploy/scripts/tests/docs/TLS-Testing-Framework.md
deploy/scripts/tests/docs/Technical-Specification.md
deploy/scripts/tests/docs/curl-tls-analysis-implementation.md
deploy/scripts/tests/docs/curl-tls-integration.md
deploy/scripts/tests/docs/fallback-mechanism-specification.md
deploy/scripts/tests/fix-certificate-mounts.sh
deploy/scripts/tests/fix-cloudflare-cors-worker.sh
deploy/scripts/tests/fix-cloudflare-options-rule.sh
deploy/scripts/tests/fix-mtls-enable-value.sh
deploy/scripts/tests/gcp-cloud-run-health-check.sh
deploy/scripts/tests/gcp-cloud-run-network-test.sh
deploy/scripts/tests/gcp-export-yaml.sh
deploy/scripts/tests/generate-client-cert-key.sh
deploy/scripts/tests/manual-cloudflare-fixes-detailed.md
deploy/scripts/tests/mtls-comprehensive-test-wrapper.sh
deploy/scripts/tests/mtls-comprehensive-test.sh
deploy/scripts/tests/mtls-connection-test-wrapper.sh
deploy/scripts/tests/mtls-connection-test.sh
deploy/scripts/tests/mtls-cors-fix-implementation-plan.md
deploy/scripts/tests/mtls-cors-implementation-guide.md
deploy/scripts/tests/mtls-diagnostics.sh
deploy/scripts/tests/mtls-framework/README.md
deploy/scripts/tests/mtls-framework/certificate-manager.sh
deploy/scripts/tests/mtls-framework/common.sh
deploy/scripts/tests/mtls-framework/run-tests.sh
deploy/scripts/tests/mtls-testing-guide.md
deploy/scripts/tests/mtls-troubleshooting.md
deploy/scripts/tests/nmap-ssl-scan.sh
deploy/scripts/tests/run-all-cf-tests.sh
deploy/scripts/tests/run-cloudflare-test-fix-cycle.sh
deploy/scripts/tests/run-comprehensive-tests.sh
deploy/scripts/tests/run-mtls-cors-fixes.sh
deploy/scripts/tests/run-network-diagnostics.sh
deploy/scripts/tests/set-cloudflare-ssl-mode.sh
deploy/scripts/tests/sslyze-scan.sh
deploy/scripts/tests/sudo_helper.sh
deploy/scripts/tests/tcpdump-tls-capture.sh
deploy/scripts/tests/test-config.sh
deploy/scripts/tests/test-cors-fixes-trending.sh
deploy/scripts/tests/test-cors-fixes.sh
deploy/scripts/tests/test-redirect.sh
deploy/scripts/tests/tls-testing/README.md
deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh
deploy/scripts/tests/tls-testing/bin/run-tls-tests.sh
deploy/scripts/tests/tls-testing/docs/CONTRIBUTING.md
deploy/scripts/tests/tls-testing/docs/ROADMAP.md
deploy/scripts/tests/tls-testing/docs/curl-tls-analysis-developer-guide.md
deploy/scripts/tests/tls-testing/docs/curl-tls-analysis-user-guide.md
deploy/scripts/tests/tls-testing/docs/quick-start-guide.md
deploy/scripts/tests/tls-testing/lib/curl_tls_parser.sh
deploy/scripts/tests/tls-testing/lib/reporting.sh
deploy/scripts/tests/tls-testing/lib/tls_utils.sh
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/curl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/nmap_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/openssl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/report.json
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/summary.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_curl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_headers.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/curl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/nmap_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/openssl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/report.json
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/summary.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_curl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_headers.txt
deploy/scripts/tests/trace_response.json
deploy/scripts/tests/tshark-tls-analysis.sh
deploy/scripts/tests/verify-cors-fixes.sh
deploy/scripts/update-gcp-cert-secret.sh
deploy/scripts/update-k8s-mtls.sh
deploy/scripts/verify-cert-before-deploy.sh
deploy/scripts/verify-cloudflare-mtls.sh
deploy/service-template.yaml
deploy/steps/2.docker-build-no-buildx.sh
deploy/steps/2.docker-build.sh
deploy/steps/3.deploy-new.sh
deploy/util/env.js
deploy/util/gcp-cloud-run-generate-yaml.sh
deploy/util/minio-cors-config.json
deploy/util/minio-cors-config.sh
deploy/util/minio-entrypoint-updated.sh
deploy/util/minio-entrypoint.sh
deploy/util/minio-entrypoint.sh.updated
deploy/util/service-info.js
deploy/util/setup-minio.sh
docker-connectivity-solution.md
docker/fast-local.yml
docker/local.yml
docker/mtls-example.yml
docker/mtls-local.yml
docker/nginx-mtls.conf
docker/test-api.yml
docker/test-chunks-workflow.yml
docs/audio-services-deployment.md
docs/cloudflare-error-526-fix.md
docs/gcp-cloud-run-config.md
docs/mtls-gcp-secrets.md
docs/mtls-setup.md
fix-cloudflare-cors.sh
fix-cloudflare-options-only-norules.sh
fix-cloudflare-options-only.sh
fix-cloudflare-transform-rules.sh
fix-minio-cors-docker.sh
fix-minio-cors.sh
fix-options-server.js
fixed-server.py
generate-cloudflare-origin-cert.sh
mTLS-PLAN.md
mTLS-README.md
mTLS-SECURITY.md
mTLS-TODO.md
manual-cloudflare-fixes.md
minio_connection_guide.md
minio_connectivity_test.py
mtls-options-fix-readme.md
mtls-options-fix-summary.md
open-parse-policy.json
options_response.txt
package.json
package.json.bak
pnpm-lock.yaml
pr-analysis/pr1-files.txt
pr-analysis/pr2-files.txt
pr-analysis/pr3-files.txt
pr-analysis/pr4-files.txt
pr-analysis/pr5-files.txt
pr-analysis/pr6-files.txt
pr-analysis/remaining-files.txt
private-keys
readme.md
resolve-mtls-options-issue.md
s3_client.py
scripts/README.md
scripts/build-images.sh
scripts/check-branch-size.js
scripts/create-test-triggers.ts
scripts/generate-mtls-keys.sh
scripts/merge-coverage.js
scripts/optimize-startup.sh
scripts/run-jest-tests.sh
scripts/start-fast.sh
scripts/stop-fast.sh
scripts/trigger-tests.ts
server.crt
server_tls_policy.yaml
target_proxy.yaml
test-chunks-workflow.sh
test-chunks-workflow.txt
test-cors-fixes.sh
test-cors-options-updated.sh
test-cors-options.sh
test-cors.js
test-curl.sh
test-file.txt
test-minio-connection.js
test-objectid.js
test-pyannote-fixed.js
test-pyannote.js
test-pyannote.sh
test-redirect.sh
test-results/.last-run.json
test-scripts/test-minio-connectivity.sh
test-scripts/test-s3-operations.js
test-ubuntu-runner.sh
test.mp3
test.txt
test_s3.py
tools/mtls-testing/mtls-testing-README.md
tools/mtls-testing/test-minio-connectivity.sh
tools/mtls-testing/test-mtls-connections.sh
tools/mtls-testing/test_mtls.py
tools/mtls-testing/test_mtls_advanced.py
trust_config.yaml
url-map.yaml
vitest.config.mjs
workspace/clients/auth.json
workspace/clients/auth/admin.json
workspace/clients/auth/owner.json
workspace/clients/auth/user.json
workspace/clients/embed/package.json
workspace/clients/playwright/.auth/user.json
workspace/clients/tests/.gitignore
workspace/clients/tests/.test-data-cache.json
workspace/clients/tests/Migration-Guide.md
workspace/clients/tests/README.md
workspace/clients/tests/Resource-Pool-Examples.md
workspace/clients/tests/StorageState_Resource-Pooling-Architecture-for-Testing.md
workspace/clients/tests/auth.json
workspace/clients/tests/auth/admin.json
workspace/clients/tests/auth/owner.json
workspace/clients/tests/auth/user.json
workspace/clients/tests/docs/audio-fine-tune-tests.md
workspace/clients/tests/env/test-local.env
workspace/clients/tests/env/test-staging.env
workspace/clients/tests/fixtures/test-audio.mp3
workspace/clients/tests/hybrid-TODO.md
workspace/clients/tests/package.json
workspace/clients/tests/playwright.config.ts
workspace/clients/tests/src/api-test-mapping.ts
workspace/clients/tests/src/api/api-client.ts
workspace/clients/tests/src/api/user-group-api.ts
workspace/clients/tests/src/api/vector-api.ts
workspace/clients/tests/src/api/whitelabel-api.ts
workspace/clients/tests/src/auth.setup.ts
workspace/clients/tests/src/auth/api-auth.setup.ts
workspace/clients/tests/src/auth/auth-utils.ts
workspace/clients/tests/src/auth/auth.setup.ts
workspace/clients/tests/src/config/config.ts
workspace/clients/tests/src/config/env.ts
workspace/clients/tests/src/constants/auth0.ts
workspace/clients/tests/src/constants/internal.ts
workspace/clients/tests/src/e2e/README.md
workspace/clients/tests/src/e2e/audio-rag-status-check.spec.ts
workspace/clients/tests/src/e2e/audio-rag-status.spec.ts
workspace/clients/tests/src/e2e/audio-to-rag-extended.spec.ts
workspace/clients/tests/src/e2e/audio-transcript-upload.spec.ts
workspace/clients/tests/src/e2e/audio-transcript-zip-upload.spec.ts
workspace/clients/tests/src/e2e/chunks-workflow-api.spec.ts
workspace/clients/tests/src/e2e/chunks-workflow-real.spec.ts
workspace/clients/tests/src/e2e/create-whitelabel.spec.ts
workspace/clients/tests/src/e2e/login.spec.ts
workspace/clients/tests/src/e2e/rag-vector-create.spec.ts
workspace/clients/tests/src/e2e/rag-vector-upload-file-status.spec.ts
workspace/clients/tests/src/e2e/rag-vector-upload-file.spec.ts
workspace/clients/tests/src/e2e/resource-intensive/README.md
workspace/clients/tests/src/e2e/resource-intensive/audio-transcript-to-fine-tune.spec.ts
workspace/clients/tests/src/e2e/screenshots/.gitignore
workspace/clients/tests/src/e2e/utils/audio-transcript-helpers.ts
workspace/clients/tests/src/e2e/utils/common.ts
workspace/clients/tests/src/e2e/utils/fine-tune-helpers.ts
workspace/clients/tests/src/e2e/utils/test-auth.ts
workspace/clients/tests/src/e2e/utils/test-data-cache.ts
workspace/clients/tests/src/github-test-helper.ts
workspace/clients/tests/src/globals/auth0/client.ts
workspace/clients/tests/src/globals/auth0/index.ts
workspace/clients/tests/src/globals/auth0/mock-auth.ts
workspace/clients/tests/src/globals/auth0/storage-state.ts
workspace/clients/tests/src/globals/auth0/token-cache.ts
workspace/clients/tests/src/globals/auth0/types.ts
workspace/clients/tests/src/index.ts
workspace/clients/tests/src/resources/resource-pool.ts
workspace/clients/tests/src/resources/user-group-pool.ts
workspace/clients/tests/src/resources/vector-pool.ts
workspace/clients/tests/src/resources/whitelabel-pool.ts
workspace/clients/tests/src/story-test-auth.setup.ts
workspace/clients/tests/src/story-test/util/group/mock-user-group-service.ts
workspace/clients/tests/src/story-test/util/group/pooled-actions.ts
workspace/clients/tests/src/story-test/util/group/storage-state-actions.ts
workspace/clients/tests/src/story-test/util/permission/usage/permission-types/group-specific.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/create-audio-transcript.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/index.ts
workspace/clients/tests/src/story-test/workbench/data-sources/audio-transcript/tests/free-write/upload-zip-file.ts
workspace/clients/tests/src/story-test/workbench/rag/tests/free-write/file-lifecycle/index.ts
workspace/clients/tests/src/test-mapping.ts
workspace/clients/tests/src/tests/mock-auth.spec.ts
workspace/clients/tests/src/tests/resource-pool.spec.ts
workspace/clients/tests/src/tests/user-group.example.spec.ts
workspace/clients/tests/src/tests/user-group.migrated.spec.ts
workspace/clients/tests/src/tests/user-group.spec.ts
workspace/clients/tests/src/tests/vector.spec.ts
workspace/clients/tests/src/tests/whitelabel.spec.ts
workspace/clients/tests/src/util/request-throttler.ts
workspace/clients/tests/src/util/resource-pool.ts
workspace/clients/tests/src/util/storage-state-resource-pool.ts
workspace/clients/tests/src/utils/auth.ts
workspace/clients/tests/src/utils/file-upload.ts
workspace/clients/tests/src/utils/screenshot.ts
workspace/clients/tests/src/utils/storage.ts
workspace/clients/tests/src/utils/test-utils.ts
workspace/clients/tests/src/utils/whitelabel.ts
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice-faststart.mp4
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice.flac
workspace/clients/tests/test-files/Dr. Fuhrman - TikTok Nutrition Advice.mp4
workspace/clients/tests/test-files/small-audio-test.zip
workspace/clients/web/.env.local
workspace/clients/web/env/shared.env
workspace/clients/web/env/web-client-local.env
workspace/clients/web/nginx.conf
workspace/clients/web/package.json
workspace/clients/web/src/components/AudioTranscript/ServiceHealthCheck.module.css
workspace/clients/web/src/components/AudioTranscript/ServiceHealthCheck.tsx
workspace/clients/web/src/components/Permission/data/DocPermissionContext.tsx
workspace/clients/web/src/components/Permission/input/ChangeOwner.tsx
workspace/clients/web/src/components/Permission/input/GeneralUser.tsx
workspace/clients/web/src/components/Permission/input/IndividualUser.tsx
workspace/clients/web/src/components/Permission/input/UserGroup.tsx
workspace/clients/web/src/components/ReleaseComponents/Assistant/data/UsableFineTuneModels.tsx
workspace/clients/web/src/components/ReleaseComponents/Assistant/index.tsx
workspace/clients/web/src/components/ReleaseComponents/MessagePrefix/data/MessagePrefix.tsx
workspace/clients/web/src/components/ReleaseComponents/MessagePrefix/index.tsx
workspace/clients/web/src/components/ReleaseComponents/PromptModeration/data/PromptModeration.tsx
workspace/clients/web/src/components/ReleaseComponents/PromptModeration/index.tsx
workspace/clients/web/src/components/ReleaseComponents/RagVector/data/RagVectorIndex.tsx
workspace/clients/web/src/components/ReleaseComponents/RagVector/index.tsx
workspace/clients/web/src/components/ReleaseComponents/ThreadPrefix/index.tsx
workspace/clients/web/src/components/ServiceHealthCheck/HealthCheckTester.tsx
workspace/clients/web/src/components/Tabs/index.tsx
workspace/clients/web/src/components/Tabs/tab-menu.tsx
workspace/clients/web/src/components/Transcript/data/RatingContext.tsx
workspace/clients/web/src/components/Transcript/data/TranscriptContext.tsx
workspace/clients/web/src/components/Transcript/views/Chat/Transcript/MessageList.tsx
workspace/clients/web/src/components/Transcript/views/Chat/Transcript/RenderContent.tsx
workspace/clients/web/src/components/Transcript/views/Chat/Transcript/RenderMermaid.tsx
workspace/clients/web/src/components/Transcript/views/Emoji/context/EmojiPickerComponent.tsx
workspace/clients/web/src/components/Transcript/views/File/Form/Input.tsx
workspace/clients/web/src/components/Transcript/views/File/Form/index.tsx
workspace/clients/web/src/components/data/ReleaseInfoContext.tsx
workspace/clients/web/src/components/data/UserInfoContext.tsx
workspace/clients/web/src/components/displays/BatchStatus.tsx
workspace/clients/web/src/components/displays/Timestamp.tsx
workspace/clients/web/src/components/inputs/CustomSelect/index.tsx
workspace/clients/web/src/components/inputs/ExtractableFileArrayInput/index.tsx
workspace/clients/web/src/contexts/HealthCheckContext.tsx
workspace/clients/web/src/globals/api.ts
workspace/clients/web/src/globals/constants/api.ts
workspace/clients/web/src/globals/local-menu.tsx
workspace/clients/web/src/globals/server-error.tsx
workspace/clients/web/src/index.tsx
workspace/clients/web/src/pages/Chat/ChatIndex/Drawers/left-drawer.tsx
workspace/clients/web/src/pages/Chat/ChatItem/ChatItemIndex/Drawers/left-drawer.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/ChunkerTool.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/DiarizeTool.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/FileInput/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/MediaDisplay.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/URLInput/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/MediaInput/URLTextareaInput/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/TranscribeTool.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/diarizeTool.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Form/transcribeTool.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateFineTuneFile/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateFineTuneFile/form.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/List.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/form.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/CreateRagFile/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/ItemDisplay/DisplaySample/Sample.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/ItemDisplay/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/ItemDisplay/styles.module.css
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/Outlet.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Item/RegenerateTranscript/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/Audio-RAG-Errors.md
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/local-mode.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/types.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/Outlet.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/URL/Form.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/URL/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/URL/types.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/List.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/data/AudioTools.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/utils/toolPreferences.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/FineTune/File-Editor/data/CSVEditorStore/db-operations/save.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Form/ChunkingToolSelect.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Item/FileItemDisplay/DeleteButton.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Item/FileItemDisplay/Finalize.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Item/FileItemDisplay/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/InputFileForm.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/submit-add-file-workflow.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/index.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/Transcript/Drawers/components/PrefixEditor.tsx
workspace/clients/web/src/pages/WhiteLabel/Setup/Transcript/Drawers/left-drawer.tsx
workspace/clients/web/tsconfig.json
workspace/clients/web/typings/trusted-types.d.ts
workspace/clients/web/vitest.setup.ts
workspace/deploy/docker/ci/client-docker-entrypoint.sh
workspace/install-with-pnpm.sh
workspace/playwright/.auth/user.json
workspace/resources/actions/src/user/preferences/audio-tools.ts
workspace/resources/actions/src/workspace/data-source/audio/life-cycle/create.ts
workspace/resources/actions/src/workspace/data-source/audio/life-cycle/polling.ts
workspace/resources/actions/src/workspace/data-source/audio/mock-test-trigger.ts
workspace/resources/models/package.json
workspace/resources/models/src/util/Target.ts
workspace/resources/models/src/white-label/RagVector/file.ts
workspace/resources/models/src/white-label/Tool/index.ts
workspace/resources/mtls/LICENSE.md
workspace/resources/mtls/README.md
workspace/resources/mtls/docs/certificate-generation.md
workspace/resources/mtls/examples/api-client.ts
workspace/resources/mtls/examples/open-parse-client.py
workspace/resources/mtls/examples/open-parse-server.py
workspace/resources/mtls/examples/worker-server.ts
workspace/resources/mtls/package.json
workspace/resources/mtls/src/certificates/index.ts
workspace/resources/mtls/src/certificates/loader.ts
workspace/resources/mtls/src/certificates/paths.ts
workspace/resources/mtls/src/client/agent.ts
workspace/resources/mtls/src/client/fetch.ts
workspace/resources/mtls/src/client/index.ts
workspace/resources/mtls/src/index.ts
workspace/resources/mtls/src/server/index.ts
workspace/resources/mtls/src/server/middleware.ts
workspace/resources/mtls/src/server/setup.ts
workspace/resources/mtls/src/types.ts
workspace/resources/mtls/tsconfig.json
workspace/resources/python-utils/mtls_utils.py
workspace/resources/server-globals/package.json
workspace/resources/server-globals/src/certificates/README.md
workspace/resources/server-globals/src/certificates/paths.ts
workspace/resources/server-globals/src/cloudflare/busboy-file-handler.ts
workspace/resources/server-globals/src/cloudflare/r2.ts
workspace/resources/server-globals/src/cors/domains-from-env.ts
workspace/resources/server-globals/src/cors/index.ts
workspace/resources/server-globals/src/cors/local-mode-cors-fix.ts
workspace/resources/server-globals/src/open-parse/fileblob-to-openparseelements.ts
workspace/resources/server-globals/tests/cors/domains-from-env.test.ts
workspace/resources/server-globals/tests/cors/headers.test.ts
workspace/resources/server-globals/tests/unit/certificates/paths.test.ts
workspace/resources/server-models/package.json
workspace/resources/server-models/src/money/transcript-value-wallet/statics/transaction/step-transaction.ts
workspace/resources/server-models/src/white-label/data-source/audio-transcript/methods/generateRagFile.ts
workspace/resources/server-models/src/white-label/fine-tune/file/statics/addFile/index.ts
workspace/resources/server-models/src/white-label/qa/run-prompts/methods/run-prompt/wrapped-run-prompt.ts
workspace/resources/server-models/src/white-label/rag/file/methods/chunk-and-save/README.md
workspace/resources/server-models/src/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.ts
workspace/resources/server-models/src/white-label/rag/file/methods/chunk-and-save/index.ts
workspace/resources/server-models/src/white-label/rag/file/methods/finalize/textchunk-to-d1chunk.ts
workspace/resources/server-models/src/white-label/rag/file/methods/manage-chunks/lifecycle/addChunk.ts
workspace/resources/server-models/src/white-label/rag/file/methods/r2-pointer.ts
workspace/resources/server-models/src/white-label/rag/file/schema.ts
workspace/resources/server-models/src/white-label/rag/file/statics/addNewFileFromText.ts
workspace/resources/server-models/src/white-label/rag/file/statics/createFileRecord.ts
workspace/resources/server-models/src/white-label/rag/shared/constants.ts
workspace/resources/server-models/src/white-label/rag/shared/r2.ts
workspace/resources/server-models/src/white-label/release/public/methods/finalizeRelease/finalize-release-atomic.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.edge-cases.test.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.test.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/utilities.test.ts
workspace/resources/server-models/tsconfig.ci.json
workspace/resources/server-models/tsconfig.json
workspace/resources/server-permissions/package.json
workspace/resources/server-permissions/tsconfig.ci.json
workspace/resources/server-permissions/tsconfig.json
workspace/resources/server-tools/package.json
workspace/resources/server-tools/src/ai-assistant/generators/image/dall-e-base.ts
workspace/resources/server-tools/src/audio/speaker-diarization/divinci-pyannote/api/processFile/handleWithFetch.ts
workspace/resources/server-tools/src/audio/speaker-diarization/divinci-pyannote/api/processFile/mtls-fetch.ts
workspace/resources/server-tools/src/audio/speaker-diarization/pyannote/processFile/index.ts
workspace/resources/server-tools/src/audio/speaker-diarization/pyannote/processFile/startJob.ts
workspace/resources/server-tools/src/audio/std-utils/convert-to-flac.ts
workspace/resources/server-tools/src/audio/std-utils/convert-to-mp3.ts
workspace/resources/server-tools/src/audio/std-utils/create-slice.ts
workspace/resources/server-tools/src/audio/std-utils/get-duration.ts
workspace/resources/server-tools/src/audio/std-utils/index.ts
workspace/resources/server-tools/src/audio/std-utils/mtls-fetch.ts
workspace/resources/server-tools/src/fine-tune/fine-tuners/OpenAIFineTuneable/add-file.ts
workspace/resources/server-tools/src/rag/index.ts
workspace/resources/server-tools/src/rag/raw-to-chunks/open-parse/r2file-to-chunkstream.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/constants.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/index.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/vector-index.ts
workspace/resources/server-tools/src/rag/workflows/ChunkWorkflowClient.ts
workspace/resources/server-tools/src/rag/workflows/index.ts
workspace/resources/server-tools/tests/rag/vector/cloudflare/vector-index.test.ts
workspace/resources/server-tools/tests/rag/workflows/ChunkWorkflowClient.integration.test.ts
workspace/resources/server-tools/tests/rag/workflows/ChunkWorkflowClient.test.ts
workspace/resources/server-tools/tsconfig.ci.json
workspace/resources/server-tools/tsconfig.json
workspace/resources/server-utils/package.json
workspace/resources/server-utils/src/certificates/README.md
workspace/resources/server-utils/src/certificates/validation.ts
workspace/resources/server-utils/src/env.ts
workspace/resources/server-utils/src/fetch/runFetch.ts
workspace/resources/server-utils/src/http-request/__tests__/middleware.test.ts
workspace/resources/server-utils/src/http-request/index.ts
workspace/resources/server-utils/src/http-request/middleware.ts
workspace/resources/server-utils/src/http-request/mtls-agent.ts
workspace/resources/server-utils/src/ws/reject.ts
workspace/resources/server-utils/tests/unit/certificates/README.md
workspace/resources/server-utils/tests/unit/certificates/cert-chain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-expiration.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-info.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-key-match.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate.test.ts
workspace/resources/server-utils/tests/unit/certificates/validation.test.ts
workspace/resources/tools/tests/audio-transcript/audio-to-rag-extension.test.ts
workspace/resources/utils/package.json
workspace/servers/open-parse/src/file_to_parsed.py
workspace/servers/public-api-live/package.json
workspace/servers/public-api-live/src/middleware/README.md
workspace/servers/public-api-live/src/middleware/cors-debug.ts
workspace/servers/public-api-live/src/middleware/mtls-debug.ts
workspace/servers/public-api-live/src/setup/http/index.ts
workspace/servers/public-api-live/tests/unit/src/setup/database/setupDBs.test.skip.ts
workspace/servers/public-api-live/tsconfig.json
workspace/servers/public-api-webhook/tsconfig.json
workspace/servers/public-api/deploy/docker/ci/api-mtls-entrypoint.sh
workspace/servers/public-api/env/api-local.env
workspace/servers/public-api/package.json
workspace/servers/public-api/src/app.ts
workspace/servers/public-api/src/env.ts
workspace/servers/public-api/src/middleware/local-cors.ts
workspace/servers/public-api/src/routes/ai-chat/mock-test-trigger.ts
workspace/servers/public-api/src/routes/finetune/mock-test-trigger.ts
workspace/servers/public-api/src/routes/message/mock-test-trigger.ts
workspace/servers/public-api/src/routes/moderation/mock-test-trigger.ts
workspace/servers/public-api/src/routes/rag/mock-test-trigger.ts
workspace/servers/public-api/src/routes/thread/mock-test-trigger.ts
workspace/servers/public-api/src/routes/whitelabel/mock-test-trigger.ts
workspace/servers/public-api/src/routes/workspace/mock-test-trigger.ts
workspace/servers/public-api/src/setup/http/index.ts
workspace/servers/public-api/src/setup/server.ts
workspace/servers/public-api/src/util/permission/middleware.ts
workspace/servers/public-api/src/util/permission/types.d.ts
workspace/servers/public-api/src/ux/ai-chat/router/index.ts
workspace/servers/public-api/src/ux/system/health.ts
workspace/servers/public-api/src/ux/system/index.ts
workspace/servers/public-api/src/ux/system/pyannote-constants.ts
workspace/servers/public-api/src/ux/user/router/index.ts
workspace/servers/public-api/src/ux/user/router/preferences/audio-tools.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/actions.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/from-file/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/from-presigned/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/from-url/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/ensure-valid-flac.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/s3-copy-original.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/transcribe-media.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/add-audio/resuable/validate-media-name.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/mock-status.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/lifecycle/status.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/tools/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/tools/service-health.ts
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/util/r2-constants.ts
workspace/servers/public-api/src/ux/whitelabel/router/fine-tuning/csv-editor/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/message-prefix/create-prefix.ts
workspace/servers/public-api/src/ux/whitelabel/router/notifications.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/custom-rag/item/rag-file/pending-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/custom-rag/item/rag-file/success-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/create-file-record.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/get-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/get-record.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/update-status.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/util/ensure-valid-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/add-chunks.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/finalize.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/update-chunk.ts
workspace/servers/public-api/src/ux/whitelabel/router/util.ts
workspace/servers/public-api/tests/unit/mtls/README.md
workspace/servers/public-api/tests/unit/mtls/certificate-loading.test.ts
workspace/servers/public-api/tests/unit/mtls/client-mtls.test.ts
workspace/servers/public-api/tests/unit/mtls/client-verification.test.ts
workspace/servers/public-api/tests/unit/mtls/https-server.test.ts
workspace/servers/public-api/tests/unit/src/ux/user/router/phone-number/subscribeToChatWithPhoneNumber.test.skip.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/add-chunks.test.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/pending-file.test.ts
workspace/servers/public-api/tsconfig.json
workspace/servers/public-api/tsconfig.tests.json
workspace/uninstall.js.improved
workspace/workers/audio-speaker-diarization@pyannote/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/audio-speaker-diarization@pyannote/mtls_utils.py
workspace/workers/audio-speaker-diarization@pyannote/requirements.txt
workspace/workers/audio-speaker-diarization@pyannote/server.py
workspace/workers/audio-speaker-diarization@pyannote/src/process_file.py
workspace/workers/audio-speaker-diarization@pyannote/src/progress_reporter.py
workspace/workers/audio-speaker-diarization@pyannote/src/s3_client.py
workspace/workers/audio-speaker-diarization@pyannote/wsgi.py
workspace/workers/audio-splitter@ffmpeg/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/audio-splitter@ffmpeg/package.json
workspace/workers/audio-splitter@ffmpeg/src/app/convert-to-mp3/index.ts
workspace/workers/audio-splitter@ffmpeg/src/app/index.ts
workspace/workers/audio-splitter@ffmpeg/src/index.ts
workspace/workers/audio-splitter@ffmpeg/src/mtls-server.ts
workspace/workers/audio-splitter@ffmpeg/src/services/redis.ts
workspace/workers/audio-splitter@ffmpeg/src/services/s3.ts
workspace/workers/audio-splitter@ffmpeg/src/utils/child-process.ts
workspace/workers/audio-splitter@ffmpeg/src/utils/env.ts
workspace/workers/chunks-workflow/.npmrc
workspace/workers/chunks-workflow/README.md
workspace/workers/chunks-workflow/chunks-workflow_tail/package.json
workspace/workers/chunks-workflow/chunks-workflow_tail/src/index.js
workspace/workers/chunks-workflow/chunks-workflow_tail/test/index.spec.js
workspace/workers/chunks-workflow/migrations/0005_add_status_column.sql
workspace/workers/chunks-workflow/migrations/0006_add_processor_columns.sql
workspace/workers/chunks-workflow/package-lock.json
workspace/workers/chunks-workflow/package.json
workspace/workers/chunks-workflow/run-clean-install.sh
workspace/workers/chunks-workflow/run-tests.sh
workspace/workers/chunks-workflow/src/constants.ts
workspace/workers/chunks-workflow/src/index.ts
workspace/workers/chunks-workflow/src/processors/openparse.ts
workspace/workers/chunks-workflow/src/processors/types.ts
workspace/workers/chunks-workflow/src/processors/unstructured.ts
workspace/workers/chunks-workflow/src/types.ts
workspace/workers/chunks-workflow/src/utils.ts
workspace/workers/chunks-workflow/src/utils/aws-sig-v4.ts
workspace/workers/chunks-workflow/src/utils/check-file-in-buckets.ts
workspace/workers/chunks-workflow/src/utils/d1-api.ts
workspace/workers/chunks-workflow/src/utils/evaluate-relevance.ts
workspace/workers/chunks-workflow/src/utils/fixed-storage-client.ts
workspace/workers/chunks-workflow/src/utils/retry.ts
workspace/workers/chunks-workflow/src/utils/simple-storage-client.ts
workspace/workers/chunks-workflow/src/utils/storage-client-minio.ts
workspace/workers/chunks-workflow/src/utils/storage-client.ts
workspace/workers/chunks-workflow/src/utils/vectorize-api.ts
workspace/workers/chunks-workflow/src/workflows/chunks-vectorized.ts
workspace/workers/chunks-workflow/src/workflows/chunks-vectorized_old-working.ts
workspace/workers/chunks-workflow/src/workflows/steps/add-chunks-to-file-record.ts
workspace/workers/chunks-workflow/src/workflows/steps/ensure-d1-table.ts
workspace/workers/chunks-workflow/src/workflows/steps/ensure-file-record.ts
workspace/workers/chunks-workflow/src/workflows/steps/filter-chunks.ts
workspace/workers/chunks-workflow/src/workflows/steps/initialize-processing.ts
workspace/workers/chunks-workflow/src/workflows/steps/initialize-workflow.ts
workspace/workers/chunks-workflow/src/workflows/steps/link-file-to-rag.ts
workspace/workers/chunks-workflow/src/workflows/steps/process-batch.ts
workspace/workers/chunks-workflow/src/workflows/steps/store-chunks-in-d1.ts
workspace/workers/chunks-workflow/src/workflows/steps/store-chunks-in-r2.ts
workspace/workers/chunks-workflow/src/workflows/steps/types.ts
workspace/workers/chunks-workflow/src/workflows/steps/update-file-status.ts
workspace/workers/chunks-workflow/src/workflows/steps/upload-to-r2.ts
workspace/workers/chunks-workflow/src/workflows/steps/upsert-workflow-metadata.ts
workspace/workers/chunks-workflow/src/workflows/steps/validate-and-get-r2-file.ts
workspace/workers/chunks-workflow/src/workflows/steps/vectorize-chunks.ts
workspace/workers/chunks-workflow/test-debug.txt
workspace/workers/chunks-workflow/test/README.md
workspace/workers/chunks-workflow/test/__snapshots__/snapshot.test.ts.snap
workspace/workers/chunks-workflow/test/basic-processor-functionality.test.js
workspace/workers/chunks-workflow/test/boundary.test.ts
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/edge-cases.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/edge-cases.test.js
workspace/workers/chunks-workflow/test/boundary/skip-original-tests.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.test.js
workspace/workers/chunks-workflow/test/cloudflare-workers.test.js
workspace/workers/chunks-workflow/test/concurrency.test.ts
workspace/workers/chunks-workflow/test/concurrency/parallel-processing.test.js
workspace/workers/chunks-workflow/test/concurrency/resource-contention.test.js
workspace/workers/chunks-workflow/test/error-recovery/retry-logic.test.ts
workspace/workers/chunks-workflow/test/import.test.js
workspace/workers/chunks-workflow/test/index.test.js
workspace/workers/chunks-workflow/test/index.test.ts
workspace/workers/chunks-workflow/test/integration/end-to-end.test.ts
workspace/workers/chunks-workflow/test/mocks/aws-sig-v4-mock.ts
workspace/workers/chunks-workflow/test/mocks/aws-sig-v4.js
workspace/workers/chunks-workflow/test/mocks/chunks-vectorized.mock.js
workspace/workers/chunks-workflow/test/mocks/cloudflare-workers.js
workspace/workers/chunks-workflow/test/mocks/openparse.mock.js
workspace/workers/chunks-workflow/test/mocks/openparse.mock.ts
workspace/workers/chunks-workflow/test/mocks/unstructured-processor.js
workspace/workers/chunks-workflow/test/parameterized.test.ts
workspace/workers/chunks-workflow/test/parameterized/processor-parameterized.test.js
workspace/workers/chunks-workflow/test/parameterized/workflow-parameterized.test.js
workspace/workers/chunks-workflow/test/performance.test.ts
workspace/workers/chunks-workflow/test/performance/chunking-performance.test.js
workspace/workers/chunks-workflow/test/performance/workflow-performance.test.js
workspace/workers/chunks-workflow/test/processors/openparse.mock.test.ts
workspace/workers/chunks-workflow/test/processors/openparse.smoke.test.ts
workspace/workers/chunks-workflow/test/processors/unstructured.test.js
workspace/workers/chunks-workflow/test/processors/unstructured.test.ts
workspace/workers/chunks-workflow/test/property-based.test.ts
workspace/workers/chunks-workflow/test/property-based/chunking.skip.js
workspace/workers/chunks-workflow/test/property-based/chunking.test.js
workspace/workers/chunks-workflow/test/property-based/chunking.test.skip.js
workspace/workers/chunks-workflow/test/security.test.ts
workspace/workers/chunks-workflow/test/security/security.test.js
workspace/workers/chunks-workflow/test/setup-comprehensive.js
workspace/workers/chunks-workflow/test/setup-new.js
workspace/workers/chunks-workflow/test/setup.js
workspace/workers/chunks-workflow/test/setup.ts
workspace/workers/chunks-workflow/test/skip-original-tests.js
workspace/workers/chunks-workflow/test/snapshot.test.ts
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/processor-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/workflow-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/processor-output.test.js
workspace/workers/chunks-workflow/test/snapshot/workflow-output.test.js
workspace/workers/chunks-workflow/test/types.test.ts
workspace/workers/chunks-workflow/test/utils.test.ts
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.js
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.ts
workspace/workers/chunks-workflow/test/utils/storage-client.test.js
workspace/workers/chunks-workflow/test/utils/storage-client.test.ts
workspace/workers/chunks-workflow/test/utils/vectorize-api.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/add-chunks-to-file-record.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/filter-chunks.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/initialize-workflow.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/link-file-to-rag.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/update-file-status.test.ts
workspace/workers/chunks-workflow/tsconfig.json
workspace/workers/chunks-workflow/tsconfig.test.json
workspace/workers/chunks-workflow/vitest.config.js
workspace/workers/chunks-workflow/vitest.config.ts
workspace/workers/chunks-workflow/vitest.config.ts.bak
workspace/workers/chunks-workflow/vitest.config.ts.new
workspace/workers/chunks-workflow/worker-configuration.d.ts
workspace/workers/chunks-workflow/wrangler.toml
workspace/workers/create-cf-email-destination/create-cf-email-destination_tail/package.json
workspace/workers/create-cf-email-destination/package.json
workspace/workers/d1-doc-elements/d1-doc-elements_tail/package.json
workspace/workers/d1-doc-elements/package-lock.json
workspace/workers/d1-doc-elements/package.json
workspace/workers/d1-doc-elements/src/stream/index.ts
workspace/workers/d1-doc-elements/src/stream/retrieve.ts
workspace/workers/d1-doc-elements/src/stream/upsert.ts
workspace/workers/d1-doc-elements/wrangler.toml
workspace/workers/divinci-send-notification-email-d011/divinci-send-notification-email-d011n_tail/package.json
workspace/workers/divinci-send-notification-email-d011/package.json
workspace/workers/open-parse/README.md
workspace/workers/open-parse/app.py
workspace/workers/open-parse/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/open-parse/docker-entrypoint.sh
workspace/workers/open-parse/docs/ai-chunker.md
workspace/workers/open-parse/docs/api-reference.md
workspace/workers/open-parse/mtls_utils.py
workspace/workers/open-parse/open-parse.Dockerfile
workspace/workers/open-parse/requirements.txt
workspace/workers/open-parse/src/__init__.py
workspace/workers/open-parse/src/config.py
workspace/workers/open-parse/src/file_to_parsed.py
workspace/workers/open-parse/src/json_stream.py
workspace/workers/open-parse/src/s3_client.py
workspace/workers/open-parse/src/text_to_chunks.py
workspace/workers/open-parse/src/utils/allowed_files.py
workspace/workers/open-parse/src/zip_to_parsed.py
workspace/workers/rag-chunker-unstructured/package.json
workspace/workers/rag-chunker-unstructured/wrangler.jsonc
workspace/workers/worker-ai-text-categorization/package.json
workspace/workers/worker-ai-text-categorization/worker-ai-text-categorization_tail/package.json
