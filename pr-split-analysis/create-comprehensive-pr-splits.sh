#!/bin/bash

# Comprehensive PR Split Script - No Overlaps, All Files Covered
# This script creates 8 focused PRs covering all 943 changed files

echo "=== CREATING COMPREHENSIVE PR SPLITS ==="
echo "Total files to split: $(wc -l < all-changed-files.txt)"
echo ""

# Create output directory
mkdir -p final-pr-splits

# Create a copy of all files to track what's been assigned
cp all-changed-files.txt remaining-to-assign.txt

# Function to assign files to a PR and remove them from remaining
assign_to_pr() {
    local pr_num=$1
    local pr_name=$2
    local pattern=$3
    
    echo "=== PR $pr_num: $pr_name ===" | tee deploy/final-pr-splits/pr${pr_num}-${pr_name// /-}.txt
    
    # Find matching files that haven't been assigned yet
    grep -E "$pattern" remaining-to-assign.txt | tee -a deploy/final-pr-splits/pr${pr_num}-${pr_name// /-}.txt
    
    # Remove assigned files from remaining list
    grep -vE "$pattern" remaining-to-assign.txt > temp-remaining.txt
    mv temp-remaining.txt remaining-to-assign.txt
    
    local count=$(grep -v "^===" deploy/final-pr-splits/pr${pr_num}-${pr_name// /-}.txt | wc -l)
    echo "Files assigned to PR$pr_num: $count"
    echo "Remaining files: $(wc -l < remaining-to-assign.txt)"
    echo ""
}

# PR 1: GitHub Actions, CI/CD, and Build Infrastructure
assign_to_pr 1 "GitHub-Actions-CI-CD" "^\.github/|^scripts/|^deploy/cli/|^\.npmrc$|^\.husky/|^package\.json$|^pnpm-lock\.yaml$|^vitest\.config\.|^\.vscode/|^\.claude/"

# PR 2: mTLS Implementation and Security Infrastructure
assign_to_pr 2 "mTLS-Security" "mtls|mTLS|cert|ssl|tls|security|\.crt$|\.key$|\.pem$|trust_config|server_tls|target_proxy|url-map|generate-cloudflare-origin-cert"

# PR 3: CORS and Network Configuration  
assign_to_pr 3 "CORS-Network" "cors|CORS|fix-cloudflare|cloudflare.*options|cloudflare.*transform|test-cors|options_response|fix-options"

# PR 4: Docker and Environment Configuration
assign_to_pr 4 "Docker-Environment" "^docker/|Dockerfile|docker-entrypoint|^\.env|env/.*\.env|minio.*entrypoint|setup-minio|gcp-cloud-run"

# PR 5: Audio Processing and Pyannote Integration
assign_to_pr 5 "Audio-Processing" "audio|pyannote|ffmpeg|speaker-diarization|transcript|AudioTranscript|audio-tools|test-pyannote|\.mp3$|\.flac$|\.mp4$"

# PR 6: RAG, Chunking, and Open-Parse Workflow
assign_to_pr 6 "RAG-Chunking" "rag|RAG|chunk|open-parse|chunks-workflow|vectorize|d1-doc-elements|unstructured|ChunkWorkflow"

# PR 7: Testing Infrastructure and E2E Tests
assign_to_pr 7 "Testing-Infrastructure" "test.*\.spec\.|test.*\.test\.|playwright|\.test\.|test-files/|test-scripts/|jest\.|vitest\.|^workspace/clients/tests/"

# PR 8: Client-Side Web Application Changes
assign_to_pr 8 "Client-Web-App" "^workspace/clients/web/|^workspace/clients/embed/|^workspace/clients/auth"

# PR 9: Server-Side API and Backend Changes (for remaining files)
echo "=== PR 9: Server-Backend-Misc ===" | tee deploy/final-pr-splits/pr9-Server-Backend-Misc.txt
cat remaining-to-assign.txt | tee -a deploy/final-pr-splits/pr9-Server-Backend-Misc.txt
remaining_count=$(wc -l < remaining-to-assign.txt)
echo "Files assigned to PR9: $remaining_count"
echo ""

# Final verification
echo "=== FINAL VERIFICATION ==="
total_assigned=0
for i in {1..9}; do
    file="deploy/final-pr-splits/pr$i-*.txt"
    if ls $file 1> /dev/null 2>&1; then
        count=$(grep -v "^===" $file | wc -l)
        echo "PR$i: $count files"
        total_assigned=$((total_assigned + count))
    fi
done

echo ""
echo "Total files in original: $(wc -l < all-changed-files.txt)"
echo "Total files assigned: $total_assigned"

if [ $total_assigned -eq $(wc -l < all-changed-files.txt) ]; then
    echo "✅ SUCCESS: All files have been assigned to PRs!"
else
    echo "❌ ERROR: File count mismatch!"
fi

# Create summary file
echo "# Comprehensive PR Split Summary" > deploy/final-pr-splits/SUMMARY.md
echo "" >> deploy/final-pr-splits/SUMMARY.md
echo "Total files split: $(wc -l < all-changed-files.txt)" >> deploy/final-pr-splits/SUMMARY.md
echo "Total PRs created: 9" >> deploy/final-pr-splits/SUMMARY.md
echo "" >> deploy/final-pr-splits/SUMMARY.md

for i in {1..9}; do
    file="deploy/final-pr-splits/pr$i-*.txt"
    if ls $file 1> /dev/null 2>&1; then
        count=$(grep -v "^===" $file | wc -l)
        pr_name=$(basename $file .txt | sed 's/pr[0-9]-//' | sed 's/-/ /g')
        echo "- **PR $i**: $pr_name ($count files)" >> deploy/final-pr-splits/SUMMARY.md
    fi
done

echo ""
<<<<<<< HEAD
echo "Summary written to final-pr-splits/SUMMARY.md"
=======
echo "Summary written to deploy/final-pr-splits/SUMMARY.md"
>>>>>>> origin/pr-split-1-github-actions-ci-cd
