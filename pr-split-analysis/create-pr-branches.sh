#!/bin/bash

# <PERSON>ript to create PR branches from the comprehensive split
# This script creates 9 focused branches from the current branch

set -e

echo "=== CREATING PR BRANCHES FROM COMPREHENSIVE SPLIT ==="
echo ""

# Get current branch name
CURRENT_BRANCH=$(git branch --show-current)
echo "Current branch: $CURRENT_BRANCH"

# Ensure we're on the right branch
if [[ "$CURRENT_BRANCH" != "AS-211_AS-176-Workflow-Polish_2-recovered" ]]; then
    echo "❌ ERROR: Expected to be on 'AS-211_AS-176-Workflow-Polish_2-recovered' branch"
    echo "Current branch: $CURRENT_BRANCH"
    exit 1
fi

# Ensure we have the latest develop
echo "Fetching latest develop..."
git fetch origin develop

# Function to create a PR branch
create_pr_branch() {
    local pr_num=$1
    local pr_name=$2
    local file_list=$3
    local description=$4
    
    local branch_name="pr-split-${pr_num}-${pr_name}"
    
    echo ""
    echo "=== Creating PR $pr_num: $pr_name ==="
    echo "Branch: $branch_name"
    echo "Files: $(wc -l < "$file_list")"
    
    # Create new branch from develop
    git checkout -b "$branch_name" origin/develop
    
    # Copy files from current branch
    echo "Copying files from $CURRENT_BRANCH..."
    git checkout "$CURRENT_BRANCH" -- $(cat "$file_list" | grep -v "^===")
    
    # Add and commit
    git add .
    git commit -m "feat: $description

This PR is part of a comprehensive split of the large workflow polish branch.

Files included: $(wc -l < "$file_list")
- $(head -5 "$file_list" | grep -v "^===" | sed 's/^/  /')
$(if [ $(wc -l < "$file_list") -gt 5 ]; then echo "  ... and $(($(wc -l < "$file_list") - 5)) more files"; fi)

Part of: AS-211_AS-176-Workflow-Polish_2 split
Related: #579"
    
    echo "✅ Created branch: $branch_name"
}

# Create all PR branches
create_pr_branch 1 "github-actions-ci-cd" "deploy/final-pr-splits/pr1-GitHub-Actions-CI-CD.txt" "GitHub Actions, CI/CD, and Build Infrastructure improvements"

create_pr_branch 2 "mtls-security" "deploy/final-pr-splits/pr2-mTLS-Security.txt" "mTLS implementation and security infrastructure"

create_pr_branch 3 "cors-network" "deploy/final-pr-splits/pr3-CORS-Network.txt" "CORS and network configuration fixes"

create_pr_branch 4 "docker-environment" "deploy/final-pr-splits/pr4-Docker-Environment.txt" "Docker and environment configuration updates"

create_pr_branch 5 "audio-processing" "deploy/final-pr-splits/pr5-Audio-Processing.txt" "Audio processing and Pyannote integration"

create_pr_branch 6 "rag-chunking" "deploy/final-pr-splits/pr6-RAG-Chunking.txt" "RAG, chunking, and Open-Parse workflow improvements"

create_pr_branch 7 "testing-infrastructure" "deploy/final-pr-splits/pr7-Testing-Infrastructure.txt" "Testing infrastructure and E2E test improvements"

create_pr_branch 8 "client-web-app" "deploy/final-pr-splits/pr8-Client-Web-App.txt" "Client-side web application changes"

create_pr_branch 9 "server-backend-misc" "deploy/final-pr-splits/pr9-Server-Backend-Misc.txt" "Server-side API and backend miscellaneous changes"

# Return to original branch
git checkout "$CURRENT_BRANCH"

echo ""
echo "=== SUMMARY ==="
echo "✅ Successfully created 9 PR branches!"
echo ""
echo "Created branches:"
for i in {1..9}; do
    echo "  pr-split-$i-* ($(git log --oneline pr-split-$i-* | head -1 | cut -d' ' -f2-))"
done

echo ""
echo "Next steps:"
echo "1. Push branches to remote: git push origin pr-split-*"
echo "2. Create PRs on GitHub for each branch"
echo "3. Review and merge in the recommended order:"
echo "   - PR 4: Docker Environment (foundation)"
echo "   - PR 1: GitHub Actions CI/CD (build infrastructure)"
echo "   - PR 2: mTLS Security (security layer)"
echo "   - PR 3: CORS Network (network layer)"
echo "   - PR 6: RAG Chunking (core functionality)"
echo "   - PR 5: Audio Processing (feature layer)"
echo "   - PR 7: Testing Infrastructure (testing)"
echo "   - PR 8: Client Web App (frontend)"
echo "   - PR 9: Server Backend Misc (remaining backend)"

echo ""
echo "To push all branches:"
echo "git push origin pr-split-1-github-actions-ci-cd pr-split-2-mtls-security pr-split-3-cors-network pr-split-4-docker-environment pr-split-5-audio-processing pr-split-6-rag-chunking pr-split-7-testing-infrastructure pr-split-8-client-web-app pr-split-9-server-backend-misc"
