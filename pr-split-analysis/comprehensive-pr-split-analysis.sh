#!/bin/bash

# Comprehensive PR Split Analysis Script
# This script analyzes all 943 changed files and categorizes them into logical PRs

echo "=== COMPREHENSIVE PR SPLIT ANALYSIS ==="
echo "Total files to analyze: $(wc -l < all-changed-files.txt)"
echo ""

# Create output directory
mkdir -p deploy/pr-split-analysis

# PR 1: GitHub Actions, CI/CD, and Build Infrastructure
echo "=== PR 1: GitHub Actions & CI/CD Infrastructure ===" | tee deploy/pr-split-analysis/pr1-github-actions.txt
grep -E "^\.github/|^scripts/|^deploy/cli/|^\.npmrc$|^\.husky/|^package\.json$|^pnpm-lock\.yaml$|^vitest\.config\.|^\.vscode/|^\.claude/" all-changed-files.txt | tee -a deploy/pr-split-analysis/pr1-github-actions.txt
echo "Files in PR1: $(grep -v "^===" deploy/pr-split-analysis/pr1-github-actions.txt | wc -l)"
echo ""

# PR 2: mTLS Implementation and Security Infrastructure  
echo "=== PR 2: mTLS Implementation & Security ===" | tee deploy/pr-split-analysis/pr2-mtls-security.txt
grep -E "mtls|mTLS|cert|ssl|tls|security|cloudflare.*ssl|origin_ca|\.crt$|\.key$|\.pem$|trust_config|server_tls|target_proxy|url-map" all-changed-files.txt | tee -a deploy/pr-split-analysis/pr2-mtls-security.txt
echo "Files in PR2: $(grep -v "^===" deploy/pr-split-analysis/pr2-mtls-security.txt | wc -l)"
echo ""

# PR 3: CORS and Network Configuration
echo "=== PR 3: CORS & Network Configuration ===" | tee deploy/pr-split-analysis/pr3-cors-network.txt
grep -E "cors|CORS|fix-cloudflare|cloudflare.*options|cloudflare.*transform|test-cors|options_response|fix-options" all-changed-files.txt | tee -a deploy/pr-split-analysis/pr3-cors-network.txt
echo "Files in PR3: $(grep -v "^===" deploy/pr-split-analysis/pr3-cors-network.txt | wc -l)"
echo ""

# PR 4: Docker and Environment Configuration
echo "=== PR 4: Docker & Environment Configuration ===" | tee deploy/pr-split-analysis/pr4-docker-env.txt
grep -E "^docker/|Dockerfile|docker-entrypoint|^\.env|env/|minio.*entrypoint|setup-minio|gcp-cloud-run" all-changed-files.txt | tee -a deploy/pr-split-analysis/pr4-docker-env.txt
echo "Files in PR4: $(grep -v "^===" deploy/pr-split-analysis/pr4-docker-env.txt | wc -l)"
echo ""

# PR 5: Audio Processing and Pyannote Integration
echo "=== PR 5: Audio Processing & Pyannote ===" | tee deploy/pr-split-analysis/pr5-audio-processing.txt
grep -E "audio|pyannote|ffmpeg|speaker-diarization|transcript|AudioTranscript|audio-tools|test-pyannote|\.mp3$|\.flac$|\.mp4$" all-changed-files.txt | tee -a deploy/pr-split-analysis/pr5-audio-processing.txt
echo "Files in PR5: $(grep -v "^===" deploy/pr-split-analysis/pr5-audio-processing.txt | wc -l)"
echo ""

# PR 6: RAG, Chunking, and Open-Parse Workflow
echo "=== PR 6: RAG, Chunking & Open-Parse Workflow ===" | tee deploy/pr-split-analysis/pr6-rag-workflow.txt
grep -E "rag|RAG|chunk|open-parse|chunks-workflow|vectorize|d1-doc-elements|unstructured|ChunkWorkflow" all-changed-files.txt | tee -a deploy/pr-split-analysis/pr6-rag-workflow.txt
echo "Files in PR6: $(grep -v "^===" deploy/pr-split-analysis/pr6-rag-workflow.txt | wc -l)"
echo ""

# PR 7: Client-Side Web Application Changes
echo "=== PR 7: Client-Side Web Application ===" | tee deploy/pr-split-analysis/pr7-client-web.txt
grep -E "^workspace/clients/web/|^workspace/clients/embed/|^workspace/clients/auth" all-changed-files.txt | tee -a deploy/pr-split-analysis/pr7-client-web.txt
echo "Files in PR7: $(grep -v "^===" deploy/pr-split-analysis/pr7-client-web.txt | wc -l)"
echo ""

# PR 8: Testing Infrastructure and E2E Tests
echo "=== PR 8: Testing Infrastructure & E2E Tests ===" | tee deploy/pr-split-analysis/pr8-testing.txt
grep -E "^workspace/clients/tests/|test.*\.spec\.|test.*\.test\.|playwright|\.test\.|test-files/|test-scripts/|jest\.|vitest\." all-changed-files.txt | tee -a deploy/pr-split-analysis/pr8-testing.txt
echo "Files in PR8: $(grep -v "^===" deploy/pr-split-analysis/pr8-testing.txt | wc -l)"
echo ""

# Create a combined file of all categorized files
cat deploy/pr-split-analysis/pr*-*.txt | grep -v "^===" | sort > deploy/pr-split-analysis/all-categorized.txt

# Find uncategorized files
echo "=== UNCATEGORIZED FILES ===" | tee deploy/pr-split-analysis/uncategorized.txt
comm -23 <(sort all-changed-files.txt) <(sort deploy/pr-split-analysis/all-categorized.txt) | tee -a deploy/pr-split-analysis/uncategorized.txt
echo "Uncategorized files: $(grep -v "^===" deploy/pr-split-analysis/uncategorized.txt | wc -l)"
echo ""

# Summary
echo "=== SUMMARY ==="
echo "Total files: $(wc -l < all-changed-files.txt)"
echo "Categorized files: $(wc -l < deploy/pr-split-analysis/all-categorized.txt)"
echo "Uncategorized files: $(grep -v "^===" deploy/pr-split-analysis/uncategorized.txt | wc -l)"
echo ""

for i in {1..8}; do
    file="deploy/pr-split-analysis/pr$i-*.txt"
    if ls $file 1> /dev/null 2>&1; then
        count=$(grep -v "^===" $file | wc -l)
        echo "PR$i: $count files"
    fi
done
