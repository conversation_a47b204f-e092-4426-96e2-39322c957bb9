=== PR 6: RAG, Chunking & Open-Parse Workflow ===
Audio-RAG-E2E-TODO.md
Audio-RAG-Fixes-TODO.md
Audio-RAG-Local-Mode-IMPLEMENTATION.md
Audio-RAG-Local-Mode-PLAN.md
audio-transcript-rag-implementation-plan.md
chunks-vectorized-migration-plan.md
deploy/cli/coverage/lcov-report/base.css
deploy/cli/coverage/lcov-report/block-navigation.js
deploy/cli/coverage/lcov-report/build-deploy.js.html
deploy/cli/coverage/lcov-report/deploy.js.html
deploy/cli/coverage/lcov-report/favicon.png
deploy/cli/coverage/lcov-report/index.html
deploy/cli/coverage/lcov-report/parse-args.js.html
deploy/cli/coverage/lcov-report/prettify.css
deploy/cli/coverage/lcov-report/prettify.js
deploy/cli/coverage/lcov-report/run-tests.js.html
deploy/cli/coverage/lcov-report/service-utils.js.html
deploy/cli/coverage/lcov-report/sort-arrow-sprite.png
deploy/cli/coverage/lcov-report/sorter.js
deploy/cli/coverage/lcov.info
deploy/docker/ci/open-parse.ci.Dockerfile
docker/test-chunks-workflow.yml
open-parse-policy.json
scripts/merge-coverage.js
test-chunks-workflow.sh
test-chunks-workflow.txt
workspace/clients/tests/StorageState_Resource-Pooling-Architecture-for-Testing.md
workspace/clients/tests/src/e2e/audio-rag-status-check.spec.ts
workspace/clients/tests/src/e2e/audio-rag-status.spec.ts
workspace/clients/tests/src/e2e/audio-to-rag-extended.spec.ts
workspace/clients/tests/src/e2e/chunks-workflow-api.spec.ts
workspace/clients/tests/src/e2e/chunks-workflow-real.spec.ts
workspace/clients/tests/src/e2e/rag-vector-create.spec.ts
workspace/clients/tests/src/e2e/rag-vector-upload-file-status.spec.ts
workspace/clients/tests/src/e2e/rag-vector-upload-file.spec.ts
workspace/clients/tests/src/globals/auth0/storage-state.ts
workspace/clients/tests/src/story-test/util/group/storage-state-actions.ts
workspace/clients/tests/src/story-test/workbench/rag/tests/free-write/file-lifecycle/index.ts
workspace/clients/tests/src/util/storage-state-resource-pool.ts
workspace/clients/tests/src/utils/storage.ts
workspace/clients/web/src/pages/WhiteLabel/Setup/DataSources/AudioTranscript/Root/Create/File/Audio-RAG-Errors.md
workspace/resources/mtls/examples/open-parse-client.py
workspace/resources/mtls/examples/open-parse-server.py
workspace/resources/server-globals/src/open-parse/fileblob-to-openparseelements.ts
workspace/resources/server-models/src/white-label/rag/file/methods/chunk-and-save/README.md
workspace/resources/server-models/src/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.ts
workspace/resources/server-models/src/white-label/rag/file/methods/chunk-and-save/index.ts
workspace/resources/server-models/src/white-label/rag/file/methods/finalize/textchunk-to-d1chunk.ts
workspace/resources/server-models/src/white-label/rag/file/methods/manage-chunks/lifecycle/addChunk.ts
workspace/resources/server-models/src/white-label/rag/file/methods/r2-pointer.ts
workspace/resources/server-models/src/white-label/rag/file/schema.ts
workspace/resources/server-models/src/white-label/rag/file/statics/addNewFileFromText.ts
workspace/resources/server-models/src/white-label/rag/file/statics/createFileRecord.ts
workspace/resources/server-models/src/white-label/rag/shared/constants.ts
workspace/resources/server-models/src/white-label/rag/shared/r2.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.edge-cases.test.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.test.ts
workspace/resources/server-models/tests/white-label/rag/file/methods/chunk-and-save/utilities.test.ts
workspace/resources/server-tools/src/rag/index.ts
workspace/resources/server-tools/src/rag/raw-to-chunks/open-parse/r2file-to-chunkstream.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/constants.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/index.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/vector-index.ts
workspace/resources/server-tools/src/rag/workflows/ChunkWorkflowClient.ts
workspace/resources/server-tools/src/rag/workflows/index.ts
workspace/resources/server-tools/tests/rag/vector/cloudflare/vector-index.test.ts
workspace/resources/server-tools/tests/rag/workflows/ChunkWorkflowClient.integration.test.ts
workspace/resources/server-tools/tests/rag/workflows/ChunkWorkflowClient.test.ts
workspace/resources/tools/tests/audio-transcript/audio-to-rag-extension.test.ts
workspace/servers/open-parse/src/file_to_parsed.py
workspace/servers/public-api/src/routes/rag/mock-test-trigger.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/custom-rag/item/rag-file/pending-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/custom-rag/item/rag-file/success-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/create-file-record.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/get-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/get-record.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/update-status.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/util/ensure-valid-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/add-chunks.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/finalize.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/update-chunk.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/add-chunks.test.ts
workspace/workers/chunks-workflow/.npmrc
workspace/workers/chunks-workflow/README.md
workspace/workers/chunks-workflow/chunks-workflow_tail/package.json
workspace/workers/chunks-workflow/chunks-workflow_tail/src/index.js
workspace/workers/chunks-workflow/chunks-workflow_tail/test/index.spec.js
workspace/workers/chunks-workflow/migrations/0005_add_status_column.sql
workspace/workers/chunks-workflow/migrations/0006_add_processor_columns.sql
workspace/workers/chunks-workflow/package-lock.json
workspace/workers/chunks-workflow/package.json
workspace/workers/chunks-workflow/run-clean-install.sh
workspace/workers/chunks-workflow/run-tests.sh
workspace/workers/chunks-workflow/src/constants.ts
workspace/workers/chunks-workflow/src/index.ts
workspace/workers/chunks-workflow/src/processors/openparse.ts
workspace/workers/chunks-workflow/src/processors/types.ts
workspace/workers/chunks-workflow/src/processors/unstructured.ts
workspace/workers/chunks-workflow/src/types.ts
workspace/workers/chunks-workflow/src/utils.ts
workspace/workers/chunks-workflow/src/utils/aws-sig-v4.ts
workspace/workers/chunks-workflow/src/utils/check-file-in-buckets.ts
workspace/workers/chunks-workflow/src/utils/d1-api.ts
workspace/workers/chunks-workflow/src/utils/evaluate-relevance.ts
workspace/workers/chunks-workflow/src/utils/fixed-storage-client.ts
workspace/workers/chunks-workflow/src/utils/retry.ts
workspace/workers/chunks-workflow/src/utils/simple-storage-client.ts
workspace/workers/chunks-workflow/src/utils/storage-client-minio.ts
workspace/workers/chunks-workflow/src/utils/storage-client.ts
workspace/workers/chunks-workflow/src/utils/vectorize-api.ts
workspace/workers/chunks-workflow/src/workflows/chunks-vectorized.ts
workspace/workers/chunks-workflow/src/workflows/chunks-vectorized_old-working.ts
workspace/workers/chunks-workflow/src/workflows/steps/add-chunks-to-file-record.ts
workspace/workers/chunks-workflow/src/workflows/steps/ensure-d1-table.ts
workspace/workers/chunks-workflow/src/workflows/steps/ensure-file-record.ts
workspace/workers/chunks-workflow/src/workflows/steps/filter-chunks.ts
workspace/workers/chunks-workflow/src/workflows/steps/initialize-processing.ts
workspace/workers/chunks-workflow/src/workflows/steps/initialize-workflow.ts
workspace/workers/chunks-workflow/src/workflows/steps/link-file-to-rag.ts
workspace/workers/chunks-workflow/src/workflows/steps/process-batch.ts
workspace/workers/chunks-workflow/src/workflows/steps/store-chunks-in-d1.ts
workspace/workers/chunks-workflow/src/workflows/steps/store-chunks-in-r2.ts
workspace/workers/chunks-workflow/src/workflows/steps/types.ts
workspace/workers/chunks-workflow/src/workflows/steps/update-file-status.ts
workspace/workers/chunks-workflow/src/workflows/steps/upload-to-r2.ts
workspace/workers/chunks-workflow/src/workflows/steps/upsert-workflow-metadata.ts
workspace/workers/chunks-workflow/src/workflows/steps/validate-and-get-r2-file.ts
workspace/workers/chunks-workflow/src/workflows/steps/vectorize-chunks.ts
workspace/workers/chunks-workflow/test-debug.txt
workspace/workers/chunks-workflow/test/README.md
workspace/workers/chunks-workflow/test/__snapshots__/snapshot.test.ts.snap
workspace/workers/chunks-workflow/test/basic-processor-functionality.test.js
workspace/workers/chunks-workflow/test/boundary.test.ts
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/edge-cases.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/edge-cases.test.js
workspace/workers/chunks-workflow/test/boundary/skip-original-tests.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.test.js
workspace/workers/chunks-workflow/test/cloudflare-workers.test.js
workspace/workers/chunks-workflow/test/concurrency.test.ts
workspace/workers/chunks-workflow/test/concurrency/parallel-processing.test.js
workspace/workers/chunks-workflow/test/concurrency/resource-contention.test.js
workspace/workers/chunks-workflow/test/error-recovery/retry-logic.test.ts
workspace/workers/chunks-workflow/test/import.test.js
workspace/workers/chunks-workflow/test/index.test.js
workspace/workers/chunks-workflow/test/index.test.ts
workspace/workers/chunks-workflow/test/integration/end-to-end.test.ts
workspace/workers/chunks-workflow/test/mocks/aws-sig-v4-mock.ts
workspace/workers/chunks-workflow/test/mocks/aws-sig-v4.js
workspace/workers/chunks-workflow/test/mocks/chunks-vectorized.mock.js
workspace/workers/chunks-workflow/test/mocks/cloudflare-workers.js
workspace/workers/chunks-workflow/test/mocks/openparse.mock.js
workspace/workers/chunks-workflow/test/mocks/openparse.mock.ts
workspace/workers/chunks-workflow/test/mocks/unstructured-processor.js
workspace/workers/chunks-workflow/test/parameterized.test.ts
workspace/workers/chunks-workflow/test/parameterized/processor-parameterized.test.js
workspace/workers/chunks-workflow/test/parameterized/workflow-parameterized.test.js
workspace/workers/chunks-workflow/test/performance.test.ts
workspace/workers/chunks-workflow/test/performance/chunking-performance.test.js
workspace/workers/chunks-workflow/test/performance/workflow-performance.test.js
workspace/workers/chunks-workflow/test/processors/openparse.mock.test.ts
workspace/workers/chunks-workflow/test/processors/openparse.smoke.test.ts
workspace/workers/chunks-workflow/test/processors/unstructured.test.js
workspace/workers/chunks-workflow/test/processors/unstructured.test.ts
workspace/workers/chunks-workflow/test/property-based.test.ts
workspace/workers/chunks-workflow/test/property-based/chunking.skip.js
workspace/workers/chunks-workflow/test/property-based/chunking.test.js
workspace/workers/chunks-workflow/test/property-based/chunking.test.skip.js
workspace/workers/chunks-workflow/test/security.test.ts
workspace/workers/chunks-workflow/test/security/security.test.js
workspace/workers/chunks-workflow/test/setup-comprehensive.js
workspace/workers/chunks-workflow/test/setup-new.js
workspace/workers/chunks-workflow/test/setup.js
workspace/workers/chunks-workflow/test/setup.ts
workspace/workers/chunks-workflow/test/skip-original-tests.js
workspace/workers/chunks-workflow/test/snapshot.test.ts
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/processor-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/workflow-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/processor-output.test.js
workspace/workers/chunks-workflow/test/snapshot/workflow-output.test.js
workspace/workers/chunks-workflow/test/types.test.ts
workspace/workers/chunks-workflow/test/utils.test.ts
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.js
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.ts
workspace/workers/chunks-workflow/test/utils/storage-client.test.js
workspace/workers/chunks-workflow/test/utils/storage-client.test.ts
workspace/workers/chunks-workflow/test/utils/vectorize-api.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/add-chunks-to-file-record.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/filter-chunks.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/initialize-workflow.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/link-file-to-rag.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/update-file-status.test.ts
workspace/workers/chunks-workflow/tsconfig.json
workspace/workers/chunks-workflow/tsconfig.test.json
workspace/workers/chunks-workflow/vitest.config.js
workspace/workers/chunks-workflow/vitest.config.ts
workspace/workers/chunks-workflow/vitest.config.ts.bak
workspace/workers/chunks-workflow/vitest.config.ts.new
workspace/workers/chunks-workflow/worker-configuration.d.ts
workspace/workers/chunks-workflow/wrangler.toml
workspace/workers/d1-doc-elements/d1-doc-elements_tail/package.json
workspace/workers/d1-doc-elements/package-lock.json
workspace/workers/d1-doc-elements/package.json
workspace/workers/d1-doc-elements/src/stream/index.ts
workspace/workers/d1-doc-elements/src/stream/retrieve.ts
workspace/workers/d1-doc-elements/src/stream/upsert.ts
workspace/workers/d1-doc-elements/wrangler.toml
workspace/workers/open-parse/README.md
workspace/workers/open-parse/app.py
workspace/workers/open-parse/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/open-parse/docker-entrypoint.sh
workspace/workers/open-parse/docs/ai-chunker.md
workspace/workers/open-parse/docs/api-reference.md
workspace/workers/open-parse/mtls_utils.py
workspace/workers/open-parse/open-parse.Dockerfile
workspace/workers/open-parse/requirements.txt
workspace/workers/open-parse/src/__init__.py
workspace/workers/open-parse/src/config.py
workspace/workers/open-parse/src/file_to_parsed.py
workspace/workers/open-parse/src/json_stream.py
workspace/workers/open-parse/src/s3_client.py
workspace/workers/open-parse/src/text_to_chunks.py
workspace/workers/open-parse/src/utils/allowed_files.py
workspace/workers/open-parse/src/zip_to_parsed.py
workspace/workers/rag-chunker-unstructured/package.json
workspace/workers/rag-chunker-unstructured/wrangler.jsonc
