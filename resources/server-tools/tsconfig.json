{"compilerOptions": {"target": "es2022", "module": "nodenext", "moduleResolution": "nodenext", "types": ["node", "vitest/globals"], "lib": ["es2022", "dom"], "sourceMap": true, "outDir": "./dist", "resolveJsonModule": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "removeComments": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true}, "files": ["src/index.ts"], "include": ["./src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}